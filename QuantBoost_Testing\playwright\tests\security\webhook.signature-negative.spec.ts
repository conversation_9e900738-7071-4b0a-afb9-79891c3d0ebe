import { test, expect } from '../../fixtures/webhook-testing.fixture';

/**
 * Negative signature validation test.
 * Sends a webhook payload with an invalid signature header and expects 400.
 * Skips if STRIPE_WEBHOOK_SECRET is not configured because the route only performs
 * real signature verification when not using the test bypass header.
 */

test.describe('🔐 Webhook Security - Signature Validation', () => {
  test('❌ rejects invalid signature', async () => {
    if (!process.env.STRIPE_WEBHOOK_SECRET) {
      test.skip(true, 'STRIPE_WEBHOOK_SECRET not set; cannot validate negative signature path.');
    }

    const baseUrl = process.env.BASE_URL || 'http://localhost:3000';

    // Standard minimal event body
    const fakeEvent = {
      id: `evt_bad_${Date.now()}`,
      object: 'event',
      type: 'customer.updated',
      created: Math.floor(Date.now() / 1000),
      data: { object: { id: 'cus_fake_123', email: '<EMAIL>' } },
      livemode: false,
      pending_webhooks: 0,
      request: { id: null, idempotency_key: null },
      api_version: '2025-03-31.basil'
    };

    const res = await fetch(`${baseUrl}/api/webhooks/stripe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Purposely invalid header value (does not match test bypass header nor real signature)
        'stripe-signature': 'bad-signature'
      },
      body: JSON.stringify(fakeEvent)
    });

    expect(res.status).toBe(400); // Expect signature failure
    const txt = await res.text();
    expect(txt.toLowerCase()).toContain('webhook signature failed');
  });
});
