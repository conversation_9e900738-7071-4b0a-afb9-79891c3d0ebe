### **`main.tf` (Full and Final Production Version)**

```terraform
# ==============================================================================
# Provider & Data Source Configuration
# ==============================================================================
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

provider "azurerm" {
  features {}
}

# Data source to get information about the current Azure client configuration
data "azurerm_client_config" "current" {}

# Random string for ensuring globally unique names for certain resources
resource "random_string" "suffix" {
  length  = 6
  special = false
  upper   = false
}

################################################################################
# SECTION 1: QUANTBOOST API INFRASTRUCTURE
# Contains all resources for running the core QuantBoost back-end API.
################################################################################

# 1.1. Resource Group for the API
resource "azurerm_resource_group" "api" {
  name     = "rg-quantboost-api-${var.environment}"
  location = var.api_location
  tags = {
    environment = var.environment
    purpose     = "API"
  }
}

# 1.2. Key Vault for storing all application secrets
resource "azurerm_key_vault" "main" {
  name                = "kv-quantboost-api-${var.environment}-${random_string.suffix.result}"
  location            = azurerm_resource_group.api.location
  resource_group_name = azurerm_resource_group.api.name
  tenant_id           = data.azurerm_client_config.current.tenant_id
  sku_name            = "standard"

  # Grant the current user/principal permissions to manage secrets
  access_policy {
    tenant_id = data.azurerm_client_config.current.tenant_id
    object_id = data.azurerm_client_config.current.object_id
    secret_permissions = [ "Get", "List", "Set", "Delete", "Purge" ]
  }
}

# 1.3. Container Registry to store our Docker images
resource "azurerm_container_registry" "main" {
  name                = "acrquantboost${random_string.suffix.result}"
  resource_group_name = azurerm_resource_group.api.name
  location            = azurerm_resource_group.api.location
  sku                 = "Basic"
  admin_enabled       = true # Required for Container Apps password-based auth
}

# 1.4. Log Analytics Workspace for centralized logging
resource "azurerm_log_analytics_workspace" "main" {
  name                = "law-quantboost-${var.environment}"
  location            = azurerm_resource_group.api.location
  resource_group_name = azurerm_resource_group.api.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
}

# 1.5. Application Insights for APM and monitoring
resource "azurerm_application_insights" "main" {
  name                = "ai-quantboost-api-${var.environment}"
  location            = azurerm_resource_group.api.location
  resource_group_name = azurerm_resource_group.api.name
  workspace_id        = azurerm_log_analytics_workspace.main.id
  # Assuming a Node.js API based on original file; adjust if necessary
  application_type    = "Node.JS"
}

# 1.6. Container Apps Environment - the hosting boundary for our app
resource "azurerm_container_app_environment" "main" {
  name                       = "cae-quantboost-${var.environment}"
  location                   = azurerm_resource_group.api.location
  resource_group_name        = azurerm_resource_group.api.name
  log_analytics_workspace_id = azurerm_log_analytics_workspace.main.id
}

# 1.7. Key Vault Secrets - populated from pipeline variables
resource "azurerm_key_vault_secret" "supabase_url" {
  name         = "supabase-url"
  value        = var.supabase_url
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "supabase_key" {
  name         = "supabase-anon-key"
  value        = var.supabase_anon_key
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "supabase_service_key" {
  name         = "supabase-service-role-key"
  value        = var.supabase_service_role_key
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "jwt_secret" {
  name         = "jwt-secret"
  value        = var.jwt_secret
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "stripe_secret_key" {
  name         = "stripe-secret-key"
  value        = var.stripe_secret_key
  key_vault_id = azurerm_key_vault.main.id
}

resource "azurerm_key_vault_secret" "stripe_webhook_secret" {
  name         = "stripe-webhook-signing-secret"
  value        = var.stripe_webhook_signing_secret
  key_vault_id = azurerm_key_vault.main.id
}

# 1.8. The QuantBoost API Container App itself
resource "azurerm_container_app" "api" {
  name                         = "ca-quantboost-api-${var.environment}"
  container_app_environment_id = azurerm_container_app_environment.main.id
  resource_group_name          = azurerm_resource_group.api.name
  revision_mode                = "Single"
  
  identity {
    type = "SystemAssigned"
  }

  registry {
    server               = azurerm_container_registry.main.login_server
    username             = azurerm_container_registry.main.admin_username
    password_secret_name = "acr-password"
  }

  secret { name = "acr-password", value = azurerm_container_registry.main.admin_password }
  secret { name = "supabase-url", value = var.supabase_url }
  secret { name = "supabase-anon-key", value = var.supabase_anon_key }
  secret { name = "supabase-service-role-key", value = var.supabase_service_role_key }
  secret { name = "jwt-secret", value = var.jwt_secret }
  secret { name = "stripe-secret-key", value = var.stripe_secret_key }
  secret { name = "stripe-webhook-signing-secret", value = var.stripe_webhook_signing_secret }
  
  template {
    container {
      name   = "quantboost-api"
      image  = "${azurerm_container_registry.main.login_server}/quantboost-api:latest"
      cpu    = 0.5
      memory = "1Gi"

      env { name = "NODE_ENV", value = "production" }
      env { name = "PORT", value = "3000" }
      env { name = "SUPABASE_URL", secret_name = "supabase-url" }
      env { name = "SUPABASE_ANON_KEY", secret_name = "supabase-anon-key" }
      env { name = "SUPABASE_SERVICE_ROLE_KEY", secret_name = "supabase-service-role-key" }
      env { name = "JWT_SECRET", secret_name = "jwt-secret" }
      env { name = "STRIPE_SECRET_KEY", secret_name = "stripe-secret-key" }
      env { name = "STRIPE_WEBHOOK_SIGNING_SECRET", secret_name = "stripe-webhook-signing-secret" }
      env { name = "APPLICATIONINSIGHTS_CONNECTION_STRING", value = azurerm_application_insights.main.connection_string }
    }

    min_replicas = 1
    max_replicas = 3

    http_scale_rule {
      name                = "http-concurrent-requests"
      concurrent_requests = 50
    }
  }
 
  ingress {
    external_enabled = true
    target_port      = 3000
    
    traffic_weight {
      percentage      = 100
      latest_revision = true
    }
  }
}

################################################################################
# SECTION 2: INSTALLER DISTRIBUTION INFRASTRUCTURE
# Contains resources for hosting the installer file (.exe) globally.
################################################################################

# 2.1. New Resource Group for distribution assets
resource "azurerm_resource_group" "distribution" {
  name     = "rg-quantboost-distribution-${var.environment}"
  location = var.distribution_location
  tags = {
    environment = var.environment
    purpose     = "Distribution"
  }
}

# 2.2. Storage Account to host the QuantBoost.exe installer
resource "azurerm_storage_account" "distribution" {
  name                     = "stqboostdist${random_string.suffix.result}"
  resource_group_name      = azurerm_resource_group.distribution.name
  location                 = azurerm_resource_group.distribution.location
  account_tier             = "Standard"
  account_replication_type = "GRS"
}

# 2.3. Storage Container for the installer files
resource "azurerm_storage_container" "releases" {
  name                  = "releases"
  storage_account_name  = azurerm_storage_account.distribution.name
  container_access_type = "blob"
}

# 2.4. CDN Profile for global content delivery
resource "azurerm_cdn_profile" "main" {
  name                = "cdn-quantboost-${var.environment}"
  resource_group_name = azurerm_resource_group.distribution.name
  location            = azurerm_resource_group.distribution.location
  sku                 = "Standard_Microsoft"
}

# 2.5. CDN Endpoint linked to the storage account
resource "azurerm_cdn_endpoint" "main" {
  name                = "quantboost-downloads-${random_string.suffix.result}"
  profile_name        = azurerm_cdn_profile.main.name
  resource_group_name = azurerm_resource_group.distribution.name
  location            = azurerm_resource_group.distribution.location

  origin {
    name      = "storage-origin"
    host_name = azurerm_storage_account.distribution.primary_blob_host
  }
}

################################################################################
# SECTION 3: AZURE CODE SIGNING INFRASTRUCTURE
# Contains resources for the managed code signing service.
################################################################################

# 3.1. Code Signing Account - the top-level service container
resource "azurerm_codesigning_account" "main" {
  name                = "csa-quantboost-${var.environment}"
  resource_group_name = azurerm_resource_group.distribution.name
  location            = azurerm_resource_group.distribution.location
  sku_name            = "Basic"
}

# 3.2. Certificate Profile for signing our public installer
resource "azurerm_codesigning_certificate_profile" "public_trust" {
  name                    = "csp-${var.environment}-publictrust"
  codesigning_account_id  = azurerm_codesigning_account.main.id
  profile_type            = "PublicTrust"
  
  certificate_extended_key_usage {
    oid = ["*******.*******.3"]
  }
  
  depends_on = [azurerm_codesigning_account.main]
}

################################################################################
# SECTION 4: OUTPUTS
# Provides useful information about the deployed infrastructure.
################################################################################

# --- Outputs for API Infrastructure ---
output "api_resource_group_name" {
  description = "The name of the API Resource Group."
  value       = azurerm_resource_group.api.name
}

output "container_registry_name" {
  description = "Container Registry name."
  value       = azurerm_container_registry.main.name
}

output "container_registry_server" {
  description = "Container Registry server URL."
  value       = azurerm_container_registry.main.login_server
}

output "container_app_fqdn" {
  description = "The fully qualified domain name of the API Container App."
  value       = azurerm_container_app.api.latest_revision_fqdn
}

output "key_vault_name" {
  description = "The name of the Key Vault for storing secrets."
  value       = azurerm_key_vault.main.name
}

output "application_insights_connection_string" {
  description = "The connection string for Application Insights."
  value       = azurerm_application_insights.main.connection_string
  sensitive   = true
}

output "acr_username" {
  description = "Container Registry admin username."
  value       = azurerm_container_registry.main.admin_username
  sensitive   = true
}

output "acr_password" {
  description = "Container Registry admin password."
  value       = azurerm_container_registry.main.admin_password
  sensitive   = true
}

# --- Outputs for Distribution & Signing Infrastructure ---
output "distribution_resource_group_name" {
  description = "The name of the Distribution Resource Group."
  value       = azurerm_resource_group.distribution.name
}

output "distribution_cdn_url_default" {
  description = "The default Azure CDN URL for the installer."
  value       = "https://${azurerm_cdn_endpoint.main.hostname}/QuantBoost.exe"
}

output "distribution_cdn_url_custom" {
  description = "The final, custom domain URL to distribute. Note: Requires manual CNAME setup and can take hours for HTTPS to provision."
  value       = "https://${var.custom_domain_host_name}/QuantBoost.exe"
}

output "upload_instructions" {
  description = "Instructions for uploading the installer file."
  value       = "Upload QuantBoost.exe to Storage Account '${azurerm_storage_account.distribution.name}' into the container '${azurerm_storage_container.releases.name}'."
}

output "dns_instructions" {
  description = "CNAME record required for the custom domain."
  value       = "Create a CNAME record for '${var.custom_domain_host_name}' pointing to '${azurerm_cdn_endpoint.main.hostname}'."
}

output "codesigning_account_name" {
  description = "The name of the Azure Code Signing account to be used in the CI/CD pipeline."
  value       = azurerm_codesigning_account.main.name
}

output "codesigning_certificate_profile_name" {
  description = "The name of the certificate profile to be used for signing the installer."
  value       = azurerm_codesigning_certificate_profile.public_trust.name
}

```