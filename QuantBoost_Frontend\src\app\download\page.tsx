"use client";

import React, { useRef } from 'react';
import { motion, useScroll, useTransform, useSpring, Variants } from 'motion/react';
import Header from '@/components/Header';
import AnimatedQuantBoostLogo from '@/components/AnimatedQuantBoostLogo';

// Professional animation variants
const fadeInUp: Variants = {
  hidden: { opacity: 0, y: 40 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const fadeInScale: Variants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

// Ultra-fast download button animation
const downloadButtonVariants: Variants = {
  idle: { 
    scale: 1, 
    y: 0,
    boxShadow: "0 4px 14px 0 rgba(16, 185, 129, 0.3)",
    background: "linear-gradient(135deg, #10B981, #059669)"
  },
  hover: { 
    scale: 1.05,
    y: -3,
    boxShadow: "0 20px 40px -5px rgba(16, 185, 129, 0.5)",
    background: "linear-gradient(135deg, #059669, #047857)",
    transition: { 
      duration: 0.08,
      type: "spring",
      stiffness: 800,
      damping: 15
    }
  },
  tap: {
    scale: 0.98,
    transition: { duration: 0.1 }
  }
};

const systemRequirementVariants: Variants = {
  hidden: { opacity: 0, x: -20 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: 0.6, ease: "easeOut" }
  }
};

export default function DownloadPage() {
  const mainRef = useRef<HTMLElement>(null);
  const heroRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  const { scrollYProgress: globalScrollProgress } = useScroll({
    target: mainRef,
    offset: ["start start", "end end"]
  });

  // Parallax transforms
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const smoothBackgroundY = useSpring(backgroundY, { stiffness: 100, damping: 30, restDelta: 0.001 });

  // Excel grid animation
  const gridOpacity = useTransform(globalScrollProgress, [0, 0.3, 0.7, 1], [0.02, 0.06, 0.1, 0.15]);
  const gridScale = useTransform(globalScrollProgress, [0, 1], [1, 1.02]);
  const smoothGridOpacity = useSpring(gridOpacity, { stiffness: 50, damping: 25 });

  return (
    <>
      <Header />
      
      <main ref={mainRef} className="flex flex-col items-center justify-center min-h-screen overflow-hidden relative">
        {/* Excel Grid Background - Professional Brand Feel */}
        <motion.div 
          className="fixed inset-0 -z-20 overflow-hidden"
          style={{ 
            opacity: smoothGridOpacity,
            scale: gridScale
          }}
        >
          <svg
            className="absolute inset-0 w-full h-full"
            xmlns="http://www.w3.org/2000/svg"
            style={{ 
              width: '100%', 
              height: '100%',
              minHeight: '100vh'
            }}
          >
            <defs>
              <pattern
                id="excel-grid-download"
                x="0"
                y="0"
                width="40"
                height="24"
                patternUnits="userSpaceOnUse"
              >
                <rect
                  x="0"
                  y="0"
                  width="40"
                  height="24"
                  fill="none"
                  stroke="#10B981"
                  strokeWidth="0.5"
                  opacity="0.4"
                />
                <rect
                  x="0"
                  y="0"
                  width="200"
                  height="120"
                  fill="none"
                  stroke="#10B981"
                  strokeWidth="1"
                  opacity="0.6"
                  patternUnits="userSpaceOnUse"
                />
              </pattern>
            </defs>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#excel-grid-download)"
            />
          </svg>
        </motion.div>

        {/* Hero Section */}
        <motion.section 
          ref={heroRef}
          className="text-center py-24 px-6 relative z-10 max-w-4xl mx-auto"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={staggerContainer}
        >
          {/* Parallax Background Blobs */}
          <motion.div
            className="absolute inset-0 -z-10 opacity-10"
            style={{ y: smoothBackgroundY }}
          >
            <div className="absolute top-20 left-10 w-72 h-72 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div className="absolute top-40 right-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div className="absolute bottom-20 left-20 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
          </motion.div>

          {/* Logo and Branding */}
          <motion.div 
            className="mb-8"
            variants={fadeInScale}
          >
            <AnimatedQuantBoostLogo 
              size="6rem"
              className="mx-auto mb-6"
              showDrawAnimation={false}
            />
          </motion.div>

          {/* Main Heading */}
          <motion.h1 
            className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 relative z-20"
            variants={fadeInUp}
          >
            Download QuantBoost
          </motion.h1>

          <motion.p 
            className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed relative z-20"
            variants={fadeInUp}
          >
            Transform your Excel and PowerPoint workflow with professional-grade productivity tools designed for analysts and consultants.
          </motion.p>

          {/* Download Button - Premium Animation */}
          <motion.div
            className="mb-12 relative z-20"
            variants={fadeInUp}
          >
            <motion.a 
              href="/QuantBoostInstaller.exe"
              className="inline-flex items-center gap-3 px-8 py-4 text-lg font-semibold text-white rounded-xl transition-all duration-200 cursor-pointer relative z-30"
              variants={downloadButtonVariants}
              initial="idle"
              whileHover="hover"
              whileTap="tap"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download QuantBoost
              <span className="text-sm opacity-90">(Windows)</span>
            </motion.a>
          </motion.div>

          {/* System Requirements Card */}
          <motion.div
            className="bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl p-8 max-w-2xl mx-auto shadow-lg relative z-20"
            variants={fadeInScale}
          >
            <motion.h2 
              className="text-2xl font-bold mb-6 text-gray-800 relative z-20"
              variants={fadeInUp}
            >
              System Requirements
            </motion.h2>

            <div className="grid md:grid-cols-2 gap-6 text-left">
              <motion.div variants={systemRequirementVariants}>
                <h3 className="font-semibold text-emerald-700 mb-3 flex items-center gap-2">
                  <span className="text-xl">🖥️</span>
                  Operating System
                </h3>
                <ul className="text-gray-600 space-y-1 text-sm">
                  <li>• Windows 10 or later</li>
                  <li>• 64-bit architecture recommended</li>
                </ul>
              </motion.div>

              <motion.div variants={systemRequirementVariants}>
                <h3 className="font-semibold text-emerald-700 mb-3 flex items-center gap-2">
                  <span className="text-xl">📊</span>
                  Microsoft Office
                </h3>
                <ul className="text-gray-600 space-y-1 text-sm">
                  <li>• Excel 2016 or later</li>
                  <li>• PowerPoint 2016 or later</li>
                  <li>• Office 365 fully supported</li>
                </ul>
              </motion.div>

              <motion.div variants={systemRequirementVariants}>
                <h3 className="font-semibold text-emerald-700 mb-3 flex items-center gap-2">
                  <span className="text-xl">⚡</span>
                  Performance
                </h3>
                <ul className="text-gray-600 space-y-1 text-sm">
                  <li>• 4GB RAM minimum</li>
                  <li>• 50MB available storage</li>
                  <li>• Internet connection for activation</li>
                </ul>
              </motion.div>

              <motion.div variants={systemRequirementVariants}>
                <h3 className="font-semibold text-emerald-700 mb-3 flex items-center gap-2">
                  <span className="text-xl">🔧</span>
                  Installation
                </h3>
                <ul className="text-gray-600 space-y-1 text-sm">
                  <li>• Administrator privileges required</li>
                  <li>• VSTO runtime (auto-installed)</li>
                  <li>• .NET Framework 4.8.1</li>
                </ul>
              </motion.div>
            </div>
          </motion.div>

          {/* Support Links */}
          <motion.div 
            className="mt-12 flex flex-wrap justify-center gap-6 text-sm"
            variants={fadeInUp}
          >
            <a href="/help" className="text-emerald-600 hover:text-emerald-700 underline underline-offset-2">
              Installation Guide
            </a>
            <a href="/help" className="text-emerald-600 hover:text-emerald-700 underline underline-offset-2">
              Troubleshooting
            </a>
            <a href="/training" className="text-emerald-600 hover:text-emerald-700 underline underline-offset-2">
              Getting Started Tutorial
            </a>
          </motion.div>
        </motion.section>
      </main>
    </>
  );
}
