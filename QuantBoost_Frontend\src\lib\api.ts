import { createBrowserClient } from '@supabase/ssr';
import { authJson } from './authFetch';
import type { SupabaseClient } from '@supabase/supabase-js';

// Types for API responses
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface Subscription {
  id: string;
  user_id: string;
  status: string;
  plan_id: string;
  quantity: number;
  current_period_end: string;
  stripe_subscription_id: string;
  cancel_at_period_end?: boolean;
  created_at: string;
  updated_at: string;
}

export interface License {
  id: string;
  subscription_id: string;
  product_id: string;
  status: 'active' | 'inactive' | 'canceled' | 'trial_active' | 'expired' | 'graceperiod' | 'unassigned' | 'assigned' | 'revoked' | 'pending';
  email?: string;
  user_id?: string;
  license_key: string;
  license_tier: string;
  expiry_date?: string;
  assigned_at?: string;
  activated_at?: string;
  max_activations: number;
  activation_count: number;
  is_activated: boolean;
  available_activations: number;
}

export interface TeamLicenseAssignment {
  license_id_to_assign: string;
  target_user_email: string;
}

export interface TeamInvitation {
  target_email: string;
}

// Global Supabase client instance - this will be shared across the app
let globalSupabaseClient: SupabaseClient | null = null;

function getGlobalSupabaseClient(): SupabaseClient {
  if (!globalSupabaseClient) {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      // During build time or SSR, create a placeholder client
      globalSupabaseClient = createBrowserClient('https://placeholder.supabase.co', 'placeholder-key');
      return globalSupabaseClient;
    }

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase environment variables');
      globalSupabaseClient = createBrowserClient('https://placeholder.supabase.co', 'placeholder-key');
      return globalSupabaseClient;
    }

    globalSupabaseClient = createBrowserClient(supabaseUrl, supabaseAnonKey);
  }
  
  return globalSupabaseClient;
}

class ApiClient {
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_AZURE_API_URL || '';
    if (!this.baseURL && typeof window !== 'undefined') {
      // Only warn in browser environment, not during build
      console.warn('NEXT_PUBLIC_AZURE_API_URL is not configured');
    }
  }

  private getSupabase(): SupabaseClient {
    return getGlobalSupabaseClient();
  }

  private async getAuthToken(): Promise<string | null> {
    try {
      const supabase = this.getSupabase();
      console.log('🔍 API Client: Attempting to get auth token...');
      
      // Add retry logic for newly set sessions
      let retries = 3;
      while (retries > 0) {
        const { data: { session } } = await supabase.auth.getSession();
        console.log(`🔍 API Client: Session check attempt ${4 - retries}, session exists:`, !!session, 'has token:', !!session?.access_token);
        if (session?.access_token) {
          console.log('✅ API Client: Successfully retrieved auth token');
          return session.access_token;
        }
        retries--;
        if (retries > 0) {
          console.log(`⏳ API Client: Retrying in 300ms... (${retries} attempts left)`);
          await new Promise(resolve => setTimeout(resolve, 300));
        }
      }
      console.log('❌ API Client: No auth token available after all retries');
      return null;
    } catch (error) {
      console.error('Failed to get auth token:', error);
      return null;
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        throw new Error('Authentication token not available');
      }



      const url = `${this.baseURL}${endpoint}`;
      const response = await fetch(url, {
        ...options,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        success: true,
        data: data.data || data,
        message: data.message,
      };
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Subscription Management
  async getUserSubscriptions(): Promise<ApiResponse<Subscription[]>> {
    return this.makeRequest<Subscription[]>('/v1/me/subscriptions');
  }

  // User License Management
  async getUserLicenses(): Promise<ApiResponse<License[]>> {
    return this.makeRequest<License[]>('/v1/me/licenses');
  }

  // Team License Management
  async getTeamLicenses(subscriptionId: string): Promise<ApiResponse<License[]>> {
    return this.makeRequest<License[]>(`/v1/me/subscriptions/${subscriptionId}/team-licenses`);
  }

  async assignTeamLicense(
    subscriptionId: string,
    assignment: TeamLicenseAssignment
  ): Promise<ApiResponse<License>> {
    return this.makeRequest<License>(`/v1/me/subscriptions/${subscriptionId}/team-licenses/assign`, {
      method: 'POST',
      body: JSON.stringify(assignment),
    });
  }

  async inviteTeamMember(
    subscriptionId: string,
    invitation: TeamInvitation
  ): Promise<ApiResponse<License>> {
    return this.makeRequest<License>(`/v1/me/subscriptions/${subscriptionId}/team-licenses/invite`, {
      method: 'POST',
      body: JSON.stringify(invitation),
    });
  }

  async unassignTeamLicense(
    subscriptionId: string,
    licenseId: string
  ): Promise<ApiResponse<License>> {
    return this.makeRequest<License>(`/v1/me/subscriptions/${subscriptionId}/team-licenses/unassign`, {
      method: 'POST',
      body: JSON.stringify({ license_id_to_unassign: licenseId }),
    });
  }

  // Billing Portal Integration - Use frontend API route
  async createBillingPortalSession(returnUrl: string): Promise<ApiResponse<{ url: string }>> {
    // Get user's stripe customer ID first
    const supabase = this.getSupabase();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: 'User not authenticated'
      };
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('stripe_customer_id')
      .eq('id', user.id)
      .single();

    if (!profile?.stripe_customer_id) {
      return {
        success: false,
        error: 'No billing information found'
      };
    }

    // Call frontend API route directly
    try {
      const response = await authJson<{ url: string; error?: string }>(
        '/api/billing/create-portal-session',
        { method: 'POST', body: JSON.stringify({ customerId: profile.stripe_customer_id }) }
      );
      if (!response.ok || !response.body?.url) {
        return { success: false, error: response.body?.error || `HTTP ${response.status}` };
      }
      return { success: true, data: { url: response.body.url } };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Network error' };
    }
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Utility functions for error handling
export const handleApiError = (response: ApiResponse<unknown>, fallbackMessage = 'An error occurred') => {
  if (!response.success) {
    console.error('API Error:', response.error);
    return response.error || fallbackMessage;
  }
  return null;
};

export const isApiSuccess = <T>(response: ApiResponse<T>): response is ApiResponse<T> & { data: T } => {
  return response.success && response.data !== undefined;
};