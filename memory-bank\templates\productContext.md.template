# productContext.md - The 'Why' and User Experience (Multi-Project Workspace)

**Purpose:** Explains the problem the project solves, the target audience, and the desired user experience across the different components/subprojects.

**Instructions for Dane:**
*   Populate based on PRD and user understanding.
*   For User Scenarios/Workflows, clearly indicate which subproject(s) are involved in each step (e.g., `User interacts with [Frontend]`, `[Frontend] calls [Backend] API`).
*   Prefix UX Goals/Principles if they apply differently to different subprojects (e.g., `[Frontend]` Simplicity vs. `[VSTO]` Data Density).

---

## 1. Problem Statement

<!-- #Problem -->
**Problem Being Solved:** [Describe the overall problem the integrated solution addresses.]

---

## 2. Proposed Solution

<!-- #Solution -->
**Our Solution:** [Describe how the combined subprojects solve the problem.]

---

## 3. Target Audience / Users

<!-- #TargetUsers -->
**Primary Users:**
*   [Describe users and how they might interact with different parts: e.g., End-users primarily via Frontend, Admins via potentially a separate Backend interface or VSTO Add-in.]

---

## 4. Key User Scenarios / Workflows

<!-- #UserFlows -->
**Core User Journeys:**
*   **Scenario 1: [Name of Scenario, e.g., User Registers and Logs In]**
    *   **Goal:** User creates an account and accesses the application.
    *   **Steps:**
        1.  User accesses `[Frontend]` registration page.
        2.  User submits details via `[Frontend]`.
        3.  `[Frontend]` calls `[Backend]` registration API.
        4.  `[Backend]` validates data, creates user record, returns success/token.
        5.  User logs in via `[Frontend]`, calls `[Backend]` login API.
        6.  `[Backend]` verifies credentials, returns session/token.
    *   **Desired Outcome:** User successfully logged in and sees main dashboard on `[Frontend]`.
*   **Scenario 2: [Name of Scenario involving VSTO Add-in]**
    *   **Goal:** [...]
    *   **Steps:**
        1.  User interacts with `[VSTO Add-in]` Ribbon/Task Pane.
        2.  `[VSTO Add-in]` may call `[Backend]` API for data.
        3.  `[VSTO Add-in]` modifies PowerPoint document.
    *   **Desired Outcome:** [...]
*   [Add other critical user workflows, detailing subproject interactions.]

---

## 5. User Experience (UX) Goals & Principles

<!-- #UXGoals -->
**Desired User Experience:**
*   `[Global]` [Overall desired feel, e.g., Consistent branding across components.]
*   `[Frontend]` [Specific goals for web UI, e.g., Responsive, modern, intuitive.]
*   `[VSTO]` [Specific goals for Add-in, e.g., Seamless integration with PowerPoint workflow, efficient data handling.]

<!-- #UXPrinciples -->
**Guiding Principles:**
*   `[Global]` [Overall principles, e.g., Security first.]
*   `[Frontend]` [e.g., Mobile-first responsiveness.]
*   `[VSTO]` [e.g., Minimize user interruption.]

---