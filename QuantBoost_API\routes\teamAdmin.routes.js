const express = require('express');
const router = express.Router({ mergeParams: true }); // mergeParams is important for accessing :subscriptionId
const { authenticateJWT } = require('../middleware/authMiddleware');
const { supabase } = require('../supabaseClient');
const { sendSuccess, sendError } = require('../utils/responseHelpers');
const { isValidEmail } = require('../utils/validationHelpers'); // Added: require for validationHelpers
const { logLicenseEvent } = require('../utils/eventLogger'); // Added: require for eventLogger

// Apply JWT authentication to all routes in this file
router.use(authenticateJWT);

// Middleware to check if the authenticated user owns the subscription
// This will be used by all routes in this file that operate on a specific subscription
async function checkSubscriptionOwner(req, res, next) {
    const { subscriptionId } = req.params;
    const userId = req.user.id;

    if (!subscriptionId) {
        return sendError(res, 400, 'Subscription ID is required.');
    }

    try {
        const { data: subscription, error } = await supabase
            .from('subscriptions')
            .select('id, user_id, status, plan_id, quantity, current_period_end') // Added current_period_end
            .eq('id', subscriptionId)
            .eq('user_id', userId) // Ensure the user owns this subscription
            .single();

        if (error || !subscription) {
            return sendError(res, 404, 'Subscription not found or you do not have permission to manage it.');
        }

        // Optionally, check if the subscription plan allows team management
        // For example, if 'quantity' or a specific 'plan_id' indicates a team plan
        // if (subscription.quantity <= 1 && !isTeamPlan(subscription.plan_id)) {
        //     return sendError(res, 403, 'This subscription does not support team management.');
        // }

        if (!subscription.current_period_end) {
            // This is a critical piece of information for team license assignment
            console.warn(`Subscription ${subscriptionId} is missing current_period_end.`);
            // Depending on strictness, you might want to sendError here
        }

        req.subscription = subscription; // Attach subscription details to the request object
        next();
    } catch (err) {
        console.error('Error checking subscription owner:', err);
        return sendError(res, 500, 'Internal server error while verifying subscription ownership.');
    }
}

// GET /v1/me/subscriptions/:subscriptionId/team-licenses - Fetches all licenses associated with this subscription
router.get('/team-licenses', checkSubscriptionOwner, async (req, res) => {
    const { subscriptionId } = req.params;
    // The checkSubscriptionOwner middleware has already validated that the user owns this subscription
    // and attached the subscription details to req.subscription.

    try {
        // Enhanced query to include activation information
        const { data: licenses, error: licensesError } = await supabase
            .from('licenses')
            .select(`
                *,
                activation_count:license_activations(count)
            `)
            .eq('subscription_id', subscriptionId);

        if (licensesError) {
            console.error('Error fetching team licenses:', licensesError);
            return sendError(res, 500, 'Failed to retrieve licenses for this subscription.', licensesError.message);
        }

        if (!licenses || licenses.length === 0) {
            // It's not an error if a subscription has no licenses yet, could be a new team subscription
            return sendSuccess(res, 200, []); 
        }

        // Get activation counts for each license
        const licenseIds = licenses.map(l => l.id);
        const { data: activations, error: activationsError } = await supabase
            .from('license_activations')
            .select('license_id, is_active')
            .in('license_id', licenseIds);

        if (activationsError) {
            console.error('Error fetching license activations:', activationsError);
            return sendError(res, 500, 'Failed to retrieve license activation data.', activationsError.message);
        }

        // Enhance licenses with activation data
        const enhancedLicenses = licenses.map(license => {
            const licenseActivations = activations?.filter(a => a.license_id === license.id) || [];
            const activeActivations = licenseActivations.filter(a => a.is_active);
            
            return {
                ...license,
                activation_count: activeActivations.length,
                max_activations: license.max_activations || 1,
                is_activated: activeActivations.length > 0,
                available_activations: (license.max_activations || 1) - activeActivations.length
            };
        });

        sendSuccess(res, 200, enhancedLicenses);
    } catch (err) {
        console.error('Unexpected error in GET /team-licenses:', err);
        return sendError(res, 500, 'An unexpected error occurred while fetching team licenses.');
    }
});

// POST /v1/me/subscriptions/:subscriptionId/team-licenses/assign - Assigns a license from the subscription to a user
router.post('/team-licenses/assign', checkSubscriptionOwner, async (req, res) => {
    const { subscriptionId } = req.params;
    const { license_id_to_assign, target_user_email } = req.body;
    const requestingUserId = req.user.id; // The subscription owner

    if (!license_id_to_assign || !target_user_email) {
        return sendError(res, 400, 'License ID and target user email are required.');
    }

    if (!isValidEmail(target_user_email)) {
        return sendError(res, 400, 'Invalid target user email format.');
    }

    try {
        // 1. Fetch the target user's profile to get their user_id
        const { data: targetUserProfile, error: userProfileError } = await supabase
            .from('profiles') // Assuming your user profiles table is named 'profiles'
            .select('user_id, email')
            .eq('email', target_user_email)
            .single();

        if (userProfileError || !targetUserProfile) {
            // Option: Could allow assigning to an email even if user doesn't exist yet,
            // then user claims it. For now, require existing user.
            return sendError(res, 404, `User with email ${target_user_email} not found.`);
        }
        const targetUserId = targetUserProfile.user_id;

        // 2. Fetch the license to be assigned
        const { data: license, error: licenseFetchError } = await supabase
            .from('licenses')
            .select('*')
            .eq('id', license_id_to_assign)
            .eq('subscription_id', subscriptionId) // Ensure it belongs to this subscription
            .single();        

        if (licenseFetchError || !license) {
            return sendError(res, 404, 'License not found or it does not belong to this subscription.');
        }

        // 3. Check if the license is assignable (e.g., not already assigned to someone else, or is 'unassigned')
        if (license.user_id && license.user_id !== targetUserId) {
            return sendError(res, 409, `License ${license_id_to_assign} is already assigned to another user.`);
        }
        if (license.user_id === targetUserId && license.status === 'active') {
            return sendError(res, 409, `License ${license_id_to_assign} is already assigned and active for this user.`);
        }
        // Add more checks if needed, e.g., license.status === 'unassigned' or similar

        // 4. Update the license
        const updates = {
            user_id: targetUserId,
            email: target_user_email,
            status: 'active', // Directly activate for team member
            assigned_at: new Date().toISOString(),
            // Ensure expiry_date is aligned with the subscription's current_period_end
            // req.subscription is available from checkSubscriptionOwner middleware
            expiry_date: req.subscription.current_period_end ? new Date(req.subscription.current_period_end).toISOString() : license.expiry_date,
        };

        const { data: updatedLicense, error: licenseUpdateError } = await supabase
            .from('licenses')
            .update(updates)
            .eq('id', license_id_to_assign)
            .select()
            .single();

        if (licenseUpdateError) {
            console.error('Error assigning team license:', licenseUpdateError);
            return sendError(res, 500, 'Failed to assign license.', licenseUpdateError.message);
        }

        // 5. Log the event
        try {
            await logLicenseEvent(license.id, 'team_assigned', `License assigned to ${target_user_email} (User ID: ${targetUserId}) by subscription owner ${requestingUserId}.`, { assigned_to_user_id: targetUserId, assigned_by_user_id: requestingUserId, subscription_id: subscriptionId });
        } catch (logError) {
            console.error("Failed to log team assignment event:", logError); 
            // Non-critical, so don't fail the whole request
        }

        sendSuccess(res, 200, updatedLicense);

    } catch (err) {
        console.error('Unexpected error in POST /team-licenses/assign:', err);
        return sendError(res, 500, 'An unexpected error occurred while assigning the license.');
    }
});

// POST /v1/me/subscriptions/:subscriptionId/team-licenses/invite - Invites a user to the subscription by creating a new license for them
router.post('/team-licenses/invite', checkSubscriptionOwner, async (req, res) => {
    const { subscriptionId } = req.params;
    const { target_email } = req.body;
    const requestingUserId = req.user.id; // The subscription owner

    if (!target_email || !isValidEmail(target_email)) {
        return sendError(res, 400, 'A valid target user email is required.');
    }

    try {
        // 1. Check subscription capacity
        const { count: existingLicensesCount, error: countError } = await supabase
            .from('licenses')
            .select('id', { count: 'exact', head: true })
            .eq('subscription_id', subscriptionId);

        if (countError) {
            console.error('Error counting existing licenses for subscription:', countError);
            return sendError(res, 500, 'Could not verify subscription capacity.', countError.message);
        }

        if (existingLicensesCount >= req.subscription.quantity) {
            return sendError(res, 409, 'Subscription capacity reached. Cannot invite more users.');
        }

        // 2. Determine Product ID and Tier
        const productIdToUse = "quantboost-suite"; // Single product ID as per user clarification
        const licenseTierToUse = "team";         // Licenses created via team invite are "team" tier

        // The console.warn messages for placeholder product_id and license_tier are removed
        // as these are now explicitly set based on the simplified approach.

        // 3. Create the new license record
        const newLicenseData = {
            subscription_id: subscriptionId,
            product_id: productIdToUse, 
            status: 'assigned', // 'assigned' indicates it's ready for the invited user to claim by logging in
            email: target_email,
            user_id: null, // Will be populated when the user claims/activates
            expiry_date: req.subscription.current_period_end ? new Date(req.subscription.current_period_end).toISOString() : null,
            assigned_at: new Date().toISOString(),
            license_tier: licenseTierToUse,
            // max_activations and license_key will use DB defaults (e.g., max_activations=1, license_key=uuid_generate_v4())
        };

        const { data: createdLicense, error: licenseCreationError } = await supabase
            .from('licenses')
            .insert(newLicenseData)
            .select()
            .single();

        if (licenseCreationError) {
            console.error('Error creating invited team license:', licenseCreationError);
            // Check for unique constraint violation on email if you have one for active/assigned licenses per product
            if (licenseCreationError.code === '23505') { // Unique violation
                 return sendError(res, 409, `An active or assigned license already exists for ${target_email} for this product/subscription type.`, licenseCreationError.message);
            }
            return sendError(res, 500, 'Failed to create license for invitation.', licenseCreationError.message);
        }

        // 4. Log the event
        try {
            await logLicenseEvent(createdLicense.id, 'team_invited', `License invite created for ${target_email} by subscription owner ${requestingUserId}.`, { invited_email: target_email, invited_by_user_id: requestingUserId, subscription_id: subscriptionId });
        } catch (logError) {
            console.error("Failed to log team invitation event:", logError); 
            // Non-critical, so don't fail the whole request
        }

        // TODO: Trigger an actual email invitation to target_email. This is outside the scope of this API endpoint's direct response.

        sendSuccess(res, 201, createdLicense); // 201 Created for new resource

    } catch (err) {
        console.error('Unexpected error in POST /team-licenses/invite:', err);
        return sendError(res, 500, 'An unexpected error occurred while processing the invitation.');
    }
});

// POST /v1/me/subscriptions/:subscriptionId/team-licenses/unassign
router.post('/team-licenses/unassign', checkSubscriptionOwner, async (req, res) => {
    const { subscriptionId } = req.params;
    const { license_id_to_unassign } = req.body;
    const requestingUserId = req.user.id; // The subscription owner

    if (!license_id_to_unassign) {
        return sendError(res, 400, 'License ID to unassign is required.');
    }

    try {
        // 1. Fetch the license to be unassigned
        const { data: license, error: licenseFetchError } = await supabase
            .from('licenses')
            .select('*')
            .eq('id', license_id_to_unassign)
            .eq('subscription_id', subscriptionId) // Ensure it belongs to this subscription
            .single();

        if (licenseFetchError || !license) {
            return sendError(res, 404, 'License not found or it does not belong to this subscription.');
        }

        // 2. Check if the license is actually assigned
        if (!license.user_id && license.status !== 'active' && license.status !== 'assigned') {
            // If it's already unassigned or in a state that's not actively used by a team member
            return sendError(res, 409, `License ${license_id_to_unassign} is not currently assigned to a user or is not active.`);
        }
        const unassignedFromUserId = license.user_id; // Capture for logging before clearing
        const unassignedFromEmail = license.email; // Capture for logging

        // 3. Update the license to unassign it
        const updates = {
            user_id: null,
            email: null, // Clear the assigned email
            status: 'unassigned', // Or 'available', 'revoked' depending on desired lifecycle
            assigned_at: null, // Clear assignment timestamp
            // expiry_date remains tied to the subscription period, or could be cleared if the license slot is fully reset.
            // For now, keeping expiry_date as is, assuming the slot is still valid for the subscription period.
            // activated_at: null, // Optionally clear activation details if any
            // device_fingerprint: null, // Optionally clear device fingerprint
        };

        const { data: updatedLicense, error: licenseUpdateError } = await supabase
            .from('licenses')
            .update(updates)
            .eq('id', license_id_to_unassign)
            .select()
            .single();

        if (licenseUpdateError) {
            console.error('Error unassigning team license:', licenseUpdateError);
            return sendError(res, 500, 'Failed to unassign license.', licenseUpdateError.message);
        }

        // 4. Log the event
        try {
            await logLicenseEvent(
                license.id,
                'team_unassigned',
                `License unassigned from ${unassignedFromEmail || 'User ID: ' + unassignedFromUserId} by subscription owner ${requestingUserId}.`,
                {
                    unassigned_from_user_id: unassignedFromUserId,
                    unassigned_from_email: unassignedFromEmail,
                    unassigned_by_user_id: requestingUserId,
                    subscription_id: subscriptionId
                }
            );
        } catch (logError) {
            console.error("Failed to log team unassignment event:", logError);
            // Non-critical, so don't fail the whole request
        }

        sendSuccess(res, 200, updatedLicense);

    } catch (err) {
        console.error('Unexpected error in POST /team-licenses/unassign:', err);
        return sendError(res, 500, 'An unexpected error occurred while unassigning the license.');
    }
});

module.exports = router;
