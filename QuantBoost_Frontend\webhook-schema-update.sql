-- Enhanced webhook_events table schema
-- Run this in Supabase SQL Editor if you haven't already

-- Add status and error tracking columns
ALTER TABLE webhook_events 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'processing',
ADD COLUMN IF NOT EXISTS error_message TEXT,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_webhook_events_status ON webhook_events(status);
CREATE INDEX IF NOT EXISTS idx_webhook_events_updated_at ON webhook_events(updated_at);

-- Update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_webhook_events_updated_at ON webhook_events;
CREATE TRIGGER update_webhook_events_updated_at
    BEFORE UPDATE ON webhook_events
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Query to check recent webhook events
-- SELECT 
--   stripe_event_id,
--   event_type,
--   status,
--   error_message,
--   processed_at,
--   updated_at
-- FROM webhook_events 
-- ORDER BY processed_at DESC 
-- LIMIT 20;
