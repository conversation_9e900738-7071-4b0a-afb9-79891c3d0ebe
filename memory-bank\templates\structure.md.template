# structure.md - Project Directory Structure Overview

**Purpose:** Provides a persistent map of the key directories and files within the workspace to aid navigation and context understanding, especially across memory resets.

**Instructions for Dane:**
*   **Consult:** Use this file when needing to understand the location of components, configuration, specific types of code, or when navigating between subprojects.
*   **Populate:** During initialization, use `list_dir` (with limited depth, e.g., `depth=2`) on the workspace root and identified subproject roots. Populate the sections below with the *actual* key directories and files discovered. Use the placeholders `[Folder Name]/` or `[File Name]` as guides for the *type* of information to include.
*   **Organize:** Use `## Subproject: [Subproject Name]` headings for each distinct logical subproject identified. If it's a single-project workspace, use one main heading like `## Subproject: Main Application`. List the actual root directory path for the subproject in the heading.
*   **Conciseness:** Keep descriptions brief and focused on the purpose of the directory/file. This is an overview map, not an exhaustive index of every file. Focus on top-level items and the first level within major source directories (like `src/`).
*   **Updates:** Update this file (and its `last_updated` YAML field, potentially `version`) ONLY when:
    *   Significant structural changes occur (major folder moves/renames).
    *   New subprojects are added or removed.
    *   Explicitly instructed by the user.
    *   **Do not** update for routine addition/deletion of individual component files within existing, documented structures.

---

## Workspace Root (`/`)

*Describe key files and directories at the absolute root of the workspace.*

*   `memory-bank/`: Contains AI agent operational context (THIS FOLDER). **DO NOT MODIFY STRUCTURE MANUALLY.**
*   `.github/`: [Optional: Describe purpose, e.g., Contains GitHub Actions workflows, issue templates, and Copilot instructions.]
*   `.vscode/`: [Optional: Describe purpose, e.g., Contains VS Code workspace settings (`settings.json`, `tasks.json`).]
*   `[Subproject-Folder-1]/`: [Brief purpose of this subproject's root folder, e.g., Contains the primary Web Application source]. See section below.
*   `[Subproject-Folder-2]/`: [Brief purpose, e.g., Contains the shared library code]. See section below.
*   `[Subproject-Folder-...]/`: [Add entry for each identified subproject root folder]. See section below.
*   `[PROJECT_NAME-PRD.md]`: [Optional: Describe purpose, e.g., Initial project requirements document (input for initialization).]
*   `.gitignore`: Specifies intentionally untracked files for Git.
*   `README.md`: Top-level project overview, setup, and contribution instructions.
*   `[LICENSE / CONTRIBUTING.md]`: [Optional: License and contribution guideline files.]
*   `[Root Config Files]`: [Optional: e.g., `docker-compose.yml`, `package.json` (for workspace), `.editorconfig`, `.prettierrc` - Describe purpose.]
*   `[Other key top-level files/folders]`: [Describe any other critical items at the root.]

---

## Subproject: [Subproject Name 1] (`/[path/to/subproject1]/`)

*Describe the key structure *within* this specific subproject's root directory.*

*   `src/` (or `lib/`, `app/`, etc.): Main source code directory.
    *   `[Key Source Subfolder 1]/`: [Brief purpose, e.g., Contains UI components, API controllers, core logic models.]
    *   `[Key Source Subfolder 2]/`: [Brief purpose, e.g., Contains utility functions, service integrations, data access layer.]
    *   `[Entry Point File]`: [Brief purpose, e.g., Main application/service entry point (e.g., `main.py`, `App.tsx`, `Program.cs`, `server.js`).]
    *   `... (List other *major* source subfolders or key files here)`
*   `tests/` (or `spec/`, `test/`, etc.): Contains automated tests (unit, integration, e2e).
*   `docs/`: Contains documentation specific to this subproject.
*   `scripts/`: Contains helper scripts for building, deploying, etc.
*   `config/` (or similar): Contains configuration files specific to this subproject.
*   `[Build Output Folder]/` (e.g., `dist/`, `build/`, `bin/`): Directory where built artifacts are placed (often in `.gitignore`).
*   `[Dependency Manifest File]`: (e.g., `package.json`, `requirements.txt`, `pom.xml`, `*.csproj`) Manages project dependencies and often build scripts.
*   `[Build Tool Config File]`: (e.g., `vite.config.js`, `webpack.config.js`, `Makefile`) Configuration for the build system.
*   `[Containerization File]`: (e.g., `Dockerfile`, `docker-compose.yml`) Configuration for building/running the subproject in containers.
*   `[.env.example / config.example.*]`: Template for environment variables or runtime configuration.
*   `README.md`: Subproject-specific overview, setup, and usage instructions.
*   `[Other key subproject files/folders]`: [Describe any other critical items.]

---

## Subproject: [Subproject Name 2] (`/[path/to/subproject2]/`)

*Repeat the structure above, filling in the details discovered for the second subproject.*

*   `src/` (or `lib/`, `app/`, etc.): Main source code directory.
    *   `[Key Source Subfolder 1]/`: [Brief purpose...]
    *   `[Key Source Subfolder 2]/`: [Brief purpose...]
    *   `[Entry Point File]`: [Brief purpose...]
    *   `...`
*   `tests/`: Automated tests.
*   `[Dependency Manifest File]`: Manages dependencies.
*   `README.md`: Subproject-specific details.
*   `[Other key subproject files/folders]`: [Describe any other critical items.]

---

<!-- Add sections for other identified subprojects following the pattern above -->
```
