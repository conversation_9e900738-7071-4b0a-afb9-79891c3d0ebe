import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

export async function POST(request: NextRequest) {
  try {
    const { subscriptionId, cancelAtPeriodEnd } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    const key = (globalThis as any).process?.env?.STRIPE_SECRET_KEY as string | undefined;
    if (!key) {
      return NextResponse.json({ error: 'Stripe secret key not configured' }, { status: 500 });
    }
    const stripe = new Stripe(key, { apiVersion: '2025-03-31.basil' });

    // Cancel the subscription at period end using Stripe API
    // Use idempotency key to avoid double updates and improve reliability
    const idempotencyKey = `cancel-${subscriptionId}`;
    let updatedSubscription = await stripe.subscriptions.update(
      subscriptionId,
      { cancel_at_period_end: cancelAtPeriodEnd || true },
      { idempotencyKey }
    );

    // In rare cases, a first attempt may fail silently; perform a one-time verify/retry
    if (!(updatedSubscription as any).cancel_at_period_end) {
      updatedSubscription = await stripe.subscriptions.update(
        subscriptionId,
        { cancel_at_period_end: true },
        { idempotencyKey }
      );
    }

    return NextResponse.json({ 
      success: true, 
      subscription: {
        id: updatedSubscription.id,
        cancel_at_period_end: (updatedSubscription as any).cancel_at_period_end,
        current_period_end: (updatedSubscription as any).current_period_end,
      }
    });

  } catch (error) {
    console.error('Error canceling subscription:', error);
    return NextResponse.json(
      { error: 'Failed to cancel subscription' },
      { status: 500 }
    );
  }
}