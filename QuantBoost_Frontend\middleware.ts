import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  // 1. Generate a random nonce
  const nonce = Buffer.from(crypto.randomUUID()).toString('base64');

  // 2. Define the Content Security Policy
  // This policy allows scripts from 'self' and <PERSON><PERSON>, and allows necessary inline scripts for payment flows
  const cspHeader = `
    default-src 'self';
    script-src 'self' 'nonce-${nonce}' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com;
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: https:;
    font-src 'self';
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    frame-src 'self' https://js.stripe.com;
    connect-src 'self' https://izoutrnsxaaoueljiimu.supabase.co https://api.stripe.com https://ca-quantboost-api.greentree-6ebc0a43.westus3.azurecontainerapps.io;
    block-all-mixed-content;
    upgrade-insecure-requests;
  `.replace(/\s{2,}/g, ' ').trim(); // Remove newlines and extra spaces

  // 3. Clone the request headers and set the nonce
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-nonce', nonce);

  // 4. Start building the response
  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });

  // 5. Set the CSP header on the response
  response.headers.set('Content-Security-Policy', cspHeader);

  // 6. Handle authentication redirect (existing logic)
  const token = request.cookies.get('sb-access-token')?.value;
  if (!token && request.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
