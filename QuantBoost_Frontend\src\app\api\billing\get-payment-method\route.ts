import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
});

export async function POST(request: NextRequest) {
  try {
    const { subscriptionId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      );
    }

    // Get the subscription to find the customer and default payment method
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    if (!subscription.customer || !subscription.default_payment_method) {
      return NextResponse.json(
        { error: 'No payment method found for this subscription' },
        { status: 404 }
      );
    }

    // Get the payment method details
    const paymentMethod = await stripe.customers.retrievePaymentMethod(
      subscription.customer as string,
      subscription.default_payment_method as string
    );

    return NextResponse.json(paymentMethod);
  } catch (error) {
    console.error('Error retrieving payment method:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve payment method' },
      { status: 500 }
    );
  }
}
