PS C:\VS projects\QuantBoost> az containerapp logs show --name ca-quantboost-api --resource-group rg-quantboost-api-prod --follow false --tail 50
{"TimeStamp": "2025-08-13T04:18:59.48767", "Log": "Connecting to the container 'ca-quantboost-api'..."} 
{"TimeStamp": "2025-08-13T04:18:59.50551", "Log": "Successfully Connected to container: 'ca-quantboost-api' [Revision: 'ca-quantboost-api--0000071', Replica: 'ca-quantboost-api--0000071-7c8fb48f9b-49skw']"}  
{"TimeStamp": "2025-08-13T01:39:30.3331401+00:00", "Log": "Monitor disabled - set ENABLE_TELEMETRY=true to enable monitoring\",\"service\":\"quantboost-api\",\"timestamp\":\"2025-08-13 01:39:30.331\",\"version\":\"1.0.0\"}"}
{"TimeStamp": "2025-08-13T01:39:30.5641871+00:00", "Log": "clients (standard and admin) initialized successfully."}
{"TimeStamp": "2025-08-13T01:39:30.638495+00:00", "Log": "Client Initialized\",\"service\":\"quantboost-api\",\"timestamp\":\"2025-08-13 01:39:30.636\",\"url\":\"https://izoutrnsxaao...\",\"version\":\"1.0.0\"}"}
{"TimeStamp": "2025-08-13T01:39:30.6449186+00:00", "Log": "API server started\",\"nodeVersion\":\"v22.18.0\",\"port\":\"3000\",\"service\":\"quantboost-api\",\"timestamp\":\"2025-08-13 01:39:30.644\",\"version\":\"1.0.0\"}"}
{"TimeStamp": "2025-08-13T04:18:54.3508467+00:00", "Log": "request\",\"method\":\"GET\",\"path\":\"/\",\"query\":{},\"requestId\":\"082974f7-15ef-4018-af8d-f4d2784223f5\",\"service\":\"quantboost-api\",\"timestamp\":\"2025-08-13 04:18:54.350\",\"userAgent\":\"python-requests/2.32.3\",\"version\":\"1.0.0\"}"}   
{"TimeStamp": "2025-08-13T04:18:54.3520407+00:00", "Log": "route accessed: {"}
{"TimeStamp": "2025-08-13T04:18:54.3520529+00:00", "Log": "url: '/',"}
{"TimeStamp": "2025-08-13T04:18:54.3520589+00:00", "Log": "query: [Object: null prototype] {},"}        
{"TimeStamp": "2025-08-13T04:18:54.3520642+00:00", "Log": "headers: { referer: undefined, userAgent: 'python-requests/2.32.3' }"}
{"TimeStamp": "2025-08-13T04:18:54.352069+00:00", "Log": "2025-08-13T04:18:54.352069014Z }"}
{"TimeStamp": "2025-08-13T04:18:54.3522138+00:00", "Log": "detection result: {"}
{"TimeStamp": "2025-08-13T04:18:54.352222+00:00", "Log": "hasAuthParams: undefined,"}
{"TimeStamp": "2025-08-13T04:18:54.3522268+00:00", "Log": "fromFrontend: undefined,"}
{"TimeStamp": "2025-08-13T04:18:54.3522314+00:00", "Log": "queryKeys: [],"}
{"TimeStamp": "2025-08-13T04:18:54.3522358+00:00", "Log": "hasReferer: false,"}
{"TimeStamp": "2025-08-13T04:18:54.3522403+00:00", "Log": "refererUrl: undefined"}
{"TimeStamp": "2025-08-13T04:18:54.3522449+00:00", "Log": "2025-08-13T04:18:54.352244887Z }"}
{"TimeStamp": "2025-08-13T04:18:54.3568712+00:00", "Log": "completed\",\"method\":\"GET\",\"path\":\"/\",\"requestId\":\"082974f7-15ef-4018-af8d-f4d2784223f5\",\"service\":\"quantboost-api\",\"statusCode\":200,\"timestamp\":\"2025-08-13 04:18:54.356\",\"version\":\"1.0.0\"}"}
PS C:\VS projects\QuantBoost> 