Reconnecting to stream...
2025-08-06T22:28:35.47783  Connecting to the container 'ca-quantboost-api'...

2025-08-06T22:28:35.49623  Successfully Connected to container: 'ca-quantboost-api' [Revision: 'ca-quantboost-api--0000069', Replica: 'ca-quantboost-api--0000069-6db5c9f847-9vj5j']
2025-08-06T20:16:43.966472066Z     "id": "3f0552f0-f052-43eb-9fc8-d97fa490c9be",
2025-08-06T20:16:43.966475231Z     "user_id": "2519e2c3-44fc-4afd-bf41-3a83efff6317",
2025-08-06T20:16:43.966478626Z     "subscription_id": "777a4ffb-812e-4843-9a75-750bec925afe",
2025-08-06T20:16:43.966498196Z     "product_id": "prod_S6Fn893jGxRhKk",
2025-08-06T20:16:43.966501871Z     "status": "active",
2025-08-06T20:16:43.966504926Z     "created_at": "2025-08-06T18:22:10.546063+00:00",
2025-08-06T20:16:43.966507720Z     "updated_at": "2025-08-06T18:22:10.546063+00:00",
2025-08-06T20:16:43.966510464Z     "email": "<EMAIL>",
2025-08-06T20:16:43.966513669Z     "trial_start_date": null,
2025-08-06T20:16:43.966516604Z     "trial_expiry_date": null,
2025-08-06T20:16:43.966519698Z     "license_tier": "Basic-Individual",
2025-08-06T20:16:43.966522723Z     "license_key": "cf2aa103-2f6a-410f-ae9d-feeb0abc5205",
2025-08-06T20:16:43.966525948Z     "expiry_date": "2026-08-06T18:22:04+00:00",
2025-08-06T20:16:43.966529092Z     "max_activations": 1,
2025-08-06T20:16:43.966532357Z     "team_admin": null,
2025-08-06T20:16:43.966535562Z     "enterprise_customer_id": null
2025-08-06T20:16:43.966538637Z   }
2025-08-06T20:16:43.966541711Z ]
2025-08-06T20:16:43.966555532Z 🔍 [LICENSES] Returning 1 licenses for user 2519e2c3-44fc-4afd-bf41-3a83efff6317 (<EMAIL>)
2025-08-06T20:16:43.967796577Z {"duration":363,"environment":"production","level":"info","message":"Request completed","method":"GET","path":"/licenses","requestId":"0b6c3e61-da17-49be-8385-b357a4db46ad","service":"quantboost-api","statusCode":304,"timestamp":"2025-08-06 20:16:43.966","version":"1.0.0"}

2025-08-06T22:29:35.88124  No logs since last 60 seconds

2025-08-06T22:29:51.920152655Z {"environment":"production","ip":"::ffff:************","level":"info","message":"Incoming request","method":"POST","path":"/v1/auth/checkout-login","query":{},"requestId":"480741ae-e658-4c95-bb3b-2253a00211a7","service":"quantboost-api","timestamp":"2025-08-06 22:29:51.919","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
2025-08-06T22:29:51.920449519Z 🎯 CHECKOUT-LOGIN: Route hit with body: {

2025-08-06T22:29:51.920464221Z   "email": "<EMAIL>",
2025-08-06T22:29:51.920471802Z   "checkoutSessionId": "pi_3RtFpsE6FvhUKV1b1N8Brxnv"
2025-08-06T22:29:51.920478312Z }
2025-08-06T22:29:51.920510510Z 🎯 CHECKOUT-LOGIN: Validating email: <EMAIL> checkoutSessionId: pi_3RtFpsE6FvhUKV1b1N8Brxnv
2025-08-06T22:29:51.920798871Z 🎯 CHECKOUT-LOGIN: Starting user lookup/creation for: <EMAIL>
2025-08-06T22:29:51.920817429Z 🎯 CHECKOUT-LOGIN: Fetching existing users...

2025-08-06T22:29:52.143494630Z 🎯 CHECKOUT-LOGIN: Found 50 total users
2025-08-06T22:29:52.143527499Z 🎯 CHECKOUT-LOGIN: User exists: true (ID: d16bda13-787f-4109-be68-6bb623d06a3b)
2025-08-06T22:29:52.143535721Z 🎯 CHECKOUT-LOGIN: Generating magic link for user: d16bda13-787f-4109-be68-6bb623d06a3b

2025-08-06T22:29:52.269628683Z 🎯 CHECKOUT-LOGIN: Successfully generated magic link
2025-08-06T22:29:52.269699058Z 🎯 CHECKOUT-LOGIN: Returning success response with actionLink length: 220
2025-08-06T22:29:52.270324302Z {"contentLength":"567","duration":350,"environment":"production","level":"info","message":"Request completed","method":"POST","path":"/checkout-login","requestId":"480741ae-e658-4c95-bb3b-2253a00211a7","service":"quantboost-api","statusCode":200,"timestamp":"2025-08-06 22:29:52.270","version":"1.0.0"}

2025-08-06T22:29:52.470034964Z {"environment":"production","ip":"::ffff:************","level":"info","message":"Incoming request","method":"GET","path":"/","query":{},"requestId":"7d6f0efd-4fe5-4ccf-abd2-a779abcad221","service":"quantboost-api","timestamp":"2025-08-06 22:29:52.469","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
2025-08-06T22:29:52.470420076Z Root route accessed: {
2025-08-06T22:29:52.470453806Z   url: '/',
2025-08-06T22:29:52.470461097Z   query: [Object: null prototype] {},
2025-08-06T22:29:52.470466675Z   headers: {
2025-08-06T22:29:52.470472664Z     referer: 'https://app-quantboost-frontend-staging.azurewebsites.net/',
2025-08-06T22:29:52.470478523Z     userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
2025-08-06T22:29:52.470483971Z   }

2025-08-06T22:29:52.470489209Z }
2025-08-06T22:29:52.470668241Z Auth detection result: {
2025-08-06T22:29:52.470680489Z   hasAuthParams: false,
2025-08-06T22:29:52.470686668Z   fromFrontend: true,
2025-08-06T22:29:52.470692267Z   queryKeys: [],
2025-08-06T22:29:52.470697364Z   hasReferer: true,
2025-08-06T22:29:52.470702692Z   refererUrl: 'https://app-quantboost-frontend-staging.azurewebsites.net/'
2025-08-06T22:29:52.470708020Z }
2025-08-06T22:29:52.470750163Z Serving auth callback redirect page
2025-08-06T22:29:52.471377432Z {"duration":2,"environment":"production","level":"info","message":"Request completed","method":"GET","path":"/","requestId":"7d6f0efd-4fe5-4ccf-abd2-a779abcad221","service":"quantboost-api","statusCode":304,"timestamp":"2025-08-06 22:29:52.471","version":"1.0.0"}

2025-08-06T22:29:54.160735947Z {"environment":"production","ip":"::ffff:************","level":"info","message":"Incoming request","method":"GET","path":"/v1/me/subscriptions","query":{},"requestId":"de0e6e8a-34b1-43e2-851b-21bf7b6104bd","service":"quantboost-api","timestamp":"2025-08-06 22:29:54.160","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}

2025-08-06T22:29:54.255145096Z [authMiddleware] SUCCESS: Validated token for user.id: d16bda13-787f-4109-be68-6bb623d06a3b, email: <EMAIL>

2025-08-06T22:29:54.347644071Z [authMiddleware] SUCCESS: Validated token for user.id: d16bda13-787f-4109-be68-6bb623d06a3b, email: <EMAIL>

2025-08-06T22:29:54.449549546Z [authMiddleware] SUCCESS: Validated token for user.id: d16bda13-787f-4109-be68-6bb623d06a3b, email: <EMAIL>

2025-08-06T22:29:54.560169968Z [authMiddleware] SUCCESS: Validated token for user.id: d16bda13-787f-4109-be68-6bb623d06a3b, email: <EMAIL>

2025-08-06T22:29:54.651896434Z [authMiddleware] SUCCESS: Validated token for user.id: d16bda13-787f-4109-be68-6bb623d06a3b, email: <EMAIL>

2025-08-06T22:29:54.759725033Z [authMiddleware] SUCCESS: Validated token for user.id: d16bda13-787f-4109-be68-6bb623d06a3b, email: <EMAIL>
2025-08-06T22:29:54.759763401Z 🔍 [SUBSCRIPTIONS] Fetching subscriptions for user: d16bda13-787f-4109-be68-6bb623d06a3b (<EMAIL>)
2025-08-06T22:29:54.759782109Z 🔍 [SUBSCRIPTIONS] Using auth token: PRESENT

2025-08-06T22:29:54.826809168Z 🔍 [SUBSCRIPTIONS] Query result - error: null
2025-08-06T22:29:54.826870681Z 🔍 [SUBSCRIPTIONS] Query result - subscriptions count: 1
2025-08-06T22:29:54.826878843Z 🔍 [SUBSCRIPTIONS] Query result - subscriptions data: [
2025-08-06T22:29:54.826884261Z   {
2025-08-06T22:29:54.826889168Z     "id": "f5d1f291-dda8-4073-b139-f98172e435c0",
2025-08-06T22:29:54.826894346Z     "user_id": "d16bda13-787f-4109-be68-6bb623d06a3b",
2025-08-06T22:29:54.826899023Z     "stripe_subscription_id": "sub_1RtFpqE6FvhUKV1bMAvTZBCP",
2025-08-06T22:29:54.826903840Z     "status": "active",
2025-08-06T22:29:54.826908227Z     "quantity": 1,
2025-08-06T22:29:54.826912794Z     "current_period_start": "2025-08-06T22:29:26+00:00",
2025-08-06T22:29:54.826917321Z     "current_period_end": "2026-08-06T22:29:26+00:00",

2025-08-06T22:29:54.826921707Z     "trial_start": null,
2025-08-06T22:29:54.826939584Z     "trial_end": null,
2025-08-06T22:29:54.826944361Z     "cancel_at_period_end": false,
2025-08-06T22:29:54.826948778Z     "canceled_at": null,
2025-08-06T22:29:54.826953575Z     "created_at": "2025-08-06T22:29:31.65874+00:00",
2025-08-06T22:29:54.826958322Z     "updated_at": "2025-08-06T22:29:53.046057+00:00",
2025-08-06T22:29:54.826962859Z     "email": "<EMAIL>",
2025-08-06T22:29:54.826967566Z     "plan_id": "Basic-Individual-year",
2025-08-06T22:29:54.826972193Z     "enterprise_customer_id": null,
2025-08-06T22:29:54.826977180Z     "product_name": "year Plan",
2025-08-06T22:29:54.826982107Z     "amount": 12000,
2025-08-06T22:29:54.827001496Z     "currency": "usd",
2025-08-06T22:29:54.827006404Z     "interval": "year",
2025-08-06T22:29:54.827015788Z     "interval_count": 1,
2025-08-06T22:29:54.827435946Z     "subscription_created": "2025-08-06T22:29:26+00:00",
2025-08-06T22:29:54.827443697Z     "billing_cycle_anchor": "2025-08-06T22:29:26+00:00",
2025-08-06T22:29:54.827449626Z     "start_date": "2025-08-06T22:29:26+00:00",
2025-08-06T22:29:54.827455125Z     "cancel_at": null
2025-08-06T22:29:54.827460332Z   }
2025-08-06T22:29:54.827465570Z ]
2025-08-06T22:29:54.827484168Z 🔍 [SUBSCRIPTIONS] Returning 1 subscriptions for user d16bda13-787f-4109-be68-6bb623d06a3b (<EMAIL>)
2025-08-06T22:29:54.827494083Z {"contentLength":"876","duration":667,"environment":"production","level":"info","message":"Request completed","method":"GET","path":"/subscriptions","requestId":"de0e6e8a-34b1-43e2-851b-21bf7b6104bd","service":"quantboost-api","statusCode":200,"timestamp":"2025-08-06 22:29:54.827","version":"1.0.0"}

2025-08-06T22:29:54.883868195Z {"environment":"production","ip":"::ffff:************","level":"info","message":"Incoming request","method":"GET","path":"/v1/me/licenses","query":{},"requestId":"55ccb304-4f39-4a33-9ee8-b702d6cbefce","service":"quantboost-api","timestamp":"2025-08-06 22:29:54.883","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}

2025-08-06T22:29:54.946522546Z [authMiddleware] SUCCESS: Validated token for user.id: d16bda13-787f-4109-be68-6bb623d06a3b, email: <EMAIL>

2025-08-06T22:29:55.071673935Z [authMiddleware] SUCCESS: Validated token for user.id: d16bda13-787f-4109-be68-6bb623d06a3b, email: <EMAIL>

2025-08-06T22:29:55.163790916Z [authMiddleware] SUCCESS: Validated token for user.id: d16bda13-787f-4109-be68-6bb623d06a3b, email: <EMAIL>

2025-08-06T22:29:55.257671322Z [authMiddleware] SUCCESS: Validated token for user.id: d16bda13-787f-4109-be68-6bb623d06a3b, email: <EMAIL>
2025-08-06T22:29:55.258294349Z 🔍 [LICENSES] Fetching licenses for user: d16bda13-787f-4109-be68-6bb623d06a3b (<EMAIL>)
2025-08-06T22:29:55.258312016Z 🔍 [LICENSES] Using auth token: PRESENT

2025-08-06T22:29:55.327373652Z 🔍 [LICENSES] Query result - error: null
2025-08-06T22:29:55.327411088Z 🔍 [LICENSES] Query result - licenses count: 1
2025-08-06T22:29:55.327426681Z 🔍 [LICENSES] Query result - licenses data: [
2025-08-06T22:29:55.327432560Z   {
2025-08-06T22:29:55.327438329Z     "id": "21394dae-4180-4192-8e1e-9d5f7af08c17",
2025-08-06T22:29:55.327443226Z     "user_id": "d16bda13-787f-4109-be68-6bb623d06a3b",
2025-08-06T22:29:55.327447633Z     "subscription_id": "f5d1f291-dda8-4073-b139-f98172e435c0",
2025-08-06T22:29:55.327452210Z     "product_id": "prod_S6Fn893jGxRhKk",
2025-08-06T22:29:55.327457528Z     "status": "active",
2025-08-06T22:29:55.327462335Z     "created_at": "2025-08-06T22:29:31.991086+00:00",
2025-08-06T22:29:55.327479170Z     "updated_at": "2025-08-06T22:29:31.991086+00:00",
2025-08-06T22:29:55.327483797Z     "email": "<EMAIL>",
2025-08-06T22:29:55.327488764Z     "trial_start_date": null,
2025-08-06T22:29:55.327493421Z     "trial_expiry_date": null,

2025-08-06T22:29:55.327497928Z     "license_tier": "Basic-Individual",
2025-08-06T22:29:55.327502435Z     "license_key": "e86655aa-4845-4d7c-8c74-bd59abab49d5",
2025-08-06T22:29:55.327506781Z     "expiry_date": "2026-08-06T22:29:26+00:00",
2025-08-06T22:29:55.327511679Z     "max_activations": 1,
2025-08-06T22:29:55.327516736Z     "team_admin": null,
2025-08-06T22:29:55.327521113Z     "enterprise_customer_id": null
2025-08-06T22:29:55.327525659Z   }
2025-08-06T22:29:55.327530356Z ]
2025-08-06T22:29:55.327542805Z 🔍 [LICENSES] Returning 1 licenses for user d16bda13-787f-4109-be68-6bb623d06a3b (<EMAIL>)
2025-08-06T22:29:55.328245626Z {"contentLength":"642","duration":445,"environment":"production","level":"info","message":"Request completed","method":"GET","path":"/licenses","requestId":"55ccb304-4f39-4a33-9ee8-b702d6cbefce","service":"quantboost-api","statusCode":200,"timestamp":"2025-08-06 22:29:55.328","version":"1.0.0"}

2025-08-06T22:30:55.54065  No logs since last 60 seconds

2025-08-06T22:31:56.00705  No logs since last 60 seconds
