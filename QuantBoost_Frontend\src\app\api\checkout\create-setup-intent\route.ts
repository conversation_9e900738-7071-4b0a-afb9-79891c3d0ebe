import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { ENV, validateStripeConfig } from '@/lib/env';

export async function POST(req: NextRequest) {
  const stripeValidation = validateStripeConfig();
  if (!stripeValidation.isValid) {
    return NextResponse.json({ error: 'Payment service unavailable' }, { status: 500 });
  }
  const stripe = new Stripe(ENV.STRIPE_SECRET_KEY!, { apiVersion: '2025-03-31.basil' });
  try {
    // Use a minimal amount and USD for setup intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: 100, // $1.00, just for setup (not charged)
      currency: 'usd',
      automatic_payment_methods: { enabled: true },
      metadata: { app: 'quantboost', setup: 'true' }
    });
    return NextResponse.json({ clientSecret: paymentIntent.client_secret });
  } catch (err: any) {
    return NextResponse.json({ error: err?.message || 'Failed to create setup intent' }, { status: 500 });
  }
}
