Run set -e
  set -e
  PATTERNS='sk_live sk_test service_role STRIPE_WEBHOOK_SECRET'
  FAIL=0
  for p in $PATTERNS; do
    if grep -R "$p" frontend_dist >/dev/null 2>&1; then
      echo "❌ Forbidden secret-like pattern found: $p" >&2
      grep -R -n "$p" frontend_dist | head -n 20
      FAIL=1
    else
      echo "✅ No occurrences of pattern: $p"
    fi
  done
  if [ "$FAIL" -eq 1 ]; then
    echo "❌ Secret scan failed" >&2
    exit 1
  fi
  echo "✅ Secret scan passed"
  shell: /usr/bin/bash -e {0}
  env:
    AZURE_WEBAPP_NAME: app-quantboost-frontend-staging
    AZURE_WEBAPP_PACKAGE_PATH: ./frontend_dist
    NODE_VERSION: 22
    AZURE_WEBAPP_RESOURCE_GROUP: rg-quantboost-frontend-staging
✅ No occurrences of pattern: sk_live
✅ No occurrences of pattern: sk_test
✅ No occurrences of pattern: service_role
❌ Forbidden secret-like pattern found: STRIPE_WEBHOOK_SECRET
frontend_dist/.next/server/app/api/webhooks/stripe/route.js:1:(()=>{var e={};e.id=4024,e.ids=[4024],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58897:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>b,serverHooks:()=>v,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>y});var i={};r.r(i),r.d(i,{POST:()=>f});var s=r(96559),a=r(48088),o=r(37719),n=r(32190),l=r(97877),c=r(86345);function d(e){return e?new Date(1e3*e).toISOString():null}function u(){return crypto.randomUUID()}function p(e){if(!e||""===e.trim())return{firstName:null,lastName:null};let t=e.trim(),r=t.indexOf(" ");if(-1===r)return{firstName:t,lastName:null};let i=t.substring(0,r).trim(),s=t.substring(r+1).trim();return{firstName:i&&""!==i.trim()?i:null,lastName:s&&""!==s.trim()?s:null}}function _(e,t){let r;if(e?.current_period_end)return d(e.current_period_end);let i=new Date(e?.created?new Date(1e3*e.created):e?.start_date?new Date(1e3*e.start_date):new Date);if(t?.recurring?.interval){let e=t.recurring.interval,r=t.recurring.interval_count||1;"year"===e?i.setFullYear(i.getFullYear()+r):"month"===e?i.setMonth(i.getMonth()+r):"week"===e?i.setDate(i.getDate()+7*r):"day"===e?i.setDate(i.getDate()+r):i.setMonth(i.getMonth()+1)}else i.setMonth(i.getMonth()+1);return i.toISOString()}async function m(e,t){try{let r=await e.customers.retrieve(t);if("object"==typeof r&&!r.deleted){let e="name"in r&&r.name||null;console.log(`🔍 STRIPE CUSTOMER DEBUG - ID: ${t}`,{customerName:r.name||"NO_NAME",fullName:e||"NO_FULL_NAME",customerEmail:"email"in r&&r.email||"NO_EMAIL",customerKeys:Object.keys(r),hasName:"name"in r,nameValue:"name"in r?r.name:"NOT_FOUND"});let{firstName:i,lastName:s}=p(e);return console.log(`🔍 PARSED NAME DEBUG - Customer: ${t}`,{originalFullName:e,parsedFirstName:i||"EMPTY_FIRST",parsedLastName:s||"EMPTY_LAST",parseResult:{firstName:i,lastName:s}}),{email:"email"in r&&r.email||null,firstName:i,lastName:s}}}catch(e){console.error("Error fetching Stripe customer:",e)}return{email:null,firstName:null,lastName:null}}async function g(e,t,r,i,s){if(console.log(`🔍 ensureUserProfile called with: customer=${t}, email=${r}, firstName=${i}, lastName=${s}`),!r||"<EMAIL>"===r)return console.error(`❌ Invalid email for customer ${t}: ${r}`),null;console.log(`🔍 Looking for existing profile by stripe_customer_id: ${t}`);let{data:a,error:o}=await e.from("profiles").select("id").eq("stripe_customer_id",t).maybeSingle();if(o&&console.error(`❌ Error finding profile by stripe_customer_id:`,o),a)return console.log(`✅ Found existing profile by stripe_customer_id: ${a.id}`),a.id;console.log(`🔍 Looking for existing profile by email: ${r}`);let{data:n,error:l}=await e.from("profiles").select("id").eq("email",r).maybeSingle();if(l&&console.error(`❌ Error finding profile by email:`,l),n){console.log(`✅ Found existing profile by email: ${n.id}, updating with stripe_customer_id`);let r={stripe_customer_id:t};i&&""!==i.trim()&&(r.first_name=i),s&&""!==s.trim()&&(r.last_name=s);let{error:a}=await e.from("profiles").update(r).eq("id",n.id);return a?console.error(`❌ Error updating profile with stripe_customer_id:`,a):console.log(`✅ Successfully updated profile with stripe_customer_id`),n.id}console.log(`👤 Creating new auth user and profile for: ${r}`);try{let a=[i,s].filter(Boolean).join(" ")||"",o=null,n=null,l=0;for(;l<3&&!o;){console.log(`🔄 Attempt ${l+1} to create auth user for: ${r}`),console.log("\uD83D\uDD0D ULTRATHINK: Creating user with payload:",{email:r,email_confirm:!0,metadata_keys:["first_name","last_name","full_name","source","stripe_customer_id"],email_domain:r.split("@")[1],email_length:r.length});let c=await e.auth.admin.createUser({email:r,email_confirm:!0,user_metadata:{first_name:i||"",last_name:s||"",full_name:a,source:"stripe_webhook",stripe_customer_id:t}});if(o=c.data,n=c.error){if(console.error(`❌ Auth user creation attempt ${l+1} failed:`,{message:n.message,code:n.code,status:n.status,details:n}),console.error("\uD83D\uDD0D ULTRATHINK: Detailed error analysis:",{errorType:typeof n,hasMessage:!!n.message,hasCode:!!n.code,hasStatus:!!n.status,isAuthError:n.__isAuthError,errorKeys:Object.keys(n),stackTrace:n.stack?.substring(0,500)}),n.message?.includes("email")&&console.log("\uD83D\uDD0D ULTRATHINK: Email-related error detected"),n.message?.includes("database")&&console.log("\uD83D\uDD0D ULTRATHINK: Database-related error detected"),n.message?.includes("permission")&&console.log("\uD83D\uDD0D ULTRATHINK: Permission-related error detected"),n.message?.includes("User already registered")||n.message?.includes("email")||"user_already_exists"===n.code){console.log("✅ User already exists, proceeding to find existing profile");break}if(n.message?.includes("unexpected_failure")||500===n.status||n.message?.includes("internal server error")){console.error("\uD83D\uDEA8 Supabase auth service error detected - likely temporary"),++l<3&&(console.log(`⏱️ Waiting ${2*l} seconds before retry due to service error...`),await new Promise(e=>setTimeout(e,2e3*l)));continue}++l<3&&(console.log(`⏱️ Waiting 1 second before retry...`),await new Promise(e=>setTimeout(e,1e3)))}}if(n&&!o?.user){if(console.error("❌ Final error creating auth user after retries:",n),n?.code==="unexpected_failure"||n?.status===500){console.log("\uD83D\uDD04 Auth service failing, creating profile-only record for webhook processing...");let a=crypto.randomUUID();try{let{data:o,error:n}=await e.from("profiles").insert({id:a,email:r,first_name:i||null,last_name:s||null,stripe_customer_id:t,is_team_admin:!1,auth_created:!1}).select("id").single();if(n)return console.error("❌ Error creating fallback profile:",n),null;return console.log(`✅ Created fallback profile: ${o.id} (auth user will be created on first login)`),o.id}catch(e){return console.error("❌ Exception creating fallback profile:",e),null}}if(n?.message?.includes("email")||n?.message?.includes("already")||n?.code==="user_already_exists"){console.log("\uD83D\uDD04 Email already exists for auth user, trying to find existing profile...");let{data:a,error:o}=await e.from("profiles").select("id").eq("email",r).maybeSingle();if(o)return console.error("❌ Error finding profile after auth race condition:",o),null;if(a){console.log(`✅ Found existing profile after auth race condition: ${a.id}`);let r={stripe_customer_id:t};i&&""!==i.trim()&&(r.first_name=i),s&&""!==s.trim()&&(r.last_name=s);let{error:o}=await e.from("profiles").update(r).eq("id",a.id);return o?console.error(`❌ Error updating profile after auth race condition:`,o):console.log(`✅ Successfully updated profile after auth race condition`),a.id}}return null}console.log(`✅ Created auth user: ${o.user.id}`);let{data:c,error:d}=await e.from("profiles").insert({id:o.user.id,email:r,first_name:i||null,last_name:s||null,stripe_customer_id:t,is_team_admin:!1}).select("id").single();if(d){if(console.error("❌ Error creating profile:",d),"23505"===d.code&&d.message.includes("profiles_email_key")){console.log("\uD83D\uDD04 Duplicate email detected, looking for existing profile after race condition...");let{data:a,error:o}=await e.from("profiles").select("id").eq("email",r).maybeSingle();if(o)return console.error("❌ Error finding profile after race condition:",o),null;if(a){console.log(`✅ Found existing profile after race condition: ${a.id}`);let r={stripe_customer_id:t};i&&""!==i.trim()&&(r.first_name=i),s&&""!==s.trim()&&(r.last_name=s);let{error:o}=await e.from("profiles").update(r).eq("id",a.id);return o?console.error(`❌ Error updating profile after race condition:`,o):console.log(`✅ Successfully updated profile after race condition`),a.id}}return null}return console.log(`✅ Created new profile: ${c.id} with stripe_customer_id: ${t}`),c.id}catch(e){return console.error("❌ Exception in user creation:",e),null}}async function f(e){let t;console.log("\uD83D\uDE80 Webhook received at:",new Date().toISOString()),console.log("\uD83D\uDD27 Environment check:",{hasStripeSecret:!!process.env.STRIPE_SECRET_KEY,hasWebhookSecret:!!process.env.STRIPE_WEBHOOK_SECRET,hasSupabaseUrl:!0,hasServiceKey:!!process.env.SUPABASE_SERVICE_KEY,supabaseUrl:"***".substring(0,20)+"...",serviceKeyPrefix:process.env.SUPABASE_SERVICE_KEY?.substring(0,20)+"..."});let r=new l.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-03-31.basil"}),i=process.env.STRIPE_WEBHOOK_SECRET,s=(0,c.UU)("***",process.env.SUPABASE_SERVICE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});console.log("\uD83D\uDD0D ULTRATHINK: Testing Supabase auth service health...");try{let{error:e}=await s.from("profiles").select("*",{count:"exact",head:!0});console.log("✅ Supabase database connection test:",{success:!e,error:e?.message});let{data:t,error:r}=await s.auth.admin.listUsers({page:1,perPage:1});console.log("✅ Supabase auth service test:",{success:!r,error:r?.message,userCount:t?.users?.length||0}),r&&(console.error("\uD83D\uDEA8 ULTRATHINK: Auth service test failed - checking service key permissions"),console.error("Auth test error details:",{message:r.message,status:r.status,code:r.code}))}catch(e){console.error("\uD83D\uDEA8 ULTRATHINK: Supabase health check failed:",e)}let a=await e.text(),o=e.headers.get("stripe-signature");try{t=r.webhooks.constructEvent(a,o,i),console.log("✅ Stripe event verified:",t.type,"ID:",t.id)}catch(e){return console.error("❌ Stripe signature error:",e),new n.NextResponse("Webhook signature failed",{status:400})}try{let{data:e}=await s.from("webhook_events").select("id").eq("stripe_event_id",t.id).maybeSingle();if(e)return console.log("⚠️ Duplicate event detected, skipping:",t.id),n.NextResponse.json({received:!0,duplicate:!0});let{error:r}=await s.from("webhook_events").insert({stripe_event_id:t.id,event_type:t.type,processed_at:new Date().toISOString()});if(r){if("23505"===r.code)return console.log("⚠️ Event already being processed by another webhook instance:",t.id),n.NextResponse.json({received:!0,duplicate:!0});console.error("Failed to log webhook event:",r)}}catch(e){console.error("Exception during webhook event logging:",e)}try{switch(t.type){case"setup_intent.succeeded":{let e=t.data.object;console.log("\uD83D\uDC49 Handling setup_intent.succeeded",{id:e.id,status:e.status,customer:e.customer,payment_method:e.payment_method,usage:e.usage});break}case"setup_intent.canceled":{let e=t.data.object;console.log("\uD83D\uDC49 Handling setup_intent.canceled",{id:e.id,reason:e.cancellation_reason,customer:e.customer});break}case"payment_method.attached":{let e=t.data.object;console.log("\uD83D\uDC49 Handling payment_method.attached",{id:e.id,customer:e.customer,type:e.type,brand:e.card?.brand,last4:e.card?.last4,exp_month:e.card?.exp_month,exp_year:e.card?.exp_year});break}case"payment_method.detached":{let e=t.data.object;console.log("\uD83D\uDC49 Handling payment_method.detached",{id:e.id,customer:e.customer});break}case"customer.updated":{let e=t.data.object;console.log("\uD83D\uDC49 Handling customer.updated (invoice_settings)",{id:e.id,default_payment_method:e.invoice_settings?.default_payment_method});break}case"checkout.session.completed":{console.log("\uD83D\uDC49 Handling checkout.session.completed");let e=t.data.object;console.log(`💳 Processing checkout session: ${e.id}`);let i=e.subscription,a=e.customer;console.log(`🔗 Subscription ID: ${i}, Customer ID: ${a}`);let o=e.customer_details?.email||e.metadata?.email||null,l=e.customer_details?.name||null;console.log(`🔍 CHECKOUT SESSION DEBUG - ${e.id}`,{customerDetailsEmail:e.customer_details?.email||"NO_EMAIL",customerDetailsName:e.customer_details?.name||"NO_NAME",metadataEmail:e.metadata?.email||"NO_METADATA_EMAIL",sessionCustomer:e.customer||"NO_CUSTOMER",fullNameExtracted:l||"NO_FULL_NAME"});let{firstName:c,lastName:f}=p(l),b=c,h=f;if(console.log(`📧 Initial email: ${o}, firstName: ${b}, lastName: ${h}`),!o||!b||!h){let e=await m(r,a);o=o||e.email,b=b||e.firstName,h=h||e.lastName,console.log(`📧 After Stripe lookup - email: ${o}, firstName: ${b}, lastName: ${h}`)}if(!o||"<EMAIL>"===o)return console.error(`❌ Invalid email for checkout session ${e.id}: ${o}`),new n.NextResponse("Invalid email address",{status:422});console.log(`👤 Creating/finding user profile for ${o}`);let y=await g(s,a,o,b,h);if(!y)return console.error("❌ Failed to create or find user profile"),console.error("⚠️ Webhook will complete successfully but user profile creation failed"),n.NextResponse.json({received:!0,warning:"User profile creation failed but webhook acknowledged"});console.log(`✅ User profile resolved: ${y}`),console.log(`📋 Fetching subscription details: ${i}`);let v=await r.subscriptions.retrieve(i),w=v.items.data[0];console.log(`💰 Subscription product: ${w.price.product}, quantity: ${w.quantity}`),console.log(`📅 Checkout Billing Period Debug:`,{current_period_start:v.current_period_start,current_period_end:v.current_period_end,current_period_start_iso:d(v.current_period_start),current_period_end_iso:d(v.current_period_end),created:v.created,start_date:v.start_date,billing_cycle_anchor:v.billing_cycle_anchor,status:v.status});let S=await r.prices.retrieve(w.price.id);console.log(`📅 Price interval: ${S.recurring?.interval}, interval_count: ${S.recurring?.interval_count}`);let E=(w.quantity||1)>1;console.log(`👤 Updating profile team admin status: ${E}`);let $={is_team_admin:E};b&&""!==b.trim()&&($.first_name=b),h&&""!==h.trim()&&($.last_name=h);let{error:k}=await s.from("profiles").update($).eq("id",y);k?console.error("❌ Error updating profile team admin status:",k):console.log(`✅ Profile updated - team admin: ${E}`);let N=d(v.current_period_start),x=d(v.current_period_end);if(!N||!x){console.log(`⚠️ Missing billing period data in checkout, attempting fallback calculation...`);let e=new Date(1e3*v.created);if(N=N||e.toISOString(),!x){let t=new Date(e);S.recurring?.interval==="year"?t.setFullYear(t.getFullYear()+(S.recurring?.interval_count||1)):S.recurring?.interval==="month"?t.setMonth(t.getMonth()+(S.recurring?.interval_count||1)):t.setMonth(t.getMonth()+1),x=t.toISOString()}console.log(`✅ Calculated checkout billing periods:`,{current_period_start:N,current_period_end:x})}let{data:q,error:I}=await s.from("subscriptions").upsert({user_id:y,stripe_subscription_id:v.id,status:v.status,quantity:w.quantity||1,current_period_start:N,current_period_end:x,trial_start:d(v.trial_start),trial_end:d(v.trial_end),cancel_at_period_end:v.cancel_at_period_end,email:o},{onConflict:"stripe_subscription_id"}).select("id").single();if(I||!q?.id)return console.error("❌ Error creating subscription:",I),new n.NextResponse("Could not save subscription",{status:500});console.log(`✅ Subscription created/updated: ${q.id}`);let{count:T}=await s.from("licenses").select("*",{count:"exact",head:!0}).eq("subscription_id",q.id);if(console.log(`🎫 Existing licenses for subscription: ${T}`),0===T){let e;let t=w.quantity||1,r=t>1,i=r?"Basic-Team":"Basic-Individual";e=r?"inactive":"active"===v.status||"trialing"===v.status?"active":"pending",console.log(`🎫 Creating ${t} ${i} licenses with status: ${e}`);let a=[];for(let s=0;s<t;s++)a.push({user_id:y,subscription_id:q.id,license_key:u(),product_id:w.price.product,status:e,license_tier:i,team_admin:r?o:null,email:r?null:o,expiry_date:x||_(v,S)});let{error:n}=await s.from("licenses").insert(a);n?console.error("❌ Error creating licenses:",n):console.log(`✅ Successfully created ${t} licenses`)}else console.log(`⚠️ Licenses already exist, skipping creation`);console.log("✅ Checkout session processed successfully");break}case"customer.subscription.created":case"customer.subscription.updated":{console.log("\uD83D\uDC49 Handling",t.type);let e=t.data.object,i=e.items.data[0];console.log(`🔗 Subscription: ${e.id}, Customer: ${e.customer}, Status: ${e.status}`),console.log(`💰 Product: ${i.price.product}, Quantity: ${i.quantity}`),console.log(`📅 Billing Period Debug:`,{current_period_start:e.current_period_start,current_period_end:e.current_period_end,current_period_start_iso:d(e.current_period_start),current_period_end_iso:d(e.current_period_end),created:e.created,start_date:e.start_date,billing_cycle_anchor:e.billing_cycle_anchor,status:e.status});let a=await r.prices.retrieve(i.price.id);console.log(`📅 Price interval: ${a.recurring?.interval}, interval_count: ${a.recurring?.interval_count}`),console.log(`👤 Fetching customer details for: ${e.customer}`);let{email:o,firstName:n,lastName:l}=await m(r,e.customer);console.log(`📧 Customer details - email: ${o}, firstName: ${n}, lastName: ${l}`),console.log(`🔍 SUBSCRIPTION EVENT DEBUG - ${e.id}`,{stripeCustomerId:e.customer,extractedEmail:o||"NO_EMAIL",extractedFirstName:n||"NO_FIRST_NAME",extractedLastName:l||"NO_LAST_NAME",subscriptionStatus:e.status,hasCustomerName:!!(n&&l)}),console.log(`👤 Creating/finding user profile for ${o}`);let c=await g(s,e.customer,o,n,l);if(!c){console.error(`❌ Could not resolve user for subscription ${e.id}, customer: ${e.customer}, email: ${o}`);break}console.log(`✅ User profile resolved: ${c}`);let p=(i.quantity||1)>1;console.log(`👤 Updating profile team admin status: ${p}`);let f={is_team_admin:p};n&&""!==n.trim()&&(f.first_name=n),l&&""!==l.trim()&&(f.last_name=l);let{error:b}=await s.from("profiles").update(f).eq("id",c);b?console.error("❌ Error updating profile team admin status:",b):console.log(`✅ Profile updated - team admin: ${p}`);let h=d(e.current_period_start),y=d(e.current_period_end);if(!h||!y){console.log(`⚠️ Missing billing period data, attempting to retrieve from Stripe...`);try{let t=await r.subscriptions.retrieve(e.id);h=d(t.current_period_start)||h,y=d(t.current_period_end)||y,console.log(`✅ Retrieved fresh billing data:`,{current_period_start:h,current_period_end:y})}catch(t){if(console.error(`❌ Failed to fetch fresh subscription data:`,t),!h||!y){console.log(`🔄 Using fallback calculation for billing periods...`);let t=new Date(1e3*e.created);if(h=h||t.toISOString(),!y){let e=new Date(t);a.recurring?.interval==="year"?e.setFullYear(e.getFullYear()+(a.recurring?.interval_count||1)):a.recurring?.interval==="month"?e.setMonth(e.getMonth()+(a.recurring?.interval_count||1)):e.setMonth(e.getMonth()+1),y=e.toISOString()}console.log(`✅ Calculated billing periods:`,{current_period_start:h,current_period_end:y})}}}console.log(`💾 Creating/updating subscription record in database`);let{data:v,error:w}=await s.from("subscriptions").upsert({user_id:c,stripe_subscription_id:e.id,status:e.status,quantity:i.quantity||1,plan_id:`Basic-${(i.quantity||1)>1?"Team":"Individual"}-${a.recurring?.interval||"unknown"}`,current_period_start:h,current_period_end:y,trial_start:d(e.trial_start),trial_end:d(e.trial_end),cancel_at_period_end:e.cancel_at_period_end,email:o,product_name:a.nickname||`${a.recurring?.interval||"unknown"} Plan`,amount:i.price.unit_amount||0,currency:a.currency||"usd",interval:a.recurring?.interval||"unknown",interval_count:a.recurring?.interval_count||1,subscription_created:d(e.created),billing_cycle_anchor:d(e.billing_cycle_anchor),start_date:d(e.start_date),cancel_at:d(e.cancel_at)},{onConflict:"stripe_subscription_id"}).select("id").single();if(w||!v?.id){console.error("❌ Error updating subscription:",w);break}console.log(`✅ Subscription created/updated in DB: ${v.id}`);let{count:S}=await s.from("licenses").select("*",{count:"exact",head:!0}).eq("subscription_id",v.id),E=i.quantity||1,$=S||0;if(console.log(`🎫 License sync - target: ${E}, current: ${$}`),$<E){let t;let r=E>1,n=r?"Basic-Team":"Basic-Individual";t=r?"inactive":"active"===e.status||"trialing"===e.status?"active":"pending";let l=E-$;console.log(`🎫 Creating ${l} ${n} licenses with status: ${t}`);let d=[];for(let s=0;s<l;s++)d.push({user_id:c,subscription_id:v.id,license_key:u(),product_id:i.price.product,status:t,license_tier:n,team_admin:r?o:null,email:r?null:o,expiry_date:y||_(e,a)});let{error:p}=await s.from("licenses").insert(d);p?console.error("❌ Error creating licenses:",p):console.log(`✅ Successfully created ${l} licenses`)}let k={active:"active",trialing:"active",canceled:"canceled",unpaid:"inactive",past_due:"inactive",incomplete:"inactive"}[e.status]||"inactive";if(p){if(["canceled","unpaid","past_due","incomplete"].includes(e.status)){console.log(`🔄 Updating team license statuses to: ${k} (preserving assignments)`);let{error:t}=await s.from("licenses").update({status:k,expiry_date:y||_(e,a)}).eq("subscription_id",v.id);t?console.error("❌ Error updating team license statuses:",t):console.log(`✅ Team license statuses updated`)}else console.log(`ℹ️ Team subscription is ${e.status}, preserving individual license assignment statuses`)}else{console.log(`🔄 Updating individual license statuses to: ${k}`);let{error:t}=await s.from("licenses").update({status:k,expiry_date:y||_(e,a)}).eq("subscription_id",v.id);t?console.error("❌ Error updating individual license statuses:",t):console.log(`✅ Individual license statuses updated`)}console.log("✅ Subscription processing completed successfully");break}case"customer.subscription.deleted":{console.log("\uD83D\uDC49 Handling customer.subscription.deleted");let e=t.data.object,{data:r}=await s.from("subscriptions").update({status:"canceled"}).eq("stripe_subscription_id",e.id).select("id").single();r?.id&&await s.from("licenses").update({status:"canceled"}).eq("subscription_id",r.id);break}case"invoice.paid":{console.log("\uD83D\uDC49 Handling invoice.paid");let e=t.data.object;if(!e.subscription){console.log("Skipping non-subscription invoice");break}let{data:r}=await s.from("subscriptions").update({status:"active",current_period_start:d(e.period_start),current_period_end:d(e.period_end)}).eq("stripe_subscription_id",e.subscription).select("id").single();r?.id&&await s.from("licenses").update({status:"active",expiry_date:d(e.period_end)}).eq("subscription_id",r.id).in("status",["past_due","inactive"]);break}case"invoice.payment_succeeded":{console.log("\uD83D\uDC49 Handling invoice.payment_succeeded");let e=t.data.object;if(!e.subscription){console.log("Skipping non-subscription invoice");break}console.log(`💳 Invoice payment succeeded: ${e.id}, Amount: ${e.amount_paid}, Subscription: ${e.subscription}`);let{data:r}=await s.from("subscriptions").update({status:"active",current_period_start:d(e.period_start),current_period_end:d(e.period_end)}).eq("stripe_subscription_id",e.subscription).select("id").single();r?.id&&(await s.from("licenses").update({status:"active",expiry_date:d(e.period_end)}).eq("subscription_id",r.id).in("status",["past_due","inactive","pending"]),console.log(`✅ Updated subscription and licenses to active after payment`));break}case"invoice.payment_failed":{console.log("\uD83D\uDC49 Handling invoice.payment_failed");let e=t.data.object;if(!e.subscription)break;await s.from("subscriptions").update({status:"past_due"}).eq("stripe_subscription_id",e.subscription);break}case"payment_intent.succeeded":{console.log("\uD83D\uDC49 Handling payment_intent.succeeded");let e=t.data.object;if(console.log(`💳 Payment succeeded: ${e.id}, Amount: ${e.amount}, Customer: ${e.customer}`),e.customer){let{data:t}=await s.from("profiles").select("id, first_name, last_name, email").eq("stripe_customer_id",e.customer).maybeSingle();if(t?.id){let i=!1,a={};if(!t.first_name||""===t.first_name||!t.last_name||""===t.last_name){if(e.latest_charge)try{let s=await r.charges.retrieve(e.latest_charge);if(s.billing_details?.name&&""!==s.billing_details.name.trim()){let{firstName:r,lastName:o}=p(s.billing_details.name);console.log(`🔍 PAYMENT_INTENT CHARGE NAME EXTRACTION:`,{paymentIntentId:e.id,chargeId:s.id,billingName:s.billing_details.name,parsedFirstName:r||"NO_FIRST",parsedLastName:o||"NO_LAST"}),(!t.first_name||""===t.first_name)&&r&&(a.first_name=r,i=!0),(!t.last_name||""===t.last_name)&&o&&(a.last_name=o,i=!0)}}catch(e){console.error("❌ Error fetching charge from payment intent:",e)}if(i){console.log(`🔄 Updating profile ${t.id} with payment intent name data:`,a);let{error:e}=await s.from("profiles").update(a).eq("id",t.id);e?console.error("❌ Error updating profile with payment intent name data:",e):console.log(`✅ Successfully updated profile with name data from payment intent`)}}let{data:o}=await s.from("subscriptions").select("id, quantity, stripe_subscription_id").eq("user_id",t.id).eq("status","incomplete").maybeSingle();if(o?.stripe_subscription_id){console.log(`🔄 Fetching complete subscription details from Stripe: ${o.stripe_subscription_id}`);let e=await r.subscriptions.retrieve(o.stripe_subscription_id);console.log(`📅 Payment Success Billing Period Debug:`,{current_period_start:e.current_period_start,current_period_end:e.current_period_end,current_period_start_iso:d(e.current_period_start),current_period_end_iso:d(e.current_period_end),created:e.created,start_date:e.start_date,billing_cycle_anchor:e.billing_cycle_anchor,status:e.status});let t=d(e.current_period_start),i=d(e.current_period_end);if(!t||!i){console.log(`⚠️ Missing billing period data after payment success, using fallback calculation...`);let s=e.items.data[0],a=await r.prices.retrieve(s.price.id),o=new Date(1e3*e.created);if(t=t||o.toISOString(),!i){let e=new Date(o);a.recurring?.interval==="year"?e.setFullYear(e.getFullYear()+(a.recurring?.interval_count||1)):a.recurring?.interval==="month"?e.setMonth(e.getMonth()+(a.recurring?.interval_count||1)):e.setMonth(e.getMonth()+1),i=e.toISOString()}console.log(`✅ Calculated payment success billing periods:`,{current_period_start:t,current_period_end:i})}let{data:a,error:n}=await s.from("subscriptions").update({status:"active",current_period_start:t,current_period_end:i,trial_start:d(e.trial_start),trial_end:d(e.trial_end),cancel_at_period_end:e.cancel_at_period_end}).eq("id",o.id).select("id, quantity").single();if(n||!a?.id){console.error("❌ Error updating subscription with billing periods:",n);break}if(console.log(`✅ Updated subscription with complete billing period data: ${a.id}`),console.log(`🔄 Updating licenses to active for subscription: ${a.id}`),(a.quantity||1)>1)console.log(`ℹ️ Team subscription detected, keeping licenses inactive for assignment`);else{let t=i||_(e,null);await s.from("licenses").update({status:"active",expiry_date:t}).eq("subscription_id",a.id).in("status",["pending","inactive"]),console.log(`✅ Updated individual subscription and licenses to active status with expiry: ${t}`)}}}}break}case"charge.succeeded":{console.log("\uD83D\uDC49 Handling charge.succeeded");let e=t.data.object;if(console.log(`💳 Charge succeeded: ${e.id}, Amount: ${e.amount}, Customer: ${e.customer}`),e.customer){let{data:t}=await s.from("profiles").select("id, first_name, last_name, email").eq("stripe_customer_id",e.customer).maybeSingle();if(t?.id){let i=!1,a={};if(e.billing_details?.name&&""!==e.billing_details.name.trim()){let{firstName:r,lastName:s}=p(e.billing_details.name);console.log(`🔍 CHARGE BILLING NAME EXTRACTION:`,{chargeId:e.id,originalBillingName:e.billing_details.name,parsedFirstName:r||"NO_FIRST",parsedLastName:s||"NO_LAST",currentProfileFirstName:t.first_name||"EMPTY_PROFILE_FIRST",currentProfileLastName:t.last_name||"EMPTY_PROFILE_LAST"}),(!t.first_name||""===t.first_name)&&r&&(a.first_name=r,i=!0),(!t.last_name||""===t.last_name)&&s&&(a.last_name=s,i=!0)}let o=e.metadata?.customerEmail||e.metadata?.email||e.receipt_email;if(o&&(!t.first_name||""===t.first_name)){let e=o.split("@")[0];if(e.includes(".")&&!e.includes("-")&&!e.includes("test")){let t=e.split(".");if(2===t.length&&t[0].length>1&&t[1].length>1){let e=t[0].charAt(0).toUpperCase()+t[0].slice(1).toLowerCase(),r=t[1].charAt(0).toUpperCase()+t[1].slice(1).toLowerCase();console.log(`🔍 EMAIL-BASED NAME EXTRACTION:`,{email:o,extractedFirstName:e,extractedLastName:r}),a.first_name||(a.first_name=e,i=!0),a.last_name||(a.last_name=r,i=!0)}}}if(i){console.log(`🔄 Updating profile ${t.id} with charge billing details:`,a);let{error:e}=await s.from("profiles").update(a).eq("id",t.id);e?console.error("❌ Error updating profile with charge billing details:",e):console.log(`✅ Successfully updated profile with name data from charge billing details`)}let n=null;if(e.invoice){let t=await r.invoices.retrieve(e.invoice);if(t.subscription){let{data:e}=await s.from("subscriptions").select("id").eq("stripe_subscription_id",t.subscription).maybeSingle();n=e?.id||null}}await s.from("charge_receipts").upsert({stripe_charge_id:e.id,stripe_payment_intent_id:e.payment_intent,subscription_id:n,user_id:t.id,amount:e.amount,currency:e.currency,receipt_url:e.receipt_url,receipt_number:e.receipt_number,status:e.status,payment_method_type:e.payment_method_details?.type||null,payment_method_brand:e.payment_method_details?.card?.brand||null,payment_method_last4:e.payment_method_details?.card?.last4||null,failure_reason:e.failure_message||null,invoice_id:e.metadata?.invoice_id||null,period_start:e.metadata?.period_start?new Date(e.metadata.period_start).toISOString():null,period_end:e.metadata?.period_end?new Date(e.metadata.period_end).toISOString():null},{onConflict:"stripe_charge_id"}),console.log(`✅ Stored receipt for charge: ${e.id}`)}}break}case"charge.updated":{console.log("\uD83D\uDC49 Handling charge.updated");let e=t.data.object;if(console.log(`🔄 Charge updated: ${e.id}, Status: ${e.status}`),e.customer){let{data:t}=await s.from("profiles").select("id").eq("stripe_customer_id",e.customer).maybeSingle();if(t?.id){let i=null;if(e.invoice){let t=await r.invoices.retrieve(e.invoice);if(t.subscription){let{data:e}=await s.from("subscriptions").select("id").eq("stripe_subscription_id",t.subscription).maybeSingle();i=e?.id||null}}await s.from("charge_receipts").upsert({stripe_charge_id:e.id,stripe_payment_intent_id:e.payment_intent,subscription_id:i,user_id:t.id,amount:e.amount,currency:e.currency,receipt_url:e.receipt_url,receipt_number:e.receipt_number,status:e.status},{onConflict:"stripe_charge_id"}),console.log(`✅ Updated receipt for charge: ${e.id}`)}}break}case"invoice.paid":{console.log("\uD83D\uDC49 Handling invoice.paid");let e=t.data.object;console.log(`💳 Invoice paid: ${e.id}, Amount: ${e.amount_paid}, Customer: ${e.customer}`);let{data:r}=await s.from("enterprise_invoices").select("*, enterprise_customer:enterprise_customer_id(*)").eq("stripe_invoice_id",e.id).maybeSingle();if(r){console.log(`🏢 Processing enterprise invoice payment for: ${r.enterprise_customer.company_name}`),await s.from("enterprise_invoices").update({status:"paid",updated_at:new Date().toISOString()}).eq("id",r.id),await s.from("enterprise_customers").update({status:"active",updated_at:new Date().toISOString()}).eq("id",r.enterprise_customer_id);let{count:e}=await s.from("licenses").select("*",{count:"exact",head:!0}).eq("enterprise_customer_id",r.enterprise_customer_id),t=r.enterprise_customer.license_quantity,i=e||0;if(i<t){let e=t-i,a=Array.from({length:e},()=>({enterprise_customer_id:r.enterprise_customer_id,product_id:"enterprise-license",status:"unassigned",license_tier:r.enterprise_customer.license_tier,expiry_date:r.enterprise_customer.contract_end_date,max_activations:1}));await s.from("licenses").insert(a),console.log(`✅ Created ${e} enterprise licenses`)}else await s.from("licenses").update({status:"unassigned",expiry_date:r.enterprise_customer.contract_end_date}).eq("enterprise_customer_id",r.enterprise_customer_id).in("status",["inactive","pending"]),console.log(`✅ Activated ${i} enterprise licenses`);console.log(`✅ Enterprise invoice processing completed for: ${r.enterprise_customer.company_name}`)}break}default:console.log("\uD83D\uDC40 Unhandled event type:",t.type)}}catch(e){console.error("❌ Webhook processing error:",e);try{await s.from("webhook_events").update({status:"failed",error_message:e instanceof Error?e.message:String(e),updated_at:new Date().toISOString()}).eq("stripe_event_id",t.id)}catch(e){console.error("Failed to update webhook status to failed:",e)}return new n.NextResponse("Webhook handler error",{status:500})}try{await s.from("webhook_events").update({status:"completed",updated_at:new Date().toISOString()}).eq("stripe_event_id",t.id)}catch(e){console.error("Failed to update webhook status to completed:",e)}return n.NextResponse.json({received:!0})}let b=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/webhooks/stripe/route",pathname:"/api/webhooks/stripe",filename:"route",bundlePath:"app/api/webhooks/stripe/route"},resolvedPagePath:"/home/<USER>/work/QuantBoost/QuantBoost/QuantBoost_Frontend/src/app/api/webhooks/stripe/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:h,workUnitAsyncStorage:y,serverHooks:v}=b;function w(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:y})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,580,6345,7877],()=>r(58897));module.exports=i})();
❌ Secret scan failed
frontend_dist/.next/server/app/api/webhooks/stripe/health/route.js:1:(()=>{var e={};e.id=3453,e.ids=[3453],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},41931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>u});var o=r(96559),n=r(48088),a=r(37719),i=r(32190);async function p(){return i.NextResponse.json({status:"healthy",timestamp:new Date().toISOString(),endpoint:"/api/webhooks/stripe",environment:{hasStripeSecret:!!process.env.STRIPE_SECRET_KEY,hasWebhookSecret:!!process.env.STRIPE_WEBHOOK_SECRET,hasSupabaseUrl:!0,hasServiceKey:!!process.env.SUPABASE_SERVICE_KEY}})}async function u(){return i.NextResponse.json({message:"Webhook endpoint is accessible",method:"POST",timestamp:new Date().toISOString()})}let c=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/webhooks/stripe/health/route",pathname:"/api/webhooks/stripe/health",filename:"route",bundlePath:"app/api/webhooks/stripe/health/route"},resolvedPagePath:"/home/<USER>/work/QuantBoost/QuantBoost/QuantBoost_Frontend/src/app/api/webhooks/stripe/health/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:l}=c;function S(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580],()=>r(41931));module.exports=s})();
frontend_dist/.next/server/app/api/checkout/update-payment-intent/route.js:1:(()=>{var e={};e.id=2494,e.ids=[2494],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34960:(e,t,r)=>{"use strict";function s(e,t){return process.env[e]||process.env[e]||t}r.d(t,{Km:()=>n,_O:()=>a,db:()=>o});let n={STRIPE_SECRET_KEY:s("STRIPE_SECRET_KEY"),STRIPE_WEBHOOK_SECRET:s("STRIPE_WEBHOOK_SECRET"),NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:s("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"),NEXT_PUBLIC_SUPABASE_URL:s("NEXT_PUBLIC_SUPABASE_URL"),NEXT_PUBLIC_SUPABASE_ANON_KEY:s("NEXT_PUBLIC_SUPABASE_ANON_KEY"),SUPABASE_SERVICE_KEY:s("SUPABASE_SERVICE_KEY"),NEXT_PUBLIC_AZURE_API_URL:s("NEXT_PUBLIC_AZURE_API_URL"),NEXT_PUBLIC_BASE_URL:s("NEXT_PUBLIC_BASE_URL"),NODE_ENV:s("NODE_ENV","development")};function a(){let e=["STRIPE_SECRET_KEY","NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"].filter(e=>!n[e]);return{isValid:0===e.length,missing:e}}function o(){return{nodeEnv:n.NODE_ENV,hasStripeSecret:!!n.STRIPE_SECRET_KEY,hasStripePublishable:!!n.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,hasSupabaseUrl:!!n.NEXT_PUBLIC_SUPABASE_URL,hasSupabaseAnon:!!n.NEXT_PUBLIC_SUPABASE_ANON_KEY,hasSupabaseService:!!n.SUPABASE_SERVICE_KEY,stripeValidation:a(),supabaseValidation:function(){let e=["NEXT_PUBLIC_SUPABASE_URL","NEXT_PUBLIC_SUPABASE_ANON_KEY","SUPABASE_SERVICE_KEY"].filter(e=>!n[e]);return{isValid:0===e.length,missing:e}}(),timestamp:new Date().toISOString()}}},38927:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>_,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{POST:()=>p});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(97877),E=r(34960);async function p(e){let t=(0,E._O)();if(!t.isValid)return console.error("❌ Stripe configuration validation failed:",t.missing),i.NextResponse.json({error:"Payment system configuration error",details:t.missing,debug:(0,E.db)()},{status:500});let r=new u.A(E.Km.STRIPE_SECRET_KEY,{apiVersion:"2025-03-31.basil"});try{let{paymentIntentId:t,email:s,customerInfo:n}=await e.json();if(!t)return i.NextResponse.json({error:"Payment Intent ID is required"},{status:400});if(!s)return i.NextResponse.json({error:"Email is required"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))return console.error(`❌ Invalid email format: ${s}`),i.NextResponse.json({error:"Invalid email format"},{status:400});if("<EMAIL>"===s)return console.error(`❌ Rejected guest email in payment intent update`),i.NextResponse.json({error:"Guest checkout not supported. Please provide a valid email address."},{status:400});console.log(`🔄 Updating Payment Intent ${t} with email: ${s}`);let a=await r.paymentIntents.retrieve(t);if(!a)return i.NextResponse.json({error:"Payment Intent not found"},{status:404});let o=a.customer;if(o){let e={email:s};n&&((n.firstName||n.lastName)&&(e.name=`${n.firstName||""} ${n.lastName||""}`.trim()),n.phone&&(e.phone=n.phone),n.addressLine1&&n.city&&n.country&&(e.address={line1:n.addressLine1,line2:n.addressLine2||void 0,city:n.city,state:n.state||void 0,postal_code:n.postalCode||void 0,country:n.country})),console.log(`🔄 Updating customer ${o} with email and info`),await r.customers.update(o,e),console.log(`✅ Updated customer ${o} with email: ${s}`)}let u=await r.paymentIntents.update(t,{receipt_email:s,metadata:{...a.metadata,email:s,customerEmail:s,emailCaptured:"true",emailCapturedAt:new Date().toISOString()}});return console.log(`✅ Updated Payment Intent ${t} with email: ${s}`),i.NextResponse.json({success:!0,paymentIntentId:u.id,customerId:o,email:s})}catch(e){return console.error("Error updating payment intent:",e),console.error("Environment check:",{hasStripeSecret:!!process.env.STRIPE_SECRET_KEY,hasSupabaseUrl:!0,hasSupabaseKey:!!process.env.SUPABASE_SERVICE_KEY}),i.NextResponse.json({error:"Failed to update payment intent",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let _=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/checkout/update-payment-intent/route",pathname:"/api/checkout/update-payment-intent",filename:"route",bundlePath:"app/api/checkout/update-payment-intent/route"},resolvedPagePath:"/home/<USER>/work/QuantBoost/QuantBoost/QuantBoost_Frontend/src/app/api/checkout/update-payment-intent/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=_;function S(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7877],()=>r(38927));module.exports=s})();
frontend_dist/.next/server/app/api/checkout/create-payment-intent/route.js:1:(()=>{var e={};e.id=6327,e.ids=[6327],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34960:(e,t,s)=>{"use strict";function r(e,t){return process.env[e]||process.env[e]||t}s.d(t,{Km:()=>i,_O:()=>n,db:()=>a});let i={STRIPE_SECRET_KEY:r("STRIPE_SECRET_KEY"),STRIPE_WEBHOOK_SECRET:r("STRIPE_WEBHOOK_SECRET"),NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:r("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"),NEXT_PUBLIC_SUPABASE_URL:r("NEXT_PUBLIC_SUPABASE_URL"),NEXT_PUBLIC_SUPABASE_ANON_KEY:r("NEXT_PUBLIC_SUPABASE_ANON_KEY"),SUPABASE_SERVICE_KEY:r("SUPABASE_SERVICE_KEY"),NEXT_PUBLIC_AZURE_API_URL:r("NEXT_PUBLIC_AZURE_API_URL"),NEXT_PUBLIC_BASE_URL:r("NEXT_PUBLIC_BASE_URL"),NODE_ENV:r("NODE_ENV","development")};function n(){let e=["STRIPE_SECRET_KEY","NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"].filter(e=>!i[e]);return{isValid:0===e.length,missing:e}}function a(){return{nodeEnv:i.NODE_ENV,hasStripeSecret:!!i.STRIPE_SECRET_KEY,hasStripePublishable:!!i.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,hasSupabaseUrl:!!i.NEXT_PUBLIC_SUPABASE_URL,hasSupabaseAnon:!!i.NEXT_PUBLIC_SUPABASE_ANON_KEY,hasSupabaseService:!!i.SUPABASE_SERVICE_KEY,stripeValidation:n(),supabaseValidation:function(){let e=["NEXT_PUBLIC_SUPABASE_URL","NEXT_PUBLIC_SUPABASE_ANON_KEY","SUPABASE_SERVICE_KEY"].filter(e=>!i[e]);return{isValid:0===e.length,missing:e}}(),timestamp:new Date().toISOString()}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},93841:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>S,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>E});var r={};s.r(r),s.d(r,{POST:()=>p});var i=s(96559),n=s(48088),a=s(37719),o=s(32190),u=s(97877),c=s(34960);let d={price_1RC3HTE6FvhUKV1bE9D6zf6e:{name:"Annual Plan",description:"Full access, billed annually.",productId:"prod_S6Fn893jGxRhKk"},price_1RCQeBE6FvhUKV1bUN94Oihf:{name:"Quarterly Plan",description:"Full access, billed quarterly.",productId:"prod_S6Fn893jGxRhKk"}};async function p(e){let t=Date.now();(0,c.db)();let s=(0,c._O)();if(!s.isValid)return console.error("stripe.config.invalid",s),o.NextResponse.json({error:"Payment service unavailable"},{status:500});let r=new u.A(c.Km.STRIPE_SECRET_KEY,{apiVersion:"2025-03-31.basil"});try{let s;let{priceId:i,userId:n,email:a,quantity:u=1,customerInfo:c}=await e.json()||{};if(!i)return o.NextResponse.json({error:"Missing priceId"},{status:400});let p=d[i];if(!p)return o.NextResponse.json({error:"Invalid priceId"},{status:400});if(!a||"string"!=typeof a||!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)||"<EMAIL>"===a)return o.NextResponse.json({error:"Valid email required"},{status:400});let l=null,_=await r.customers.list({email:a,limit:1});if(_.data.length>0){l=_.data[0];let e={};if(c?.firstName&&c?.lastName&&(e.name=`${c.firstName} ${c.lastName}`),c?.phone&&(e.phone=c.phone),c?.addressLine1&&c?.city&&c?.country&&(e.address={line1:c.addressLine1,line2:c.addressLine2||void 0,city:c.city,state:c.state||void 0,postal_code:c.postalCode||void 0,country:c.country}),l&&Object.keys(e).length>0)try{l=await r.customers.update(l.id,e)}catch{}}else l=await r.customers.create({email:a,name:c?.firstName&&c?.lastName?`${c.firstName} ${c.lastName}`:void 0,phone:c?.phone||void 0,address:c?.addressLine1&&c?.city&&c?.country?{line1:c.addressLine1,line2:c.addressLine2||void 0,city:c.city,state:c.state||void 0,postal_code:c.postalCode||void 0,country:c.country}:void 0,metadata:{app:"quantboost",userId:n||""}});if(!l)return o.NextResponse.json({error:"Customer creation failed"},{status:500});let E=await r.subscriptions.list({customer:l.id,status:"all",limit:10,expand:["data.latest_invoice.payment_intent"]}),m=null;for(let e of E.data)if(e.items.data.some(e=>e.price.id===i)&&["incomplete","trialing","active","past_due"].includes(e.status)){m=e;break}if(m&&m.latest_invoice)s=m,console.log(JSON.stringify({msg:"subscription.reuse",subId:s.id,status:s.status,customerId:l.id}));else{let e=`sub:${l.id}:${i}:${u}`;s=await r.subscriptions.create({customer:l.id,items:[{price:i,quantity:u}],payment_behavior:"default_incomplete",payment_settings:{save_default_payment_method:"on_subscription",payment_method_types:["card","link"]},metadata:{priceId:i,email:a,userId:n||"",productName:p.name,quantity:String(u),app:"quantboost"},expand:["latest_invoice.payment_intent"]},{idempotencyKey:e}),console.log(JSON.stringify({msg:"subscription.create",subId:s.id,customerId:l.id,idemKey:e}))}let S=null;if(!(S="string"==typeof s.latest_invoice?await r.invoices.retrieve(s.latest_invoice,{expand:["payment_intent"]}):s.latest_invoice))return o.NextResponse.json({error:"Invoice missing"},{status:500});let I=null,y=S.payment_intent;return y&&(I="string"==typeof y?await r.paymentIntents.retrieve(y):y),I||(I=await r.paymentIntents.create({amount:S.amount_due,currency:S.currency,customer:l.id,metadata:{subscription_id:s.id,invoice_id:S.id||"",email:a,priceId:i,productName:p.name,app:"quantboost"},automatic_payment_methods:{enabled:!0}}),console.log(JSON.stringify({msg:"payment_intent.create.manual",id:I.id}))),o.NextResponse.json({clientSecret:I.client_secret,subscriptionId:s.id,paymentIntentId:I.id,customerId:l.id,productName:p.name,description:p.description,reusedSubscription:!!m,durationMs:Date.now()-t})}catch(e){return console.error("subscription.route.error",{message:e?.message,stack:e?.stack}),o.NextResponse.json({error:"Failed to create subscription"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/checkout/create-payment-intent/route",pathname:"/api/checkout/create-payment-intent",filename:"route",bundlePath:"app/api/checkout/create-payment-intent/route"},resolvedPagePath:"/home/<USER>/work/QuantBoost/QuantBoost/QuantBoost_Frontend/src/app/api/checkout/create-payment-intent/route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:_,workUnitAsyncStorage:E,serverHooks:m}=l;function S(){return(0,a.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:E})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,7877],()=>s(93841));module.exports=r})();
frontend_dist/.next/server/app/api/checkout/create-setup-intent/route.js:1:(()=>{var e={};e.id=3120,e.ids=[3120],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21700:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>U,routeModule:()=>p,serverHooks:()=>P,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{POST:()=>o});var n=r(96559),a=r(48088),E=r(37719),i=r(32190),_=r(97877),u=r(34960);async function o(e){if(!(0,u._O)().isValid)return i.NextResponse.json({error:"Payment service unavailable"},{status:500});let t=new _.A(u.Km.STRIPE_SECRET_KEY,{apiVersion:"2025-03-31.basil"});try{let e=await t.paymentIntents.create({amount:100,currency:"usd",automatic_payment_methods:{enabled:!0},metadata:{app:"quantboost",setup:"true"}});return i.NextResponse.json({clientSecret:e.client_secret})}catch(e){return i.NextResponse.json({error:e?.message||"Failed to create setup intent"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/checkout/create-setup-intent/route",pathname:"/api/checkout/create-setup-intent",filename:"route",bundlePath:"app/api/checkout/create-setup-intent/route"},resolvedPagePath:"/home/<USER>/work/QuantBoost/QuantBoost/QuantBoost_Frontend/src/app/api/checkout/create-setup-intent/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:S,workUnitAsyncStorage:c,serverHooks:P}=p;function U(){return(0,E.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:c})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34960:(e,t,r)=>{"use strict";function s(e,t){return process.env[e]||process.env[e]||t}r.d(t,{Km:()=>n,_O:()=>a,db:()=>E});let n={STRIPE_SECRET_KEY:s("STRIPE_SECRET_KEY"),STRIPE_WEBHOOK_SECRET:s("STRIPE_WEBHOOK_SECRET"),NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:s("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"),NEXT_PUBLIC_SUPABASE_URL:s("NEXT_PUBLIC_SUPABASE_URL"),NEXT_PUBLIC_SUPABASE_ANON_KEY:s("NEXT_PUBLIC_SUPABASE_ANON_KEY"),SUPABASE_SERVICE_KEY:s("SUPABASE_SERVICE_KEY"),NEXT_PUBLIC_AZURE_API_URL:s("NEXT_PUBLIC_AZURE_API_URL"),NEXT_PUBLIC_BASE_URL:s("NEXT_PUBLIC_BASE_URL"),NODE_ENV:s("NODE_ENV","development")};function a(){let e=["STRIPE_SECRET_KEY","NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"].filter(e=>!n[e]);return{isValid:0===e.length,missing:e}}function E(){return{nodeEnv:n.NODE_ENV,hasStripeSecret:!!n.STRIPE_SECRET_KEY,hasStripePublishable:!!n.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,hasSupabaseUrl:!!n.NEXT_PUBLIC_SUPABASE_URL,hasSupabaseAnon:!!n.NEXT_PUBLIC_SUPABASE_ANON_KEY,hasSupabaseService:!!n.SUPABASE_SERVICE_KEY,stripeValidation:a(),supabaseValidation:function(){let e=["NEXT_PUBLIC_SUPABASE_URL","NEXT_PUBLIC_SUPABASE_ANON_KEY","SUPABASE_SERVICE_KEY"].filter(e=>!n[e]);return{isValid:0===e.length,missing:e}}(),timestamp:new Date().toISOString()}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,7877],()=>r(21700));module.exports=s})();
frontend_dist/.next/server/app/api/debug/env/route.js:1:(()=>{var e={};e.id=8683,e.ids=[8683],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51442:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>c,routeModule:()=>p,serverHooks:()=>d,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>_});var s={};t.r(s),t.d(s,{GET:()=>i});var n=t(96559),a=t(48088),o=t(37719),u=t(32190);async function i(){let e={STRIPE_SECRET_KEY:!!process.env.STRIPE_SECRET_KEY,STRIPE_WEBHOOK_SECRET:!!process.env.STRIPE_WEBHOOK_SECRET,NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:!0,NEXT_PUBLIC_SUPABASE_URL:!0,NEXT_PUBLIC_SUPABASE_ANON_KEY:!0,SUPABASE_SERVICE_KEY:!!process.env.SUPABASE_SERVICE_KEY,NEXT_PUBLIC_AZURE_API_URL:!0,NEXT_PUBLIC_BASE_URL:!0,stripe_secret_partial:process.env.STRIPE_SECRET_KEY?.substring(0,10)||"NOT_SET",supabase_url_partial:"***".substring(0,20)||0,supabase_service_partial:process.env.SUPABASE_SERVICE_KEY?.substring(0,10)||"NOT_SET",NODE_ENV:"production",runtime:"azure-static-web-apps",timestamp:new Date().toISOString()};return u.NextResponse.json(e)}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/debug/env/route",pathname:"/api/debug/env",filename:"route",bundlePath:"app/api/debug/env/route"},resolvedPagePath:"/home/<USER>/work/QuantBoost/QuantBoost/QuantBoost_Frontend/src/app/api/debug/env/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:_,serverHooks:d}=p;function c(){return(0,o.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:_})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(51442));module.exports=s})();
Error: Process completed with exit code 1.