'use client';

import { useState, useEffect, memo, useMemo, useCallback } from 'react';

import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, LinkAuthenticationElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { Button, Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui';
import { Shield, Lock, CreditCard, User, Users } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
// Legacy validation imports removed.

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

// Robust email regex (requires at least two-letter TLD)
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/i;

// Product details - Individual and Team plans use same price IDs
const PRODUCTS = {
  'price_1RC3HTE6FvhUKV1bE9D6zf6e': {
    name: 'Annual',
    price: '$120',
    period: 'per year',
    description: 'Full access, billed annually.',
    features: ['All Excel Modules', 'All PowerPoint Modules', 'Standard Support'],
    unitPrice: 120,
  },
  'price_1RCQeBE6FvhUKV1bUN94Oihf': {
    name: 'Quarterly',
    price: '$45',
    period: 'per quarter',
    description: 'Full access, billed quarterly.',
    features: ['All Excel Modules', 'All PowerPoint Modules', 'Standard Support'],
    unitPrice: 45,
  },
};

// Team plans use the same price IDs but with quantity > 1
// We'll determine if it's a team plan based on the URL parameter ?quantity=X where X > 1
const TEAM_PRICE_IDS = [
  'price_1RC3HTE6FvhUKV1bE9D6zf6e', // Annual plan used for teams
  'price_1RCQeBE6FvhUKV1bUN94Oihf', // Quarterly plan used for teams
];




// Email capture section (phase 1)
const EmailCaptureSection = memo(function EmailCaptureSection({ onComplete }: { onComplete: (email: string) => void }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
        <User className="h-4 w-4" />
        Enter your email to create your secure account
      </div>
      <div className="p-4 border rounded-lg bg-gray-50/50">
        <LinkAuthenticationElement
          onChange={(event) => {
            const ev: any = event;
            const raw = ev?.value?.email?.trim();
            if (ev?.complete && raw && EMAIL_REGEX.test(raw)) {
              onComplete(raw.toLowerCase());
            }
          }}
          options={{ defaultValues: { email: '' } }}
        />
      </div>
      <p className="text-xs text-gray-500">We'll use this email for your account, billing, and secure access.</p>
    </div>
  );
});

interface CheckoutFormProps {
  priceId: string;
  product: typeof PRODUCTS[keyof typeof PRODUCTS];
  initialQuantity?: number;
  onQuantityChange?: (quantity: number) => void;
}

interface CheckoutFormInnerProps extends CheckoutFormProps {
  initialQuantity?: number;
  email: string;
  onQuantityChange?: (quantity: number) => void;
}

// Legacy EmailCollectionStepProps removed

// Helper function to extract numeric price from string format
const extractNumericPrice = (priceString: string): number => {
  const numericValue = priceString.replace(/[^0-9.]/g, '');
  return parseFloat(numericValue) || 0;
};

// Helper function to calculate total price for display
const calculateDisplayPrice = (product: typeof PRODUCTS[keyof typeof PRODUCTS], quantity: number, isTeamPlan: boolean): string => {
  const unitPrice = product.unitPrice;
  if (isTeamPlan && quantity > 1) {
    const totalPrice = unitPrice * quantity;
    return `$${totalPrice}`;
  }
  return product.price;
};

// Helper function to get dynamic product information based on team vs individual
const getDynamicProductInfo = (baseProduct: typeof PRODUCTS[keyof typeof PRODUCTS], quantity: number, isTeamPlan: boolean) => {
  const planType = isTeamPlan ? 'Team' : 'Individual';
  const displayPrice = calculateDisplayPrice(baseProduct, quantity, isTeamPlan);
  
  // Create features array with appropriate support level
  const baseFeatures = baseProduct.features.filter(feature => feature !== 'Standard Support');
  const features = isTeamPlan 
    ? [...baseFeatures, 'Team License Management', 'Priority Support']
    : [...baseFeatures, 'Standard Support'];
  
  return {
    name: `${planType} ${baseProduct.name}`,
    price: displayPrice,
    period: baseProduct.period, // Keep the original period format
    description: isTeamPlan 
      ? `Full access for ${quantity} user${quantity > 1 ? 's' : ''}, billed ${baseProduct.name.toLowerCase()}.`
      : baseProduct.description,
    features: features,
    unitPrice: baseProduct.unitPrice,
    quantity: quantity,
    totalPrice: baseProduct.unitPrice * quantity
  };
};

// (Legacy email collection JSX removed)

// New two-stage checkout form with setup + final client secrets
function CheckoutForm({ priceId, product, initialQuantity = 1, onQuantityChange }: CheckoutFormProps) {
  const [email, setEmail] = useState('');
  const [emailComplete, setEmailComplete] = useState(false);
  const [setupClientSecret, setSetupClientSecret] = useState<string | null>(null);
  const [finalClientSecret, setFinalClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false); // final intent creation
  const [error, setError] = useState<string | null>(null);
  const [teamSize, setTeamSize] = useState(initialQuantity);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  const [marketingOptIn, setMarketingOptIn] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const isTeamPlan = teamSize > 1;
  const dynamicProduct = getDynamicProductInfo(product, teamSize, isTeamPlan);
  const displayPrice = dynamicProduct.price;

  // Fetch setup intent on mount
  useEffect(() => {
    const fetchSetup = async () => {
      if (setupClientSecret) return;
      try {
        const res = await fetch('/api/checkout/create-setup-intent', { method: 'POST' });
        if (!res.ok) throw new Error('Failed to initialize checkout.');
        const data = await res.json();
        setSetupClientSecret(data.clientSecret);
      } catch (e: any) {
        setError(e.message || 'Failed to initialize checkout');
      }
    };
    fetchSetup();
  }, [setupClientSecret]);

  // Create final payment intent once email captured
  useEffect(() => {
    const createFinal = async () => {
      if (!emailComplete || !email || !EMAIL_REGEX.test(email) || finalClientSecret || isLoading) return;
      setIsLoading(true);
      setError(null);
      try {
        const res = await fetch('/api/checkout/create-payment-intent', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ priceId, quantity: teamSize, email })
        });
        if (!res.ok) {
          const txt = await res.text();
          throw new Error(txt || 'Failed to initialize payment');
        }
        const data = await res.json();
        setFinalClientSecret(data.clientSecret);
      } catch (e: any) {
        setError(e.message || 'Failed to initialize payment');
      } finally {
        setIsLoading(false);
      }
    };
    createFinal();
  }, [emailComplete, email, finalClientSecret, isLoading, priceId, teamSize]);

  const handleTeamSizeChange = (n: number) => {
    if (finalClientSecret) return; // lock when final secret obtained
    setTeamSize(n);
    onQuantityChange?.(n);
  };

  const clientSecret = finalClientSecret || setupClientSecret;
  if (!clientSecret) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
        Loading checkout…
      </div>
    );
  }

  return (
    <Elements stripe={stripePromise} options={{ clientSecret }} key={clientSecret}>
      <div className="space-y-6">
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md text-sm text-red-700">
            <div className="flex justify-between items-start gap-4">
              <span>{error}</span>
              <button onClick={() => setError('')} className="text-red-600 hover:underline text-xs">Dismiss</button>
            </div>
          </div>
        )}

        {!finalClientSecret && (
          <EmailCaptureSection onComplete={(em) => { setEmail(em); setEmailComplete(true); }} />
        )}

        {emailComplete && !finalClientSecret && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            Initializing secure payment…
          </div>
        )}

        {!finalClientSecret && isTeamPlan && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
              <Users className="h-4 w-4" />
              Team Size
            </div>
            <div className="flex items-center gap-3">
              <input
                type="range"
                min={1}
                max={50}
                value={teamSize}
                disabled={!!finalClientSecret}
                onChange={(e) => handleTeamSizeChange(parseInt(e.target.value) || 1)}
                className="flex-1"
              />
              <span className="text-sm w-10 text-right">{teamSize}</span>
            </div>
          </div>
        )}

        {finalClientSecret && (
            <CheckoutPaymentForm
              displayPrice={displayPrice}
              termsAccepted={termsAccepted}
              setTermsAccepted={setTermsAccepted}
              privacyAccepted={privacyAccepted}
              setPrivacyAccepted={setPrivacyAccepted}
              marketingOptIn={marketingOptIn}
              setMarketingOptIn={setMarketingOptIn}
              isSubmitting={isSubmitting}
              setIsSubmitting={setIsSubmitting}
              email={email}
              router={router}
              setError={setError}
            />
        )}
      </div>
    </Elements>
  );
}

function CheckoutPaymentForm({ displayPrice, termsAccepted, setTermsAccepted, privacyAccepted, setPrivacyAccepted, marketingOptIn, setMarketingOptIn, isSubmitting, setIsSubmitting, email, router, setError }: {
  displayPrice: string;
  termsAccepted: boolean;
  setTermsAccepted: (v: boolean) => void;
  privacyAccepted: boolean;
  setPrivacyAccepted: (v: boolean) => void;
  marketingOptIn: boolean;
  setMarketingOptIn: (v: boolean) => void;
  isSubmitting: boolean;
  setIsSubmitting: (v: boolean) => void;
  email: string;
  router: any;
  setError: (msg: string) => void;
}) {
  const stripe = useStripe();
  const elements = useElements();

  const confirm = useCallback(async () => {
    if (!stripe || !elements) return;
    // Defensive: ensure valid email before confirming
    if (!EMAIL_REGEX.test(email)) {
      setError('Please enter a valid email address (e.g. <EMAIL>).');
      return;
    }
    setIsSubmitting(true);
    setError('');
    try {
      const { error: confirmError, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/checkout/complete`,
          payment_method_data: {
            billing_details: {
              email: email,
            }
          }
        },
        redirect: 'if_required'
      });
      if (confirmError) {
        setError(confirmError.message || 'Payment failed');
      } else if (paymentIntent?.status === 'succeeded') {
        router.push(`/dashboard?payment=success&email=${encodeURIComponent(email)}&payment_intent=${paymentIntent.id}`);
      }
    } catch (e: any) {
      setError(e.message || 'Payment failed');
    } finally {
      setIsSubmitting(false);
    }
  }, [stripe, elements, router, email, setError, setIsSubmitting]);

  return (
    <div className="space-y-4">
      <div className="p-4 border rounded-lg bg-gray-50/50">
        <PaymentElement
          options={{
            layout: { type: 'tabs', defaultCollapsed: false },
            fields: { billingDetails: { name: 'auto', email: 'never', address: { country: 'auto', line1: 'auto', line2: 'auto', city: 'auto', state: 'auto', postalCode: 'auto' } } },
            business: { name: 'QuantBoost' },
          }}
        />
      </div>
      <div className="space-y-3">
        <label className="flex items-start gap-2 text-xs text-gray-600">
          <input type="checkbox" className="mt-0.5" checked={termsAccepted} onChange={(e) => setTermsAccepted(e.target.checked)} />
          <span>I agree to the <a href="/terms" target="_blank" className="underline">Terms of Service</a></span>
        </label>
        <label className="flex items-start gap-2 text-xs text-gray-600">
          <input type="checkbox" className="mt-0.5" checked={privacyAccepted} onChange={(e) => setPrivacyAccepted(e.target.checked)} />
          <span>I agree to the <a href="/privacy" target="_blank" className="underline">Privacy Policy</a></span>
        </label>
        <label className="flex items-start gap-2 text-xs text-gray-600">
          <input type="checkbox" className="mt-0.5" checked={marketingOptIn} onChange={(e) => setMarketingOptIn(e.target.checked)} />
          <span>Send me occasional product updates (optional)</span>
        </label>
      </div>
      <Button
        type="button"
        onClick={confirm}
        disabled={!stripe || !elements || isSubmitting || !termsAccepted || !privacyAccepted}
        className="w-full h-12 text-base font-semibold bg-black hover:bg-gray-800 text-white"
      >
        {isSubmitting ? 'Processing…' : `Complete Purchase - ${displayPrice}`}
      </Button>
      <div className="flex items-center justify-center gap-2 text-xs text-gray-500 mt-2">
        <Shield className="h-3 w-3" />
        <span>Secured by Stripe • SSL Encrypted</span>
      </div>
    </div>
  );
}

export default function CheckoutPage() {
  // Use the optimized checkout page with React Hook Form
  return <CheckoutPageOriginal />;
}

function CheckoutPageOriginal() {
  const params = useParams();
  const searchParams = useSearchParams();
  const priceId = params.priceId as string;
  const quantityParam = searchParams.get('quantity');
  const initialQuantity = quantityParam ? parseInt(quantityParam, 10) : 1;
  
  // State to track current quantity for dynamic updates
  const [currentQuantity, setCurrentQuantity] = useState(initialQuantity);

  const baseProduct = PRODUCTS[priceId as keyof typeof PRODUCTS];
  const isTeamPlan = currentQuantity > 1; // Use current quantity instead of initial
  
  // Get dynamic product information based on team vs individual and current quantity
  const product = baseProduct ? getDynamicProductInfo(baseProduct, currentQuantity, isTeamPlan) : null;

  if (!baseProduct || !product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invalid Product</CardTitle>
            <CardDescription>
              The requested product could not be found.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link href="/pricing">Back to Pricing</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="px-4 lg:px-6 h-20 flex items-center border-b bg-white/70 backdrop-blur-sm">
        <div className="flex items-center gap-6 w-full">
          <Link href="/" className="flex items-center justify-center">
            <Image src="/QuantBoost_LogoOnly_v0.png" alt="QuantBoost Logo" width={40} height={40} />
            <span className="ml-2 text-xl font-semibold">QuantBoost</span>
          </Link>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Complete Your Purchase</h1>
          <nav className="ml-auto flex items-center gap-2">
            <Button variant="ghost" asChild>
              <Link href="/pricing">← Back to Pricing</Link>
            </Button>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-4 sm:py-6 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8 items-start">
          {/* Product Summary - Sticky Sidebar */}
          <div className="lg:col-span-2 order-2 lg:order-1">
            <div className="lg:sticky lg:top-8">
              {/* Heading moved to global header */}

              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                      <span className="text-primary-foreground font-bold text-sm">QB</span>
                    </div>
                    {product.name}
                  </CardTitle>
                  <CardDescription>{product.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <div className="text-4xl font-bold text-primary">{product.price}</div>
                      <div className="text-sm text-muted-foreground">{product.period}</div>
                      {isTeamPlan && currentQuantity > 1 && (
                        <div className="text-xs text-muted-foreground mt-1">
                          ${baseProduct.unitPrice} × {currentQuantity} users
                        </div>
                      )}
                    </div>

                    <div>
                      <h3 className="font-semibold mb-3 flex items-center gap-2">
                        <Shield className="h-4 w-4 text-primary" />
                        Included Features:
                      </h3>
                      <ul className="space-y-2">
                        {product.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-3 text-sm">
                            <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0"></div>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="border-t pt-4">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <Lock className="h-4 w-4" />
                        <span>30-day money-back guarantee</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <Shield className="h-4 w-4" />
                        <span>Cancel anytime</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <CreditCard className="h-4 w-4" />
                        <span>Secure payment processing</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Shield className="h-4 w-4" />
                        <span>Stripe Secure</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Trust Indicators */}
              <div className="text-center space-y-3">
                <div className="flex items-center justify-center gap-3 text-xs text-muted-foreground flex-wrap">
                  <div className="flex items-center gap-1">
                    <Shield className="h-3 w-3" />
                    <span>256-bit SSL</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Lock className="h-3 w-3" />
                    <span>PCI DSS</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <CreditCard className="h-3 w-3" />
                    <span>Stripe Secure</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Trusted by thousands of professionals worldwide
                </p>
              </div>
            </div>
          </div>

          {/* Checkout Form */}
          <div className="lg:col-span-3 order-1 lg:order-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl sm:text-2xl">Secure Checkout</CardTitle>
                <CardDescription>
                  Complete your information below to finalize your purchase
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CheckoutForm 
                  priceId={priceId} 
                  product={baseProduct} 
                  initialQuantity={initialQuantity}
                  onQuantityChange={setCurrentQuantity}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}