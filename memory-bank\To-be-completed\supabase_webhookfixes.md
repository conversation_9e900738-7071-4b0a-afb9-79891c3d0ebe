Collecting workspace information## 🔍 **Webhook Issues Analysis & Recommendations**

Let me analyze each issue from supabase_issues.md and provide detailed solutions:

### **Issue 1: `charge_receipts` Table - Amount Column** 💰

**Analysis:**
- **Root Cause:** Stripe amounts are in cents (60000 = $600.00), which is correct for int4 storage
- **Affected Code:** Not an issue - this is the standard Stripe format
- **Recommendation:** ✅ **No fix needed** - storing amounts in cents is the industry standard for financial data to avoid floating-point precision issues

---

### **Issue 2: `charge_receipts` Table - Missing receipt_number** 📄

**Analysis:**
- **Affected Webhook:** `charge.succeeded` (not currently in the webhook handler)
- **Current State:** The webhook handler doesn't process charge events to populate `charge_receipts`
- **Recommendation:** **Remove the `receipt_number` column** as suggested since `receipt_url` provides the necessary functionality

````sql
ALTER TABLE charge_receipts DROP COLUMN IF EXISTS receipt_number;
````

---

### **Issue 3: `charge_receipts` Table - Missing subscription_id** 🔗

**Analysis:**
- **Root Cause:** The webhook handler isn't creating `charge_receipts` records at all
- **Affected Webhooks:** Need to handle `charge.succeeded` or `payment_intent.succeeded`
- **Recommendation:** **Add charge handling to webhook and rename column**

````javascript
// Add to processStripeEvent function's switch statement

case 'payment_intent.succeeded': {
  context.log('👉 Handling payment_intent.succeeded');
  const paymentIntent = event.data.object;
  
  // Get the associated charge
  const charge = await stripe.charges.list({
    payment_intent: paymentIntent.id,
    limit: 1
  });
  
  if (charge.data.length === 0) {
    context.log('No charge found for payment intent');
    break;
  }
  
  const chargeData = charge.data[0];
  
  // Find profile by customer ID
  const { data: profile } = await supabase
    .from('profiles')
    .select('id')
    .eq('stripe_customer_id', paymentIntent.customer)
    .maybeSingle();
  
  if (!profile) {
    context.log.error(`No profile found for customer: ${paymentIntent.customer}`);
    break;
  }
  
  // Get subscription ID from invoice if available
  let stripeSubscriptionId = null;
  if (chargeData.invoice) {
    const invoice = await stripe.invoices.retrieve(chargeData.invoice);
    stripeSubscriptionId = invoice.subscription;
  }
  
  // Insert charge receipt
  const { error: receiptError } = await supabase
    .from('charge_receipts')
    .upsert({
      stripe_charge_id: chargeData.id,
      stripe_payment_intent_id: paymentIntent.id,
      stripe_subscription_id: stripeSubscriptionId, // Changed from subscription_id
      user_id: profile.id,
      amount: chargeData.amount,
      currency: chargeData.currency,
      receipt_url: chargeData.receipt_url,
      status: 'succeeded',
      created_at: new Date(chargeData.created * 1000).toISOString()
    }, { onConflict: 'stripe_charge_id' });
  
  if (receiptError) {
    context.log.error('Error creating charge receipt:', receiptError);
  } else {
    context.log(`✅ Charge receipt created for: ${chargeData.id}`);
  }
  break;
}
````

**Database Migration:**
````sql
ALTER TABLE charge_receipts 
ADD COLUMN IF NOT EXISTS stripe_subscription_id TEXT;

-- Migrate existing data if needed
UPDATE charge_receipts cr
SET stripe_subscription_id = s.stripe_subscription_id
FROM subscriptions s
WHERE cr.subscription_id = s.id;

-- Drop old column after migration
ALTER TABLE charge_receipts 
DROP COLUMN IF EXISTS subscription_id;
````

---

### **Issue 4: `customer_events` Table Purpose** 🤔

**Analysis:**
- **Purpose:** According to [`supabase_tables.md`](c:\VS projects\QuantBoost\memory-bank\memory-bank_api\supabase_tables.md), this table tracks customer lifecycle events
- **Current Usage:** Empty because `customer.updated` webhook isn't being fully processed
- **Recommendation:** **Implement customer event tracking for important changes**

````javascript
// Add to processStripeEvent function

case 'customer.updated': {
  context.log('👉 Handling customer.updated');
  const customer = event.data.object;
  const previousAttributes = event.data.previous_attributes || {};
  
  // Find profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('id')
    .eq('stripe_customer_id', customer.id)
    .maybeSingle();
  
  if (!profile) {
    context.log.error(`No profile found for customer: ${customer.id}`);
    break;
  }
  
  // Track significant changes
  const changedFields = Object.keys(previousAttributes);
  if (changedFields.length > 0) {
    const { error } = await supabase
      .from('customer_events')
      .insert({
        user_id: profile.id,
        event_type: 'customer_updated',
        event_data: {
          changed_fields: changedFields,
          previous_values: previousAttributes,
          new_values: changedFields.reduce((acc, field) => {
            acc[field] = customer[field];
            return acc;
          }, {})
        },
        created_at: new Date().toISOString()
      });
    
    if (error) {
      context.log.error('Error logging customer event:', error);
    } else {
      context.log(`✅ Customer update event logged for: ${customer.id}`);
    }
  }
  break;
}
````

---

### **Issue 5: `payment_events` Table - Missing stripe_subscription_id** 📊

**Analysis:**
- **Root Cause:** Payment events aren't being created by the webhook handler
- **Affected Webhooks:** Need to handle payment-related events
- **Recommendation:** **Add payment event tracking**

````javascript
// Add payment_intent.payment_failed handling

case 'payment_intent.payment_failed': {
  context.log('👉 Handling payment_intent.payment_failed');
  const paymentIntent = event.data.object;
  
  // Find profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('id')
    .eq('stripe_customer_id', paymentIntent.customer)
    .maybeSingle();
  
  if (!profile) {
    context.log.error(`No profile found for customer: ${paymentIntent.customer}`);
    break;
  }
  
  // Get subscription ID from invoice if available
  let stripeSubscriptionId = null;
  if (paymentIntent.invoice) {
    const invoice = await stripe.invoices.retrieve(paymentIntent.invoice);
    stripeSubscriptionId = invoice.subscription;
  }
  
  // Log payment failure event
  const { error } = await supabase
    .from('payment_events')
    .insert({
      user_id: profile.id,
      stripe_subscription_id: stripeSubscriptionId,
      stripe_payment_intent_id: paymentIntent.id,
      event_type: 'payment_failed',
      status: 'failed',
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      error_message: paymentIntent.last_payment_error?.message,
      error_code: paymentIntent.last_payment_error?.code,
      created_at: new Date().toISOString()
    });
  
  if (error) {
    context.log.error('Error logging payment event:', error);
  } else {
    context.log(`✅ Payment failure event logged for: ${paymentIntent.id}`);
  }
  break;
}
````

---

### **Issue 6: `profiles` Table - Missing first_name/last_name** 👤

**Analysis:**
- **Root Cause:** Stripe Link checkout doesn't require name fields
- **Current Implementation:** `checkout.session.completed` only captures email
- **Recommendation:** **Extract name from Stripe customer data when available**

````javascript
// Update checkout.session.completed handler

case 'checkout.session.completed': {
  context.log('👉 Handling checkout.session.completed');
  const session = event.data.object;
  
  if (session.mode !== 'subscription') {
    context.log('Skipping non-subscription checkout session');
    break;
  }

  const customerEmail = session.customer_details?.email || session.customer_email;
  const customerName = session.customer_details?.name || '';
  
  // Parse name into first and last
  const nameParts = customerName.split(' ').filter(Boolean);
  const firstName = nameParts[0] || null;
  const lastName = nameParts.slice(1).join(' ') || null;
  
  if (!customerEmail) {
    context.log.error('No customer email found in checkout session');
    break;
  }

  let { data: profile, error } = await supabase
    .from('profiles')
    .select('id')
    .eq('email', customerEmail)
    .maybeSingle();

  if (error) {
    context.log.error('Error searching for profile:', error);
    break;
  }

  if (!profile) {
    // Create new profile with name if available
    const { data: newProfile, error: createError } = await supabase
      .from('profiles')
      .insert({
        email: customerEmail,
        stripe_customer_id: session.customer,
        first_name: firstName,
        last_name: lastName,
        created_at: new Date().toISOString(),
      })
      .select('id')
      .single();

    if (createError) {
      context.log.error('Error creating profile:', createError);
      break;
    }
    profile = newProfile;
  } else {
    // Update existing profile with Stripe customer ID and name if available
    const updateData = { stripe_customer_id: session.customer };
    if (firstName && !profile.first_name) updateData.first_name = firstName;
    if (lastName && !profile.last_name) updateData.last_name = lastName;
    
    await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', profile.id);
  }

  context.log(`✅ Checkout session processed for customer: ${customerEmail}`);
  break;
}
````

---

## 📋 **Summary of Required Actions**

1. **Immediate Database Migrations:**
   - Remove `receipt_number` column from `charge_receipts`
   - Rename `subscription_id` to `stripe_subscription_id` in `charge_receipts`

2. **Webhook Handler Updates Required:**
   - ✅ Add `payment_intent.succeeded` handler for charge receipts
   - ✅ Add `payment_intent.payment_failed` handler for payment events
   - ✅ Enhance `customer.updated` handler for customer events
   - ✅ Update `checkout.session.completed` to capture names

3. **Configuration Considerations:**
   - Ensure Stripe webhook endpoint is configured to receive all necessary event types
   - Consider implementing a webhook event replay mechanism for historical data

4. **Side Effects to Monitor:**
   - Existing queries referencing `subscription_id` in `charge_receipts` will need updating
   - Name parsing logic may need refinement for edge cases (single names, complex names)
   - Performance impact of additional Stripe API calls (retrieving invoices)

The root cause of most issues is that the webhook handler is only processing a subset of the 29 webhook events that QuantBoost needs to handle comprehensively. The fixes provided will ensure complete data capture for financial tracking and customer analytics. 🚀