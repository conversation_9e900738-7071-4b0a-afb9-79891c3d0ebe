import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import Stripe from 'stripe';

// Remove top-level clients; initialize inside handlers to avoid build-time env errors

// GET - List enterprise customers
export async function GET(req: NextRequest) {
  try {
    const env = (globalThis as any).process?.env;
    const supabaseUrl = env?.NEXT_PUBLIC_SUPABASE_URL as string | undefined;
    const supabaseKey = env?.SUPABASE_SERVICE_KEY as string | undefined;
    const stripeKey = env?.STRIPE_SECRET_KEY as string | undefined;

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ error: 'Server configuration error (Supabase not configured)' }, { status: 500 });
    }
    if (!stripeKey) {
      return NextResponse.json({ error: 'Server configuration error (Stripe not configured)' }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);
    const stripe = new Stripe(stripe<PERSON>ey, { apiVersion: '2025-03-31.basil' });

    // Get user session to verify sales role
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid authentication' }, { status: 401 });
    }

    // Check if user has sales or admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['sales', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Fetch enterprise customers
    const { data: customers, error } = await supabase
      .from('enterprise_customers')
      .select(`
        *,
        sales_rep:sales_rep_id(email, first_name, last_name)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(customers);
  } catch (error) {
    console.error('Error fetching enterprise customers:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST - Create new enterprise customer
export async function POST(req: NextRequest) {
  try {
    const env = (globalThis as any).process?.env;
    const supabaseUrl = env?.NEXT_PUBLIC_SUPABASE_URL as string | undefined;
    const supabaseKey = env?.SUPABASE_SERVICE_KEY as string | undefined;
    const stripeKey = env?.STRIPE_SECRET_KEY as string | undefined;

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ error: 'Server configuration error (Supabase not configured)' }, { status: 500 });
    }
    if (!stripeKey) {
      return NextResponse.json({ error: 'Server configuration error (Stripe not configured)' }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);
    const stripe = new Stripe(stripeKey, { apiVersion: '2025-03-31.basil' });

    // Get user session to verify sales role
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid authentication' }, { status: 401 });
    }

    // Check if user has sales or admin role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['sales', 'admin'].includes(profile.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await req.json();
    const {
      company_name,
      contact_email,
      contact_name,
      contact_phone,
      billing_address,
      license_quantity,
      license_tier,
      annual_contract_value,
      contract_start_date,
      contract_end_date,
      payment_terms
    } = body;

    // Create Stripe customer for enterprise client
    const stripeCustomer = await stripe.customers.create({
      name: company_name,
      email: contact_email,
      phone: contact_phone,
      metadata: {
        type: 'enterprise',
        license_quantity: license_quantity.toString(),
        license_tier,
      },
      address: billing_address ? {
        line1: billing_address.line1,
        line2: billing_address.line2 || undefined,
        city: billing_address.city,
        state: billing_address.state,
        postal_code: billing_address.postal_code,
        country: billing_address.country || 'US',
      } : undefined,
    });

    // Create enterprise customer record
    const { data: customer, error } = await supabase
      .from('enterprise_customers')
      .insert({
        company_name,
        contact_email,
        contact_name,
        contact_phone,
        billing_address,
        stripe_customer_id: stripeCustomer.id,
        license_quantity,
        license_tier,
        annual_contract_value,
        contract_start_date,
        contract_end_date,
        payment_terms: payment_terms || 30,
        sales_rep_id: user.id,
        status: 'prospect'
      })
      .select()
      .single();

    if (error) {
      // Clean up Stripe customer if database insert failed
      await stripe.customers.del(stripeCustomer.id);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, customer });
  } catch (error) {
    console.error('Error creating enterprise customer:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
