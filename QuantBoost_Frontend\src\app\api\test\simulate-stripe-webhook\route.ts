import { NextRequest, NextResponse } from 'next/server';

/**
 * Test-only endpoint for simulating Stripe webhook events during integration testing.
 * This endpoint bypasses webhook signature validation and allows direct event injection.
 * 
 * Usage:
 * POST /api/test/simulate-stripe-webhook
 * Body: { eventType: string, payload: object }
 * 
 * Example:
 * {
 *   "eventType": "customer.subscription.created",
 *   "payload": { ... subscription object ... }
 * }
 */
export async function POST(req: NextRequest) {
  // Only allow in development/test environments
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Test endpoint not available in production' }, { status: 403 });
  }

  try {
    const { eventType, payload } = await req.json();

    if (!eventType || !payload) {
      return NextResponse.json({ 
        error: 'Missing required fields', 
        required: ['eventType', 'payload'] 
      }, { status: 400 });
    }

    // Construct a fake Stripe event
    const fakeEvent = {
      id: `evt_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      type: eventType,
      data: { object: payload },
      created: Math.floor(Date.now() / 1000),
      livemode: false,
      object: 'event',
      pending_webhooks: 1,
      request: { id: null, idempotency_key: null },
      api_version: '2025-03-31.basil'
    };

    console.log(`🧪 Simulating webhook event: ${eventType} with ID: ${fakeEvent.id}`);

    // Forward to the actual webhook handler with test signature
    const baseUrl = process.env.BASE_URL || `${req.nextUrl.protocol}//${req.nextUrl.host}`;
    const webhookResponse = await fetch(`${baseUrl}/api/webhooks/stripe`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json', 
        'X-Stripe-Signature': 'test-signature-bypass'
      },
      body: JSON.stringify(fakeEvent)
    });

    const responseBody = await webhookResponse.text();
    let parsedBody: any = {};
    try {
      parsedBody = JSON.parse(responseBody);
    } catch {
      parsedBody = { rawResponse: responseBody };
    }

    return NextResponse.json({
      success: webhookResponse.ok,
      status: webhookResponse.status,
      eventId: fakeEvent.id,
      eventType,
      webhookResponse: parsedBody,
      message: webhookResponse.ok ? 'Webhook simulation completed successfully' : 'Webhook simulation failed'
    }, { status: webhookResponse.status });

  } catch (error: any) {
    console.error('❌ Webhook simulation error:', error);
    return NextResponse.json({ 
      error: 'Webhook simulation failed', 
      details: error.message 
    }, { status: 500 });
  }
}