import { test, expect } from './combined.fixture';
import Stripe from 'stripe';

/**
 * Webhook Test Factory - Standardized test data generators
 * Creates consistent test data for all webhook testing scenarios
 */
export class WebhookTestFactory {
  static createTestCustomer(overrides: Partial<any> = {}) {
    return {
      id: `cus_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      email: `test_${Date.now()}_${Math.random().toString(36).slice(2)}@quantboost-test.com`,
      description: 'QuantBoost Webhook Test Customer',
      created: Math.floor(Date.now() / 1000),
      ...overrides
    };
  }

  static createTestSubscription(customerId: string, overrides: Partial<any> = {}) {
    const now = Math.floor(Date.now() / 1000);
    // Real Stripe price configuration (Quarterly & Annual)
    const REAL_PRICES = [
      {
        id: 'price_1RCQeBE6FvhUKV1bUN94Oihf', // Quarterly $45
        unit_amount: 4500,
        currency: 'usd',
        recurring: { interval: 'month', interval_count: 3 },
        nickname: 'Quarterly'
      },
      {
        id: 'price_1RC3HTE6FvhUKV1bE9D6zf6e', // Annual $120
        unit_amount: 12000,
        currency: 'usd',
        recurring: { interval: 'year', interval_count: 1 },
        nickname: 'Annual'
      }
    ];

    // Allow override to pick price via overrides.price_id; else randomly choose
    let chosen = REAL_PRICES[Math.floor(Math.random() * REAL_PRICES.length)];
    if ((overrides as any).price_id) {
      const forced = REAL_PRICES.find(p => p.id === (overrides as any).price_id);
      if (forced) chosen = forced;
    }

    // Compute period end based on interval
    const end = (() => {
      if (chosen.recurring.interval === 'year') return now + 365 * 24 * 60 * 60;
      if (chosen.recurring.interval === 'month' && chosen.recurring.interval_count === 3) return now + 90 * 24 * 60 * 60; // approx for quarterly
      return now + 30 * 24 * 60 * 60; // fallback
    })();

    // Allow quantity override (default 1) so items.data[0].quantity stays in sync with top-level quantity
    const quantity = (overrides as any).quantity ?? 1;
    const PRODUCT_ID = 'prod_S6Fn893jGxRhKk'; // Single product with multiple prices (per architecture docs)

    return {
      id: `sub_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      customer: customerId,
      status: 'active',
      quantity,
      current_period_start: now,
      current_period_end: end,
      trial_start: null,
      trial_end: null,
      cancel_at_period_end: false,
      canceled_at: null,
      created: now,
      items: {
        data: [{
          id: `si_test_${Date.now()}`,
          // Real Stripe shape includes quantity at the subscription item level
          quantity,
          price: {
            id: chosen.id,
            unit_amount: chosen.unit_amount,
            currency: chosen.currency,
            recurring: chosen.recurring,
            nickname: chosen.nickname,
            // Real Stripe price object includes product reference (string id)
            product: PRODUCT_ID
          }
        }]
      },
      ...overrides
    };
  }

  static createTestCharge(customerId: string, overrides: Partial<any> = {}) {
    return {
      id: `ch_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      customer: customerId,
      amount: 2000,
      currency: 'usd',
      status: 'succeeded',
      description: 'QuantBoost Basic Plan',
      receipt_email: `test_${Date.now()}@quantboost-test.com`,
      created: Math.floor(Date.now() / 1000),
      ...overrides
    };
  }

  static createTestPaymentIntent(customerId: string, overrides: Partial<any> = {}) {
    return {
      id: `pi_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      customer: customerId,
      amount: 2000,
      currency: 'usd',
      status: 'succeeded',
      description: 'QuantBoost Basic Plan',
      receipt_email: `test_${Date.now()}@quantboost-test.com`,
      created: Math.floor(Date.now() / 1000),
      ...overrides
    };
  }

  static createTestDispute(chargeId: string, overrides: Partial<any> = {}) {
    const now = Math.floor(Date.now() / 1000);
    return {
      id: `dp_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      charge: chargeId,
      amount: 2000,
      currency: 'usd',
      reason: 'fraudulent',
      status: 'warning_needs_response',
      created: now,
      evidence_details: {
        due_by: now + (7 * 24 * 60 * 60), // 7 days from now
        submission_count: 0
      },
      ...overrides
    };
  }

  static createTestRefund(chargeId: string, overrides: Partial<any> = {}) {
    return {
      id: `re_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      charge: chargeId,
      amount: 2000,
      currency: 'usd',
      status: 'succeeded',
      reason: 'requested_by_customer',
      created: Math.floor(Date.now() / 1000),
      ...overrides
    };
  }

  static createTestCheckoutSession(customerId: string, subscriptionId: string, overrides: Partial<any> = {}) {
    return {
      id: `cs_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      customer: customerId,
      subscription: subscriptionId,
      payment_status: 'paid',
      status: 'complete',
      mode: 'subscription',
      amount_total: 2000,
      currency: 'usd',
      customer_email: `test_${Date.now()}@quantboost-test.com`,
      created: Math.floor(Date.now() / 1000),
      ...overrides
    };
  }

  static createTestInvoice(customerId: string, subscriptionId: string, overrides: Partial<any> = {}) {
    return {
      id: `in_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      customer: customerId,
      subscription: subscriptionId,
      status: 'paid',
      amount_paid: 2000,
      amount_due: 0,
      currency: 'usd',
      period_start: Math.floor(Date.now() / 1000),
      period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60),
      created: Math.floor(Date.now() / 1000),
      ...overrides
    };
  }

  static createTestReview(chargeId: string, overrides: Partial<any> = {}) {
    return {
      id: `prv_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      charge: chargeId,
      reason: 'rule',
      opened_reason: 'rule',
      created: Math.floor(Date.now() / 1000),
      ...overrides
    };
  }

  static createTestSetupIntent(customerId: string, overrides: Partial<any> = {}) {
    return {
      id: `seti_test_${Date.now()}_${Math.random().toString(36).slice(2)}`,
      customer: customerId,
      status: 'succeeded',
      usage: 'off_session',
      created: Math.floor(Date.now() / 1000),
      ...overrides
    };
  }

  static async createRealSubscription(stripe: Stripe | null | undefined, priceId: string, quantity = 1, overrides: Partial<any> = {}, existingCustomerId?: string) {
    if (!stripe) {
      throw new Error('Stripe client not configured (missing STRIPE_SECRET_KEY_TEST). Set env var to run real subscription tests.');
    }
    let customerId = existingCustomerId;
    let createdCustomer: Stripe.Customer | null = null;
    if (!customerId) {
      const email = `subtest_${Date.now()}_${Math.random().toString(36).slice(2)}@quantboost-test.com`;
      createdCustomer = await stripe.customers.create({ email, name: 'Subscription Test Customer' });
      customerId = createdCustomer.id;
    }
    // Optional: Attach a test payment method and set as default to avoid incomplete status
    let paymentMethodId: string | undefined;
    try {
      // Use token-based test card to avoid raw PAN restrictions
      const pm = await stripe.paymentMethods.create({ type: 'card', card: { token: 'tok_visa' } as any });
      paymentMethodId = pm.id;
  await stripe.paymentMethods.attach(pm.id, { customer: customerId });
  await stripe.customers.update(customerId, { invoice_settings: { default_payment_method: pm.id } });
    } catch (e) {
      console.warn('⚠️ Failed to attach real payment method for test subscription, proceeding anyway:', e);
    }
    // Create subscription
    const subscription = await stripe.subscriptions.create({
  customer: customerId,
      items: [{ price: priceId, quantity }],
      payment_behavior: 'allow_incomplete',
      expand: ['latest_invoice.payment_intent']
    });
    // Normalize shape to match synthetic structure used by tests
  const firstItem = subscription.items.data[0];
    return {
      id: subscription.id,
      customer: subscription.customer,
      status: subscription.status,
      quantity: firstItem.quantity || quantity,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      trial_start: subscription.trial_start,
      trial_end: subscription.trial_end,
      cancel_at_period_end: subscription.cancel_at_period_end,
      canceled_at: subscription.canceled_at,
      created: subscription.created,
      items: {
        data: [
          {
            id: firstItem.id,
      // Ensure synthetic event mirrors real Stripe: include quantity at item level
      quantity: firstItem.quantity || quantity,
            price: {
              id: firstItem.price.id,
              unit_amount: firstItem.price.unit_amount,
              currency: firstItem.price.currency,
              recurring: firstItem.price.recurring ? { interval: firstItem.price.recurring.interval, interval_count: firstItem.price.recurring.interval_count } : null,
        nickname: firstItem.price.nickname,
        // Provide product id (present on real Stripe price objects) for team plan/product detection
        product: (firstItem.price as any).product || 'prod_S6Fn893jGxRhKk'
            }
          }
        ]
      },
      // Pass through overrides last
      ...overrides
    };
  }

  static async createRealPaymentIntent(
    stripe: Stripe | null | undefined,
    customerId: string,
    options: { outcome?: 'succeeded' | 'failed' | 'canceled' | 'manual_capturable'; amount?: number } = {}
  ) {
    if (!stripe) throw new Error('Stripe client not configured for real payment intent');
    const amount = options.amount ?? 2000;
  if (options.outcome === 'failed') {
      // Instead of triggering a real failing intent (which throws), return a synthetic failed intent
      // tied to the real customer. This keeps test deterministic while still exercising webhook logic.
      return {
        id: `pi_fail_${Date.now()}_${Math.random().toString(36).slice(2)}`,
        object: 'payment_intent',
        amount,
        currency: 'usd',
        customer: customerId,
        status: 'requires_payment_method',
        last_payment_error: {
          message: 'Your card was declined.',
          code: 'card_declined',
          type: 'card_error'
        },
        created: Math.floor(Date.now() / 1000)
      } as any;
    }
    if (options.outcome === 'canceled') {
      // Create a real PI then cancel it to produce a realistic canceled object
      const pm = await stripe.paymentMethods.create({ type: 'card', card: { token: 'tok_visa' } as any });
      await stripe.paymentMethods.attach(pm.id, { customer: customerId });
      const pi = await stripe.paymentIntents.create({
        amount,
        currency: 'usd',
        customer: customerId,
        payment_method: pm.id,
        capture_method: 'automatic',
        confirmation_method: 'automatic',
        description: 'Test Canceled Payment Intent'
      });
      const canceled = await stripe.paymentIntents.cancel(pi.id);
      return canceled;
    }

    if (options.outcome === 'manual_capturable') {
      // Create a manual capture intent so capturable amount becomes available (Stripe will emit amount_capturable_updated)
      const pm = await stripe.paymentMethods.create({ type: 'card', card: { token: 'tok_visa' } as any });
      await stripe.paymentMethods.attach(pm.id, { customer: customerId });
      const pi = await stripe.paymentIntents.create({
        amount,
        currency: 'usd',
        customer: customerId,
        payment_method: pm.id,
        capture_method: 'manual',
  confirm: true,
  automatic_payment_methods: { enabled: true, allow_redirects: 'never' },
  return_url: 'https://example.com/return',
        description: 'Test Manual Capturable Payment Intent'
      });
      // At this point payment_intent.amount_capturable_updated event would fire in real Stripe; we just return PI
      return pi;
    }

    // Success path
    const pm = await stripe.paymentMethods.create({ type: 'card', card: { token: 'tok_visa' } as any });
    await stripe.paymentMethods.attach(pm.id, { customer: customerId });
    const pi = await stripe.paymentIntents.create({
      amount,
      currency: 'usd',
      customer: customerId,
      payment_method: pm.id,
      confirm: true,
      off_session: true,
      automatic_payment_methods: { enabled: true },
      description: 'Test Successful Payment Intent'
    });
    return pi;
  }
}

/**
 * Enhanced webhook simulation with proper Stripe event structure
 * Posts directly to the real webhook endpoint with test signature bypass
 */
export async function simulateStripeWebhook(
  eventType: string,
  eventData: any,
  options: { skipSignature?: boolean; eventId?: string } = {}
) {
  const eventId = options.eventId || `evt_test_${Date.now()}_${Math.random().toString(36).slice(2)}`;
  
  const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
  
  // Create a complete Stripe webhook event structure
  const webhookEvent = {
    id: eventId,
    object: 'event',
    type: eventType,
    created: Math.floor(Date.now() / 1000),
    data: {
      object: eventData
    },
    livemode: false,
    pending_webhooks: 0,
    request: {
      id: null,
      idempotency_key: null
    },
    api_version: '2025-03-31.basil'
  };
  
  // Post directly to the real webhook endpoint with test signature bypass
  const response = await fetch(`${baseUrl}/api/webhooks/stripe`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'stripe-signature': 'test-signature-bypass'
    },
    body: JSON.stringify(webhookEvent)
  });

  const responseBody = await response.json().catch(() => ({}));

  return { 
    eventType, 
    eventData,
    response, 
    status: response.status, 
    body: responseBody 
  };
}

/**
 * Database verification utilities for webhook testing
 */
export class WebhookDatabaseVerifier {
  static async verifySubscriptionCreated(supabaseClient: any, subscriptionId: string) {
    const { data: subscription, error } = await supabaseClient
      .from('subscriptions')
      .select('*')
      .eq('stripe_subscription_id', subscriptionId)
      .single();

    if (error) {
      console.error('Error fetching subscription:', error);
      return null;
    }

    expect(subscription).toBeTruthy();
    expect(subscription.status).toBeDefined();
    return subscription;
  }

  static async verifyLicensesCreated(supabaseClient: any, subscriptionId: string) {
    const { data: subscription } = await supabaseClient
      .from('subscriptions')
      .select('id')
      .eq('stripe_subscription_id', subscriptionId)
      .single();

    if (!subscription) return [];

    const { data: licenses, error } = await supabaseClient
      .from('licenses')
      .select('*')
      .eq('subscription_id', subscription.id);

    if (error) {
      console.error('Error fetching licenses:', error);
      return [];
    }

    expect(licenses).toBeTruthy();
    expect(licenses.length).toBeGreaterThan(0);
    return licenses;
  }

  static async verifyWebhookEventLogged(supabaseClient: any, eventType: string, eventId?: string) {
    let query = supabaseClient
      .from('webhook_events')
      .select('*')
      .eq('event_type', eventType)
      .eq('status', 'processed')
      .order('created_at', { ascending: false })
      .limit(1);

    if (eventId) {
      query = query.eq('stripe_event_id', eventId);
    }

    const { data: webhookEvent, error } = await query.single();

    if (error) {
      console.warn('Webhook event not found:', error);
      return null;
    }

    expect(webhookEvent).toBeTruthy();
    return webhookEvent;
  }

  static async verifyChargeReceiptCreated(supabaseClient: any, paymentIntentId: string) {
    const { data: chargeReceipt, error } = await supabaseClient
      .from('charge_receipts')
      .select('*')
      .eq('stripe_payment_intent_id', paymentIntentId)
      .single();

    if (error) {
      console.error('Error fetching charge receipt:', error);
      return null;
    }

    expect(chargeReceipt).toBeTruthy();
    return chargeReceipt;
  }

  static async verifyDisputeCreated(supabaseClient: any, disputeId: string) {
    const { data: dispute, error } = await supabaseClient
      .from('disputes')
      .select('*')
      .eq('stripe_dispute_id', disputeId)
      .single();

    if (error) {
      console.error('Error fetching dispute:', error);
      return null;
    }

    expect(dispute).toBeTruthy();
    return dispute;
  }

  static async verifyRefundCreated(supabaseClient: any, refundId: string) {
    const { data: refund, error } = await supabaseClient
      .from('refunds')
      .select('*')
      .eq('stripe_refund_id', refundId)
      .single();

    if (error) {
      console.error('Error fetching refund:', error);
      return null;
    }

    expect(refund).toBeTruthy();
    return refund;
  }

  static async verifyFraudReviewCreated(supabaseClient: any, reviewId: string) {
    const { data: review, error } = await supabaseClient
      .from('fraud_reviews')
      .select('*')
      .eq('stripe_review_id', reviewId)
      .single();

    if (error) {
      console.error('Error fetching fraud review:', error);
      return null;
    }

    expect(review).toBeTruthy();
    return review;
  }

  static async verifySetupFailureLogged(supabaseClient: any, setupIntentId: string) {
    const { data: failure, error } = await supabaseClient
      .from('setup_failures')
      .select('*')
      .eq('stripe_setup_intent_id', setupIntentId)
      .single();

    if (error) {
      console.error('Error fetching setup failure:', error);
      return null;
    }

    expect(failure).toBeTruthy();
    return failure;
  }

  static async verifyPaymentEventLogged(supabaseClient: any, paymentIntentId: string, eventType: string) {
    let { data: paymentEvent, error } = await supabaseClient
      .from('payment_events')
      .select('*')
      .eq('stripe_payment_intent_id', paymentIntentId)
      .eq('event_type', eventType)
      .maybeSingle();

    // Fallback: allow passing shortened type like 'payment_failed'
    if ((!paymentEvent || error) && !eventType.startsWith('payment_intent.') && !eventType.startsWith('charge.')) {
      const altType = `payment_intent.${eventType}`;
      ({ data: paymentEvent, error } = await supabaseClient
        .from('payment_events')
        .select('*')
        .eq('stripe_payment_intent_id', paymentIntentId)
        .eq('event_type', altType)
        .maybeSingle());
    }

    if (error) {
      console.warn('Payment event not found:', error);
      return null;
    }

    expect(paymentEvent).toBeTruthy();
    return paymentEvent;
  }

  static async waitForDatabaseUpdate(
    verificationFn: () => Promise<any>,
    maxAttempts: number = 10,
    delayMs: number = 500
  ) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const result = await verificationFn();
        if (result) {
          return result;
        }
      } catch (error) {
        if (attempt === maxAttempts) {
          throw error;
        }
      }
      
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
    
    throw new Error(`Database update not found after ${maxAttempts} attempts`);
  }
}

export { test, expect };