# Sales MVP — Enterprise Trials

This document describes how to run and test the minimal enterprise trials flow.

## What’s included
- Create 30‑day trial for an enterprise customer (default seats: 50)
- Bulk invite users (emails or CSV); tokenized links sent via email
- Accept invite via `/invite/[token]` (requires login), increments seats atomically
- Convert to paid via Stripe Checkout (annual/quarterly; configurable price)

## Environment variables
Copy `.env.local.example` to `.env.local` and set:

- NEXT_PUBLIC_SUPABASE_URL
- NEXT_PUBLIC_SUPABASE_ANON_KEY
- SUPABASE_SERVICE_ROLE_KEY
- STRIPE_SECRET_KEY (test key)
- NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY (test key)
- STRIPE_WEBHOOK_SECRET (for Stripe signature verification; required in production. In tests we use a special bypass header.)
- NEXT_PUBLIC_APP_URL (e.g. http://localhost:3000)
- RESEND_API_KEY and FROM_EMAIL (optional; if not set, emails are skipped with a warning)

## Dev commands
- npm run dev — start Next.js locally
- npm run build — type-check and build

## API endpoints
- GET /api/sales/trials — list trials (with company and days_left)
- POST /api/sales/trials — create trial (body: { company, contactEmail, domain? })
- GET /api/sales/trials/[trialId] — trial details + invites (no tokens)
- POST /api/sales/trials/[trialId]/invite — bulk invite (body: { emails[] | csv })
- POST /api/invite/accept — accept invite (body: { token })
- POST /api/sales/trials/[trialId]/convert — Stripe Checkout (body: { priceId, quantity? })

## UI
- Dashboard: /dashboard/sales
  - New Trial dialog
  - Trials table with seats and days left
  - Invite dialog (paste emails)
  - Convert button (navigates to Stripe Checkout)
- Invite acceptance page: /invite/[token]
  - Requires user to be logged in (magic link/login), then processes token

## Notes
- RLS is enabled and deny-all on new tables; server routes use the service role.
- Atomic acceptance implemented with RPC `accept_trial_invite(p_token, p_user_id)`.
- Webhook endpoint `/api/webhooks/stripe` marks trials converted when it receives `checkout.session.completed` containing `metadata.trialId`. It uses `webhook_events` for idempotency (duplicate events return 409) and updates `enterprise_trials.status = 'converted'` and sets `converted_at`.

## E2E quick run (Playwright)
From `QuantBoost_Testing`:

1. Set the following in `.env` or `.env.local`:
  - BASE_URL (e.g. http://localhost:3000)
  - SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY (for DB verification)
  - STRIPE_SECRET_KEY_TEST (optional for real Stripe interactions; not required for synthetic webhook test)
2. Start the frontend locally: `npm run dev` in QuantBoost_Frontend.
3. Run: `npm run test:webhooks` in QuantBoost_Testing to execute `webhook-trial-conversion.spec.ts`.

The spec simulates a `checkout.session.completed` event with a matching `trialId` and asserts the trial flips to `converted` exactly once.
