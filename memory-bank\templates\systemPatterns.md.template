# systemPatterns.md - Architecture and Design (Multi-Project Workspace)

**Purpose:** Documents the overall system architecture, how subprojects interact, key components within each subproject, and relevant design patterns.

**Instructions for Dane:**
*   Focus on the overall architecture and *interactions between* subprojects in Section 1.
*   Use H3 headings under "Key Components" to group components by their parent subproject.
*   Clearly describe data flow *between* subprojects in Section 3.
*   Prefix Design Patterns/Conventions with `[Global]`, `[Frontend]`, `[Backend]`, etc., if they are not universally applied across all subprojects.

---

## 1. High-Level Architecture Overview

<!-- #ArchitectureOverview -->
**Summary:**
*   [Describe the overall architecture considering all subprojects, e.g., Web Frontend communicates with Backend API; VSTO Add-in also communicates with Backend API; Shared Library used by Backend and VSTO.]
*   [Mention primary deployment environments for each major piece.]

<!-- #InterProjectCommunication -->
**Inter-Project Communication:**
*   **Frontend <-> Backend:** [Mechanism, e.g., REST API Calls via HTTPS. Define base URL source (e.g., env variable `VITE_API_BASE_URL`)]
    *   **Key Contract(s):** [Reference API documentation location/file if it exists, e.g., `api/openapi.yaml`]
    *   **Authentication:** [Method, e.g., JWT Bearer Tokens]
*   **VSTO Add-in <-> Backend:** [Mechanism, e.g., REST API Calls via HTTPS. Define base URL source (e.g., App.config setting)]
    *   **Key Contract(s):** [Reference API documentation]
    *   **Authentication:** [Method, e.g., API Key, User JWT]
*   **Backend <-> Shared Lib:** [Mechanism, e.g., Direct library import/dependency]
*   [Detail other significant interactions.]

<!-- #ArchitectureDiagram -->
**Diagram Reference (Conceptual):**
*   [Describe or link to a diagram showing the subprojects and their main communication paths.]

---

## 2. Key Components & Responsibilities (Grouped by Subproject)

<!-- #Components -->
### Frontend Subproject
*   **[Component Name 1, e.g., Login Module]**
    *   **Responsibility:** [e.g., Handles UI for login/registration.]
    *   **Location:** `frontend/src/components/auth/`
*   **[Component Name 2, e.g., Data Grid View]**
    *   **Responsibility:** [e.g., Displays data fetched from backend.]
    *   **Location:** `frontend/src/components/dataview/`

### Backend Subproject
*   **[Component Name 1, e.g., Auth Controller]**
    *   **Responsibility:** [e.g., Handles /api/login, /api/register requests.]
    *   **Location:** `backend/src/controllers/auth.ts`
*   **[Component Name 2, e.g., User Service]**
    *   **Responsibility:** [e.g., Business logic for user management.]
    *   **Location:** `backend/src/services/userService.ts`

### VSTO Add-in Subproject
*   **[Component Name 1, e.g., Ribbon Controller (`QuantBoostRibbon.cs`)]**
    *   **Responsibility:** [e.g., Handles Ribbon UI events, triggers actions.]
    *   **Location:** `QuantBoost_Powerpoint_Addin/`
*   **[Component Name 2, e.g., Analysis Task Pane (`AnalyzePane.cs`)]**
    *   **Responsibility:** [e.g., Displays analysis results, allows user interaction.]
    *   **Location:** `QuantBoost_Powerpoint_Addin/Panes/`

<!-- Add H3 sections and components for other subprojects -->

---

## 3. Data Flow & Storage

<!-- #DataFlow -->
**Primary Data Flows (Cross-Project Emphasis):**
*   [Describe key flows emphasizing data movement BETWEEN subprojects.]
*   [Example: User upload via [Frontend] -> Request to [Backend] API -> [Backend] stores file metadata in DB & file content in S3 -> [Frontend] displays success.]
*   [Example: User clicks Analyze in [VSTO Add-in] -> [VSTO Add-in] parses PPTX -> Sends data to [Backend] API -> [Backend] processes -> [VSTO Add-in] displays results.]

<!-- #DataStorage -->
**Data Storage (Specify Owning Subproject if not Global):**
*   **Primary Database:** [Type] - [Purpose] - **Owner:** `[Backend]`
*   **Caching:** [Mechanism] - [Purpose] - **Owner:** `[Backend]`
*   **Object Storage:** [Mechanism] - [Purpose] - **Owner:** `[Backend]` (or potentially accessed by multiple)
*   **Local Settings (VSTO):** [Mechanism, e.g., User AppData file] - [Purpose] - **Owner:** `[VSTO]`

---

## 4. Design Patterns & Conventions

<!-- #DesignPatterns -->
**Key Patterns in Use:**
*   `[Global]` [List patterns applied across the board, e.g., Dependency Injection in backend/VSTO.]
*   `[Frontend]` [List patterns specific to frontend, e.g., Component-Based Architecture (React).]
*   `[Backend]` [List patterns specific to backend, e.g., Repository Pattern for data access.]
*   `[VSTO]` [List patterns specific to VSTO, e.g., Command Pattern for Ribbon actions.]

<!-- #CodingConventions -->
**Core Coding Conventions:**
*   `[Global]` [Reference overall style guides/linters.]
*   `[Frontend]` [Specific frontend conventions.]
*   `[Backend]` [Specific backend conventions.]
*   `[VSTO]` [Specific C#/.NET conventions.]

---