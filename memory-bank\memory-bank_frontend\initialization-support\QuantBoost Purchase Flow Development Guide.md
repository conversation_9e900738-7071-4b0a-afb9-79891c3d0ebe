# QuantBoost B2B SaaS Purchase & Team Management Flow – Development Guide

## Overview

This guide covers building a modern, custom purchase and onboarding experience for a Stripe-based SaaS. It spans custom checkout with Stripe Elements, a team admin dashboard, license invitation and management, and magic link authentication.

---

## 1. Custom Checkout Page

**Goal:** 
Branded checkout experience on your domain. Customers select Individual or Team plans, choose period, enter email, and (for teams) enter quantity/seats.

### Steps

- Design a plan selection UX: Individual vs Team, price tiers (Annual/Quarterly)
- For **Team**: add seat count (quantity) input
- Collect customer email
- Dynamically calculate and show total cost
- Integrate [Stripe Elements](https://stripe.com/docs/elements) for secure card entry
- On “Pay”, POST: product, price, quantity, and email to a backend route
- Backend:  
  - Use Stripe Node SDK to create a Subscription (with correct price/quantity)
  - Return a `clientSecret` for Stripe Elements
- Frontend:  
  - Use clientSecret with Stripe Elements to confirm the payment/subscription
- On success, redirect to dashboard/onboarding

**References:**
- [Stripe Elements Quickstart](https://stripe.com/docs/elements/quickstart)
- [Elements + Subscriptions Example](https://github.com/stripe-samples/elements-quickstart/tree/main/client/react)

---

## 2. Custom Admin Dashboard / Onboarding Landing Page

**Goal:**  
Central landing for admins post-purchase.

### Features

- Welcome message, onboarding steps
- Show team license/seat count (from Stripe subscription)
- Subscription details (plan, renewal date, purchaser)
- Success/failure messaging
- (Optional) Show links to docs/videos to get started

### Implementation

- On checkout success, pass a session token, email, or magic link for login
- Protect the dashboard with authentication (see Section 4 below)

---

## 3. Team License Management & Invitation

**Goal:**  
Admins manage who uses paid seats. Users are invited by email.

### DB Models

- **Users:** Standard user table
- **Subscriptions:** Linked to user & Stripe subscription ID
- **Licenses/Seats:**  
  - `id`, `subscription_id`, `assigned_email`, `status` (e.g. invited/active), `invite_token`, timestamps

### Team Seat Management Features

- View current assigned users/seats
- Show seat usage (e.g. “3 of 10 assigned”)
- Invite user by email (assigns open license, sends invitation)
- Resend/revoke invitations
- Remove or swap seat assignments
- API routes:
    - `POST /api/team/invite` (invite)
    - `GET /api/team/licenses` (list seats)
    - `POST /api/team/revoke` (remove a user)

### Invitation Flow

- Admin enters email for seat assignment
- System generates token, saves in DB, sends invite email with unique magic link
- Email recipient clicks, uses link (see next section), claims their seat

---

## 4. Magic Link Authentication

**Goal:**  
One-click login for invited users, secure and user-friendly.

### Recommended Options

- Use a service: [Magic.link](https://magic.link/), [Clerk.dev](https://clerk.com/docs/authentication/magic-links), [next-auth credentials/magic link provider](https://next-auth.js.org/)
- Or roll your own: generate cryptographically strong token (e.g. JWT or UUID), store with invite+email

### Flow

1. Generate a secure token (linking seat/license and email)
2. Email: “You’ve been invited to QuantBoost. [Accept Invite]”
3. Link: `https://yourdomain.com/accept-invite?token=...`
4. `/accept-invite` endpoint:
    - Validates the token (not expired, email matches)
    - If user exists, log them in and assign seat
    - If new user, create user, log in, assign seat
    - Mark license as accepted/active, clear token
    - Set session/cookie, redirect to app

### Security

- Token should expire
- Only valid for invited emails
- Only usable once

**References:**
- [Magic.dev docs](https://magic.link/docs)
- [Clerk Magic Links](https://clerk.com/docs/authentication/magic-links)
- [next-auth Magic Link](https://next-auth.js.org/providers/email)

---

## 5. Billing Portal

- Use Stripe portal link (optional) only for account/billing changes, not as main user dashboard.
- For all onboarding, seat management, and access: use your custom dashboard.

---

## 6. Onboarding & Help

- First login: show onboarding (steps/modal)
- Include clear “Invite your team!” CTA for team buyers
- Provide links to documentation/resources

---

## 7. Technology Stack & Libraries

| Purpose           | Options / Recommendations               |
|-------------------|-----------------------------------------|
| React Stripe Integration | [react-stripe-js](https://github.com/stripe/react-stripe-js) |
| Auth/Magic Link   | [next-auth](https://next-auth.js.org/), [Clerk.dev](https://clerk.com/), [Magic.link](https://magic.link) |
| Sending Emails    | [Resend](https://resend.com/), [SendGrid](https://sendgrid.com/), [Postmark](https://postmarkapp.com/) |
| Backend           | Next.js API Routes + Stripe SDK         |

---

## 8. Development Checklist

- [ ] **Custom checkout page** using Stripe Elements
- [ ] **Backend route** creates Stripe subscriptions and returns clientSecret
- [ ] **Success redirect** goes to your onboarding/dashboard
- [ ] **Admin dashboard:** shows seat count, allows team invites, seat management
- [ ] **API endpoints** for inviting, removing, listing team members/seats
- [ ] **Magic link authentication** for invite acceptance
- [ ] **Welcome/onboarding flow** for first time admins/users
- [ ] **Email notifications** for invitations
- [ ] (Optional) Provide Stripe customer portal access for billing only

---

## Useful Resources

- [Stripe Elements Overview](https://stripe.com/docs/elements)
- [Team Management SaaS Example](https://www.saaspegasus.com/guides/saas-stripe-team-seat-management/)
- [Magic Link Auth Pattern](https://vercel.com/blog/magic-links)
- [Figma UI inspiration for onboarding](https://www.figma.com/templates/search/onboarding/)

---


