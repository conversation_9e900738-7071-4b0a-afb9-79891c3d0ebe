# projectbrief.md - Project Foundation (Multi-Project Workspace)

**Purpose:** Defines the core goals, scope, and success criteria for the overall project, potentially detailing contributions from different subprojects.

**Instructions for Dane:**
*   Populate based on the overall project vision and requirements (PRD).
*   When defining Scope (In/Out), Goals, or Success Criteria, use prefixes like `[Global]`, `[Frontend]`, `[Backend]`, `[VSTO]`, etc., if an item applies only to a specific subproject or is a shared responsibility.
*   Populate the `subprojects` list in YAML based on identified subprojects.

---

## 1. Project Title & Elevator Pitch

<!-- #Title -->
**Title:** [Enter Overall Project Name]

<!-- #Pitch -->
**Elevator Pitch:** [Provide a 1-2 sentence summary of the entire integrated solution.]

---

## 2. Core Goals & Objectives

<!-- #Goals -->
**Primary Goals:**
*   `[Global]` [List overall project goals.]
*   `[Frontend]` [List goals specific to the frontend subproject, e.g., Achieve specific usability metric.]
*   `[Backend]` [List goals specific to the backend, e.g., Handle X requests per second.]
*   `[VSTO]` [List goals specific to the VSTO Add-in.]
*   [Add other goals, clearly indicating global or subproject scope.]

---

## 3. Scope Definition

<!-- #ScopeIn -->
**In Scope:**
*   `[Global]` [Define overall included functionalities.]
*   `[Frontend]` [Define features specific to the frontend.]
*   `[Backend]` [Define APIs/capabilities specific to the backend.]
*   `[VSTO]` [Define features specific to the Add-in.]
*   [Example: `[Frontend]` User registration and login UI.]
*   [Example: `[Backend]` API endpoints for user auth and data management.]

<!-- #ScopeOut -->
**Out of Scope:**
*   `[Global]` [List features explicitly excluded from the entire project.]
*   `[Frontend]` [List UI features explicitly excluded.]
*   [Example: `[Global]` Mobile application version.]
*   [Example: `[Frontend]` Advanced user profile customization UI.]

---

## 4. Key Stakeholders

<!-- #Stakeholders -->
**Stakeholders:**
*   [List stakeholders, potentially noting their primary area of interest if relevant, e.g., Product Owner (Overall), Lead Frontend Dev (Frontend)]

---

## 5. Success Criteria

<!-- #SuccessCriteria -->
**How we measure success:**
*   `[Global]` [Define overall project success metrics.]
*   `[Frontend]` [Define frontend-specific success metrics, e.g., Page load time < X seconds.]
*   `[Backend]` [Define backend-specific success metrics, e.g., API response time < Y ms.]
*   [Add other criteria, indicating scope.]

---