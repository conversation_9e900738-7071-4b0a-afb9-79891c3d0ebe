# QuantBoost AI Coding Agent Instructions

This document provides guidance for AI coding agents working on the QuantBoost codebase. 

## Big Picture Architecture

The QuantBoost suite consists of several interconnected applications:

-   **`QuantBoost_API`**: A Node.js backend running on Azure Container Apps. It handles user management, licensing, and serves as the primary API for the client applications. Infrastructure is managed with Terraform (`QuantBoost_API/infrastructure`). 
-   **`QuantBoost_Frontend`**: A Next.js application deployed as an Azure Static Web App. It provides the main user interface for the QuantBoost service.
-   **`QuantBoost_PPTX`**: A VSTO PowerPoint Add-in written in C#. It integrates with the API for licensing and provides features like linking Excel data into presentations. Two main features - Excel Link and Presentation Size Analyzer.
-   **`QuantBoost_Excel`**: A VSTO Excel Add-in, also in C#. Two main features - Excel Trace and File Size Analyzer.
-   **`QuantBoost_Licensing`**: A shared C# library for license key validation and management, used by the .NET add-ins.
-   **`QuantBoost_Shared`**: A shared C# project containing common code for UI, security, and utilities across the .NET add-ins.
-   **Supabase**: Used as the primary database and for authentication. Use #supabase MCP tools for debugging and updating the database schema. Project ID: `izoutrnsxaaoueljiimu`. Schema summaary: `C:\VS projects\QuantBoost\memory-bank\memory-bank_api\supabase_tables.md`
-   **Stripe**: Used for payments and subscriptions. Webhooks flow from `QuantBoost_Frontend` to Supabase. API version is 2025-03-31.basil.
-   **GitHub**: Used for source control and CI/CD. Built as a MonoRepo. 
-   **Azure**: Used as the primary cloud platform for hosting and managing resources. Use AZ CLI for working with Azure resources - make sure user is logged in prior to running any commands. 

Data flows from the client applications (Frontend, Add-ins) to the `QuantBoost_API`, which then interacts with Supabase.

## Developer Workflows

### General Rules
-   **Reflect**: Before turning control back over to the human ask yourself, "what shortcuts did I take in this implementation" and then make sure to go back and fix them with production-quality code.

### Backend (`QuantBoost_API`)

-   **Setup**: `npm install`
-   **Development**: `npm run dev`
-   **Testing**: `npm test`
-   **Docker**: `npm run docker:compose` - currently we're not using Docker for local dev. 
-   **Deployment**: Push to the `main` branch, then manually trigger a GitHub Actions workflow to deploy to Azure via gh in the CLI `Build and Deploy to Azure - Backend API (Container App)`
-   **Infrastructure**: To provision or update the Azure infrastructure, use Terraform.exe in the `QuantBoost_API/infrastructure` directory. See the `README.md` there for details. Ensure the human is logged into Azure in the CLI before beginning. Separate infrastructure provisioning (Terraform) from application deployment (GitHub Actions).
-   **Azure**: `rg-quantboost-api-prod` -> `ca-quantboost-api` (Container App) located at `https://ca-quantboost-api.greentree-6ebc0a43.westus3.azurecontainerapps.io` in US West 3. 
-   **Debugging**: `az containerapp logs show --name ca-quantboost-api --resource-group rg-quantboost-api-prod --follow false --tail 50` 

### Frontend (`QuantBoost_Frontend`)

-   Standard Next.js project. Use absolute imports. Use `npm run dev` for development. 
-   **UI**: Use #shadcn-ui MCP server and shadcn UI components. UI uses Tailwind CSS. 
-   **Deployment**: Push to the `main` branch via gh, then manually trigger a GitHub Actions workflow to deploy to Azure via gh in the CLI `Deploy Frontend to Azure App Service (Staging)`
-   **Azure**: `rg-quantboost-frontend-staging` -> `app-quantboost-frontend-staging` (App Service) located at `app-quantboost-frontend-staging.azurewebsites.net`  in US Central. 
-   **Debugging**: `az webapp log tail --name app-quantboost-frontend-staging --resource-group rg-quantboost-frontend-staging`

### .NET Add-ins (`QuantBoost_PPTX`, `QuantBoost_Excel`)

-   Open `QuantBoost_PPTX/QuantBoost_Suite.sln` in Visual Studio.
-   Build and debug directly from Visual Studio.
-   These projects rely on shared logic from `QuantBoost_Licensing` and `QuantBoost_Shared`.
-   User is a senior .NET developer preferring '100x' approach for VSTO refactoring with production-grade quality.
-   User prefers MVVM architectural pattern with WPF UI technology for VSTO add-ins, with strict separation where View code-behind contains only UI-specific logic while business logic resides in ViewModels.
-   User prefers MVVM base classes (ViewModelBase, RelayCommand) to be placed in QuantBoost_Shared folder for reuse across both Excel and PowerPoint add-ins.
-   For Excel VSTO file size analyzer, use WPF UserControl hosted in Custom Task Pane, async analysis service for time-consuming operations, and MVVM pattern with separate View/ViewModel/Service layers.

## Project-Specific Conventions & Patterns

### API (`QuantBoost_API`)

-   **Authentication**: We use password-less email-based magic link authentication. 
-   **Logging**: Use the structured logger (Winston). Do not use `console.log`.
-   **Validation**: Use Joi for input validation on API routes.
-   **Supabase Client**: Use the shared client at `supabaseClient.js` for all Supabase interactions.
-   **Health Checks**: Standard health check endpoints are available at `/health/live` and `/health/ready`.

### PowerPoint Add-in (`QuantBoost_PPTX`)

-   **UI**: The add-in uses a mix of WinForms and WPF. For new UI features, prefer the MVVM pattern with WPF, following the example of the Excel Link Manager (`Features/ExcelLink`).
-   **Ribbon**: The main ribbon is defined in `UI/QuantBoostRibbon.cs`.
-   **State Management**: The add-in manages its own state, including authentication tokens stored via `TokenStorage.cs` in `QuantBoost_Shared`.

### Cross-Component Communication

-   The .NET add-ins communicate with the `QuantBoost_API` for licensing and user data. See `LicenseDetailsClient.cs` in `QuantBoost_Shared/UI` for an example of API communication.
-   Authentication tokens obtained by the add-ins are stored securely using `TokenStorage.cs`.

## Technical Constraints

-   **VSTO Add-ins (`QuantBoost_PPTX`, `QuantBoost_Excel`, `QuantBoost_Licensing`):**
    -   **.NET Framework**: 4.8.1
    -   **C# Version**: 7.3
-   **Backend (`QuantBoost_API`):**
    -   **Node.js**: 18+
    -   **Framework**: Express 5
-   **Frontend (`QuantBoost_Frontend`):**
    -   **Framework**: Next.js 15
    -   **React**: 19
    -   **TypeScript**: 5
-   **CLI:**
    -   **Azure**: AZ CLI 2.0+
    -   **GitHub**: gh CLI 2.0+
    -   **Terraform**: 1.9.8+
    -   **commands**: do not use && to chain commands. use ; instead.

## Security
`C:\VS projects\QuantBoost\memory-bank\memory-bank_security\SECURITY_GUIDELINES.md`

### Secrets Management
- Environment variables stored via Azure Key Vault and/or Azure Secrets/Environment Variables.
- No secrets in code or Docker images