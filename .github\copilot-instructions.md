Developer: # QuantBoost AI Coding Agent Guidelines

This document outlines best practices, architectural insights, and operational directives for AI agents contributing to QuantBoost.

**Begin with a concise checklist (3-7 bullets) of what you will do; keep items conceptual, not implementation-level.**

## 🌟 General Communication Rules
- **Detail-Oriented**: Provide comprehensive, in-depth responses at all times. ℹ️
- **Emojis Required**: Enrich all responses with relevant emojis to enhance clarity and engagement. 🎉
- **Self-Review**: Prior to returning results, internally reflect and identify any shortcuts used. Ensure revisions are made for clean, production-quality solutions. 🧠
- **Testing Discipline**: Always build or compile your work and verify successful execution before returning output. 🛠️

## 🔗 High-Level System Architecture
QuantBoost is a suite of applications with integrated workflows:

- **`QuantBoost_API`**: Node.js/Express 5 backend (Azure Container Apps). Handles core API logic, user management, licensing. Infra managed via Terraform at `QuantBoost_API/infrastructure`.
- **`QuantBoost_Frontend`**: Next.js 15 (React 19/TypeScript 5) app (Azure Static Web Apps). Delivers main user UI; uses shadcn UI and Tailwind CSS.
- **`QuantBoost_PPTX`**: PowerPoint VSTO Add-in (C#/.NET 4.8.1). Integrates API-based licensing, features Excel Link & Presentation Size Analyzer.
- **`QuantBoost_Excel`**: Excel VSTO Add-in (C#/.NET 4.8.1), with Excel Trace & File Size Analyzer.
- **`QuantBoost_Licensing`**: C# shared library for license key validation (used by all .NET add-ins).
- **`QuantBoost_Shared`**: C# library with common UI/security/utilities for .NET add-ins.
- **Supabase**: Primary DB/auth via #supabase MCP. Debug/update with MCP tools. Project ID: `izoutrnsxaaoueljiimu`, Schema: `C:\VS projects\QuantBoost\memory-bank\memory-bank_api\supabase_tables.md`.
- **Stripe**: Payments and subscriptions. Webhooks from frontend to Supabase. API version: 2025-03-31.basil.
- **GitHub**: MonoRepo for source control/CI/CD.
- **Azure**: Main cloud platform; use AZ CLI, ensuring user login before executing commands.

**Data Flow:** Client apps (frontend, add-ins) → `QuantBoost_API` → Supabase.

## ⚙️ Developer Workflows
### Backend (`QuantBoost_API`)
- **Setup:** `npm install`
- **Develop:** `npm run dev`
- **Test:** `npm test`
- **Docker:** `npm run docker:compose` (not standard for local dev)
- **Deploy:** Push to `main`, trigger GitHub Actions (`Build and Deploy to Azure - Backend API (Container App)`). Separate terraform infra changes (run in `QuantBoost_API/infrastructure`; see `README.md`).
- **Azure:** Deploys to `rg-quantboost-api-prod` → `ca-quantboost-api` (`https://ca-quantboost-api.greentree-6ebc0a43.westus3.azurecontainerapps.io`)
- **Debug:** Run `az containerapp logs show --name ca-quantboost-api --resource-group rg-quantboost-api-prod --follow false --tail 50`

### Frontend (`QuantBoost_Frontend`)
- Standard Next.js 15 project; absolute imports enforced.
- **Develop:** `npm run dev`
- **UI:** Use shadcn-ui components and Tailwind CSS.
- **Deploy:** Push to `main`, trigger GitHub Actions (`Deploy Frontend to Azure App Service (Staging)`).
- **Azure:** `rg-quantboost-frontend-staging` → `app-quantboost-frontend-staging` (`app-quantboost-frontend-staging.azurewebsites.net`)
- **Debug:** `az webapp log tail --name app-quantboost-frontend-staging --resource-group rg-quantboost-frontend-staging`

### .NET Add-ins (`QuantBoost_PPTX`, `QuantBoost_Excel`)
- Open `QuantBoost_PPTX/QuantBoost_Suite.sln` in Visual Studio.
- Build/debug from Visual Studio.
- Use **MVVM with WPF** for new/refactored UI (View code-behind for UI logic only; all business logic in ViewModels).
- **MVVM Base Classes**: Place reusable ViewModelBase/RelayCommand in `QuantBoost_Shared` for all add-ins.
- For Excel File Size Analyzer: Use WPF UserControl in Custom Task Pane, async analysis service, MVVM layering (UI/ViewModel/Service).

## 🎯 Project Conventions & Patterns
### API (`QuantBoost_API`)
- **Auth:** Passwordless magic link (email-based)
- **Logging:** Use Winston (no `console.log`)
- **Validation:** Use Joi for API route input validation
- **Supabase:** Use `supabaseClient.js`
- **Health Checks:** Accessible at `/health/live` & `/health/ready`

### PowerPoint Add-in (`QuantBoost_PPTX`)
- **UI:** Transition to MVVM+WPF for new features (see Excel Link Manager for reference implementation)
- **Ribbon:** `UI/QuantBoostRibbon.cs`
- **State:** Handles auth tokens via `TokenStorage.cs` in `QuantBoost_Shared`

### Cross-Component Communication
- .NET add-ins leverage `QuantBoost_API` for authentication/licensing
- See `LicenseDetailsClient.cs` in `QuantBoost_Shared/UI` for API interactions
- Tokens stored securely using `TokenStorage.cs`

## 📏 Technical Constraints
- **VSTO Add-ins:** .NET 4.8.1, C# 7.3
- **Backend:** Node.js 22, Express 5
- **Frontend:** Next.js 15, React 19, TypeScript 5
- **CLI Requirements:** AZ CLI 2.0+, gh CLI 2.0+, Terraform 1.9.8+
- **Shell Commands:** Never chain with `&&`; use `;` instead

## 🔒 Security Practices
Consult `C:\VS projects\QuantBoost\memory-bank\memory-bank_security\SECURITY_GUIDELINES.md` for comprehensive security policy.

**Secrets Management:**
- Store environment variables in Azure Key Vault and/or as Azure Secrets/Env Vars.
- Never place secrets in code or Docker images.

**Set reasoning_effort = medium based on the development and architectural complexity of QuantBoost; provide responsive, in-depth answers tailored to project depth.**