'use client'; // Required for onClick handlers

import { useState, useRef } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import AnimatedQuantBoostLogo from '@/components/AnimatedQuantBoostLogo';
import { motion, useScroll, useTransform, useSpring, Variants, LayoutGroup } from 'motion/react';
import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Badge
} from "@/components/ui";
import Image from 'next/image'; // For header logo
import { useRouter } from 'next/navigation';

// Ultra-fast animation variants
const fadeInUp: Variants = {
  hidden: { opacity: 0, y: 40 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const fadeInScale: Variants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: { duration: 0.8, ease: "easeOut" }
  }
};

const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
};

// Professional, subtle animation variants for enterprise SaaS
const cardHoverVariants: Variants = {
  idle: { 
    scale: 1, 
    y: 0,
    boxShadow: "0 2px 8px 0 rgba(0, 0, 0, 0.08)"
  },
  hover: { 
    scale: 1.01,
    y: -2,
    boxShadow: "0 8px 16px 0 rgba(0, 0, 0, 0.12)",
    transition: { 
      duration: 0.2,
      ease: "easeOut"
    }
  }
};

// Subtle badge animation
const popularBadgeVariants: Variants = {
  idle: { 
    scale: 1,
    opacity: 1
  },
  pulse: {
    scale: [1, 1.02, 1],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

// Professional CTA button hover
const ctaButtonVariants: Variants = {
  idle: { 
    scale: 1,
    boxShadow: "0 2px 4px 0 rgba(16, 185, 129, 0.2)"
  },
  hover: { 
    scale: 1.02,
    boxShadow: "0 4px 8px 0 rgba(16, 185, 129, 0.3)",
    transition: { 
      duration: 0.2,
      ease: "easeOut"
    }
  }
};

// Billing frequency type
type BillingFrequency = 'annual' | 'quarterly';

// Simplified product structure - Individual and Team plans use same price IDs
const planTypes = [
  {
    id: 'individual',
    name: 'Individual',
    description: 'Perfect for individual users',
    features: ['All Excel Modules', 'All PowerPoint Modules', 'Standard Support'],
    pricing: {
      annual: { price: 120, period: 'per year', stripePriceId: 'price_1RC3HTE6FvhUKV1bE9D6zf6e' },
      quarterly: { price: 45, period: 'per quarter', stripePriceId: 'price_1RCQeBE6FvhUKV1bUN94Oihf' }
    }
  },
  {
    id: 'team',
    name: 'Team',
    description: 'For teams and organizations',
    features: ['All Modules', 'License Management', 'Priority Support'],
    pricing: {
      annual: { price: 120, period: 'per user/year', stripePriceId: 'price_1RC3HTE6FvhUKV1bE9D6zf6e' },
      quarterly: { price: 45, period: 'per user/quarter', stripePriceId: 'price_1RCQeBE6FvhUKV1bUN94Oihf' }
    }
  }
];

export default function PricingPage() {
  const [isLoading, setIsLoading] = useState<string | null>(null); // Store the loading priceId
  const [billingFrequency, setBillingFrequency] = useState<BillingFrequency>('annual');
  const [teamQuantity, setTeamQuantity] = useState(5); // Default team size
  const router = useRouter();

  // Scroll effects setup
  const mainRef = useRef<HTMLElement>(null);
  const heroRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: mainRef,
    offset: ["start start", "end start"]
  });

  // Professional grid opacity effects - adjusted for shorter page
  const gridOpacity = useTransform(scrollYProgress, [0, 0.2, 0.5, 1], [0.03, 0.08, 0.12, 0.18]);
  const gridScale = useTransform(scrollYProgress, [0, 1], [1, 1.02]);
  const smoothGridOpacity = useSpring(gridOpacity, { stiffness: 50, damping: 25 });

  // Subtle parallax effects
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "20%"]);
  const smoothBackgroundY = useSpring(backgroundY, {
    stiffness: 100,
    damping: 30
  });

  const handleBuyNow = async (priceId: string, planId: string) => {
    setIsLoading(priceId);
    try {
      // For team plans, include quantity in the URL
      const url = planId === 'team'
        ? `/checkout/${priceId}?quantity=${teamQuantity}`
        : `/checkout/${priceId}`;
      router.push(url);
    } catch (error) {
      console.error('Error navigating to checkout:', error);
      // Handle error - show message to user?
    } finally {
      setIsLoading(null);
    }
  };

  // Calculate savings for annual billing
  const calculateAnnualSavings = (annualPrice: number, quarterlyPrice: number) => {
    const quarterlyYearly = quarterlyPrice * 4;
    const savings = quarterlyYearly - annualPrice;
    const percentage = Math.round((savings / quarterlyYearly) * 100);
    return { savings, percentage };
  };

  return (
    <>
      <Header />
      <main ref={mainRef} className="flex flex-col items-center justify-center min-h-screen overflow-hidden relative">
        {/* Excel Grid Background - Consistent with Help/Download Pages */}
        <motion.div 
          className="fixed inset-0 -z-20 overflow-hidden"
          style={{ 
            opacity: smoothGridOpacity,
            scale: gridScale
          }}
        >
          <svg
            className="absolute inset-0 w-full h-full"
            xmlns="http://www.w3.org/2000/svg"
            style={{ 
              width: '100%', 
              height: '100%',
              minHeight: '100vh'
            }}
          >
            <defs>
              <pattern
                id="excel-grid-pricing"
                x="0"
                y="0"
                width="40"
                height="24"
                patternUnits="userSpaceOnUse"
              >
                <rect
                  x="0"
                  y="0"
                  width="40"
                  height="24"
                  fill="none"
                  stroke="#10B981"
                  strokeWidth="0.5"
                  opacity="0.4"
                />
                <rect
                  x="0"
                  y="0"
                  width="200"
                  height="120"
                  fill="none"
                  stroke="#10B981"
                  strokeWidth="1"
                  opacity="0.6"
                  patternUnits="userSpaceOnUse"
                />
              </pattern>
            </defs>
            <rect
              x="0"
              y="0"
              width="100%"
              height="100%"
              fill="url(#excel-grid-pricing)"
            />
          </svg>
        </motion.div>

        {/* Hero Section */}
        <motion.section 
          ref={heroRef}
          className="text-center py-12 pt-24 px-6 relative z-10 max-w-5xl mx-auto w-full"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={staggerContainer}
        >
          {/* Parallax Background Blobs */}
          <motion.div
            className="absolute inset-0 -z-10 opacity-10"
            style={{ y: smoothBackgroundY }}
          >
            <div className="absolute top-20 left-10 w-72 h-72 bg-emerald-100 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div className="absolute top-40 right-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div className="absolute bottom-20 left-20 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
          </motion.div>

          {/* Logo and Branding */}
          <motion.div 
            className="mb-2 relative z-20"
            variants={fadeInScale}
          >
            <AnimatedQuantBoostLogo className="w-24 h-24 mx-auto mb-4" />
          </motion.div>

          {/* Header Content - Animated */}
          <motion.div className="space-y-4 mb-6 relative z-20" variants={fadeInUp}>
            <motion.h1 
              className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-gray-800"
              variants={fadeInUp}
            >
              Choose Your Plan
            </motion.h1>
            <motion.p 
              className="max-w-[700px] mx-auto text-gray-600 md:text-xl"
              variants={fadeInUp}
            >
              Unlock powerful features to boost your productivity!
            </motion.p>
          </motion.div>

          {/* Professional Motion-Based Billing Toggle - Enterprise UX */}
          <motion.div 
            className="flex justify-center items-center mb-8 relative z-20"
            variants={fadeInUp}
          >
            <div className="bg-gray-100 p-1.5 rounded-xl inline-flex shadow-sm border border-gray-200 relative">
              {/* Motion Toggle Options */}
              <motion.button
                onClick={() => setBillingFrequency('quarterly')}
                className={`px-6 py-3 rounded-lg text-sm font-semibold transition-colors relative z-10 ${
                  billingFrequency === 'quarterly'
                    ? 'text-gray-800'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {billingFrequency === 'quarterly' && (
                  <motion.div
                    layoutId="billing-toggle-indicator"
                    className="absolute inset-0 bg-white shadow-md rounded-lg border border-gray-200"
                    initial={false}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 30,
                      duration: 0.4
                    }}
                  />
                )}
                <span className="relative z-10">Quarterly</span>
              </motion.button>
              
              <motion.button
                onClick={() => setBillingFrequency('annual')}
                className={`px-6 py-3 rounded-lg text-sm font-semibold transition-colors relative z-10 ${
                  billingFrequency === 'annual'
                    ? 'text-gray-800'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {billingFrequency === 'annual' && (
                  <motion.div
                    layoutId="billing-toggle-indicator"
                    className="absolute inset-0 bg-white shadow-md rounded-lg border border-gray-200"
                    initial={false}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 30,
                      duration: 0.4
                    }}
                  />
                )}
                <span className="relative z-10 flex items-center gap-2">
                  Annual
                  <motion.div
                    variants={popularBadgeVariants}
                    initial="idle"
                    animate="pulse"
                  >
                    <Badge className="bg-emerald-500 text-white text-xs px-1.5 py-0.5 font-medium shadow-sm">
                      33% OFF
                    </Badge>
                  </motion.div>
                </span>
              </motion.button>
            </div>
          </motion.div>

          {/* Pricing Cards Container - Enhanced with LayoutGroup */}
          <motion.div 
            className="flex justify-center relative z-20"
            variants={staggerContainer}
          >
            <div className="w-full max-w-5xl">
              {/* Professional Animated Plan Cards with Motion Layout Coordination */}
              <LayoutGroup>
                <motion.div 
                  layout
                  className="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8 px-4 sm:px-6 lg:px-8"
                  variants={staggerContainer}
                  style={{ maxWidth: '750px', margin: '0 auto' }}
                >
            {planTypes.map((plan, index) => {
              const currentPricing = plan.pricing[billingFrequency];
              const savings = billingFrequency === 'annual'
                ? calculateAnnualSavings(plan.pricing.annual.price, plan.pricing.quarterly.price)
                : null;

              return (
                <motion.div
                  key={plan.id}
                  variants={fadeInUp}
                  custom={index}
                  className="relative"
                >
                  <Card
                    className={`group flex flex-col relative transition-all duration-300 bg-white/90 backdrop-blur-sm border-2 w-full ${
                      plan.id === 'team' ? 'border-emerald-500' : 'border-gray-200'
                    } hover:border-gray-800 relative z-20`}
                  >

                    {/* Professional Hover Animation */}
                    <motion.div
                      variants={cardHoverVariants}
                      whileHover="hover"
                      initial="idle"
                      className="h-full"
                    >
                      <CardHeader className="relative z-20 px-6 pt-2 pb-1">
                        <CardTitle className="text-2xl text-gray-800">{plan.name}</CardTitle>
                        <CardDescription className="text-gray-600">{plan.description}</CardDescription>
                      </CardHeader>
                      
                      <CardContent className="flex-grow space-y-4 relative z-20 px-6">
                        {/* Animated Pricing Display with Layout Transitions */}
                        <motion.div 
                          layout
                          className="space-y-2"
                          transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 30,
                            duration: 0.6
                          }}
                        >
                          {plan.id === 'team' ? (
                            <>
                              <motion.div 
                                layout
                                className="text-4xl font-bold text-gray-800"
                                key={`team-price-${billingFrequency}-${teamQuantity}`}
                              >
                                ${(currentPricing.price * teamQuantity).toLocaleString()}
                              </motion.div>
                              <motion.div 
                                layout
                                className="text-sm text-gray-600"
                                key={`team-period-${billingFrequency}-${teamQuantity}`}
                              >
                                ${currentPricing.price} × {teamQuantity} users {currentPricing.period.replace('per user/', '')}
                              </motion.div>
                              {billingFrequency === 'annual' && savings && (
                                <motion.div 
                                  layout
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  exit={{ opacity: 0, y: -10 }}
                                  className="text-sm text-emerald-600 font-medium bg-emerald-50 px-2 py-1 rounded-full inline-block"
                                  key={`team-savings-${teamQuantity}`}
                                >
                                  Save ${(savings.savings * teamQuantity).toLocaleString()}/year ({savings.percentage}% off)
                                </motion.div>
                              )}
                            </>
                          ) : (
                            <>
                            <motion.div 
                              layout
                              className="text-4xl font-bold text-gray-800"
                              key={`individual-price-${billingFrequency}`}
                            >
                              ${currentPricing.price}
                            </motion.div>
                            <motion.div 
                              layout
                              className="text-sm text-gray-600"
                              key={`individual-period-${billingFrequency}`}
                            >
                              {currentPricing.period}
                            </motion.div>
                            {billingFrequency === 'annual' && savings && (
                              <motion.div 
                                layout
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                className="text-sm text-emerald-600 font-medium bg-emerald-50 px-2 py-1 rounded-full inline-block"
                                key="individual-savings"
                              >
                                Save ${savings.savings}/year ({savings.percentage}% off)
                              </motion.div>
                            )}
                            </>
                          )}
                        </motion.div>

                        {/* Height Balancing Spacer for Individual Plan - Critical for Equal Card Heights */}
                        {plan.id === 'individual' && (
                          <div className="h-18" />
                        )}

                        {/* Compact Team Quantity Selector */}
                        {plan.id === 'team' && (
                          <div className="space-y-1 py-2">
                            <label className="text-sm font-medium text-gray-800">Team Size</label>
                            <div className="flex items-center justify-center gap-2">
                              <motion.button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setTeamQuantity(Math.max(1, teamQuantity - 1));
                                }}
                                className="w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center hover:bg-gray-100 hover:border-gray-400 transition-colors disabled:opacity-50"
                                disabled={teamQuantity <= 1}
                                whileHover={{ 
                                  scale: 1.1,
                                  transition: { type: "spring", stiffness: 400, damping: 25 }
                                }}
                                whileTap={{ 
                                  scale: 0.9,
                                  transition: { type: "spring", stiffness: 400, damping: 25 }
                                }}
                              >
                                <motion.span
                                  className="text-base font-bold leading-none flex items-center justify-center select-none"
                                  style={{ lineHeight: '1' }}
                                  whileHover={{ rotate: -5 }}
                                  whileTap={{ rotate: -10 }}
                                >
                                  −
                                </motion.span>
                              </motion.button>
                              
                              <motion.span 
                                className="w-12 text-center font-medium text-lg flex items-center justify-center"
                                key={teamQuantity}
                                initial={{ scale: 0.8, opacity: 0.5 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ type: "spring", stiffness: 500, damping: 30 }}
                              >
                                {teamQuantity}
                              </motion.span>
                              
                              <motion.button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setTeamQuantity(teamQuantity + 1);
                                }}
                                className="w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center hover:bg-gray-100 hover:border-gray-400 transition-colors"
                                whileHover={{ 
                                  scale: 1.1,
                                  transition: { type: "spring", stiffness: 400, damping: 25 }
                                }}
                                whileTap={{ 
                                  scale: 0.9,
                                  transition: { type: "spring", stiffness: 400, damping: 25 }
                                }}
                              >
                                <motion.span
                                  className="text-base font-bold leading-none flex items-center justify-center select-none"
                                  style={{ lineHeight: '1' }}
                                  whileHover={{ rotate: 5 }}
                                  whileTap={{ rotate: 10 }}
                                >
                                  +
                                </motion.span>
                              </motion.button>
                            </div>
                          </div>
                        )}

                        {/* Animated Feature List with Reduced Bottom Spacing */}
                        <ul className="space-y-2 text-sm mb-4">
                          {plan.features.map((feature, index) => (
                            <motion.li 
                              key={index} 
                              className="flex items-center gap-3"
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1, duration: 0.5 }}
                            >
                              <CheckIcon className="h-4 w-4 text-emerald-500 flex-shrink-0" />
                              <span className="text-gray-700">{feature}</span>
                            </motion.li>
                          ))}
                        </ul>
                      </CardContent>
                      
                      <CardFooter className="relative z-20 px-6 pt-1 pb-2">
                        <motion.div
                          variants={ctaButtonVariants}
                          whileHover="hover"
                          initial="idle"
                          className="w-full"
                        >
                          <Button
                            className={`w-full transition-all duration-300 text-lg font-semibold py-3 ${
                              plan.id === 'team'
                                ? 'bg-emerald-500 text-white hover:bg-emerald-600 border-emerald-500'
                                : 'bg-white text-gray-800 border-2 border-gray-300 hover:bg-gray-800 hover:text-white hover:border-gray-800'
                            }`}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleBuyNow(currentPricing.stripePriceId, plan.id);
                            }}
                            disabled={isLoading === currentPricing.stripePriceId}
                          >
                            {isLoading === currentPricing.stripePriceId ? (
                              <div className="flex items-center gap-2">
                                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                                Processing...
                              </div>
                            ) : (
                              `Buy Now`
                            )}
                          </Button>
                        </motion.div>
                      </CardFooter>
                    </motion.div>
                  </Card>
                </motion.div>
              );
            })}
              </motion.div>
              </LayoutGroup>
            </div>
          </motion.div>

          {/* Custom Solution Section - Enhanced */}
          <motion.div 
            className="text-center mt-16 space-y-4 max-w-4xl mx-auto relative z-20"
            variants={fadeInUp}
          >
            <motion.h2 
              className="text-2xl font-bold tracking-tighter text-gray-800"
              variants={fadeInUp}
            >
              Still Have Questions?
            </motion.h2>
            <motion.p 
              className="max-w-[600px] mx-auto text-gray-600"
              variants={fadeInUp}
            >
              Contact us for enterprise licensing, volume discounts, or custom requirements.
            </motion.p>
            <motion.div variants={fadeInUp}>
              <Button 
                variant="outline" 
                asChild
                className="border-2 border-gray-300 hover:bg-gray-800 hover:text-white hover:border-gray-800 transition-all duration-300"
              >
                <Link href="/help">Contact Sales</Link>
              </Button>
            </motion.div>
          </motion.div>
        </motion.section>

        {/* Enhanced Footer */}
        <motion.footer 
          className="flex flex-col gap-2 sm:flex-row py-6 w-full shrink-0 items-center px-4 md:px-6 border-t border-gray-200 bg-white/80 backdrop-blur-sm relative z-20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
        >
          <p className="text-xs text-gray-600">&copy; 2025 QuantBoost. All rights reserved.</p>
          <nav className="sm:ml-auto flex gap-4 sm:gap-6">
            <Link href="#" className="text-xs hover:underline underline-offset-4 text-gray-600 hover:text-gray-800" prefetch={false}>
              Terms of Service
            </Link>
            <Link href="#" className="text-xs hover:underline underline-offset-4 text-gray-600 hover:text-gray-800" prefetch={false}>
              Privacy
            </Link>
          </nav>
        </motion.footer>
      </main>
    </>
  );
}

// Helper CheckIcon component (assuming it's not globally available)
function CheckIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 6 9 17l-5-5" />
    </svg>
  );
}
