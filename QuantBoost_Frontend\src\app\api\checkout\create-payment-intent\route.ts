import { NextRequest, NextResponse } from 'next/server';
import Strip<PERSON> from 'stripe';
import { ENV, validateStripeConfig, getEnvironmentDebugInfo } from '@/lib/env';

// Valid price IDs for QuantBoost subscriptions - Individual and Team use same price IDs
const VALID_PRICE_IDS = {
  'price_1RC3HTE6FvhUKV1bE9D6zf6e': { // Annual (Individual or Team based on quantity)
    name: 'Annual Plan',
    description: 'Full access, billed annually.',
    productId: 'prod_S6Fn893jGxRhKk'
  },
  'price_1RyhsAE6FvhUKV1bImD5Ft34': { // Monthly (Individual or Team based on quantity)
    name: 'Monthly Plan',
    description: 'Full access, billed monthly.',
    productId: 'prod_S6Fn893jGxRhKk'
  }
};

export async function POST(req: NextRequest) {
  const started = Date.now();
  const debugInfo = getEnvironmentDebugInfo();
  const stripeValidation = validateStripeConfig();
  if (!stripeValidation.isValid) {
    console.error('stripe.config.invalid', stripeValidation);
    return NextResponse.json({ error: 'Payment service unavailable' }, { status: 500 });
  }

  const stripe = new Stripe(ENV.STRIPE_SECRET_KEY!, { apiVersion: '2025-08-27.basil' as any });

  try {
    const body = await req.json();
  const { priceId, userId, email, quantity = 1, customerInfo } = body || {};

    // Basic validation
    if (!priceId) return NextResponse.json({ error: 'Missing priceId' }, { status: 400 });
    const priceDetails = VALID_PRICE_IDS[priceId as keyof typeof VALID_PRICE_IDS];
    if (!priceDetails) return NextResponse.json({ error: 'Invalid priceId' }, { status: 400 });
    // Email required (captured via LinkAuthenticationElement before this call)
    if (!email || typeof email !== 'string' || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email) || email === '<EMAIL>') {
      return NextResponse.json({ error: 'Valid email required' }, { status: 400 });
    }

    // 1. Find or create customer by email (idempotent via search)
  let customer: Stripe.Customer | null = null;
  const existingCustomers = await stripe.customers.list({ email, limit: 1 });
    if (existingCustomers.data.length > 0) {
      customer = existingCustomers.data[0];
      // Optional lightweight update if new info supplied
      const updatePayload: Stripe.CustomerUpdateParams = {};
      if (customerInfo?.firstName && customerInfo?.lastName) {
        updatePayload.name = `${customerInfo.firstName} ${customerInfo.lastName}`;
      }
      if (customerInfo?.phone) updatePayload.phone = customerInfo.phone;
      if (customerInfo?.addressLine1 && customerInfo?.city && customerInfo?.country) {
        updatePayload.address = {
          line1: customerInfo.addressLine1,
          line2: customerInfo.addressLine2 || undefined,
          city: customerInfo.city,
          state: customerInfo.state || undefined,
          postal_code: customerInfo.postalCode || undefined,
          country: customerInfo.country,
        };
      }
      if (customer && Object.keys(updatePayload).length > 0) {
        try { customer = await stripe.customers.update(customer.id, updatePayload); } catch { /* swallow minor update failures */ }
      }
    } else {
      customer = await stripe.customers.create({
        email,
        name: customerInfo?.firstName && customerInfo?.lastName ? `${customerInfo.firstName} ${customerInfo.lastName}` : undefined,
        phone: customerInfo?.phone || undefined,
        address: customerInfo?.addressLine1 && customerInfo?.city && customerInfo?.country ? {
          line1: customerInfo.addressLine1,
          line2: customerInfo.addressLine2 || undefined,
          city: customerInfo.city,
          state: customerInfo.state || undefined,
          postal_code: customerInfo.postalCode || undefined,
          country: customerInfo.country,
        } : undefined,
        metadata: { app: 'quantboost', userId: userId || '' }
      });
    }

    // 2. Attempt to find a reusable active/incomplete subscription for same customer+price (avoid duplicates on rapid retries)
    // We can't filter server-side by metadata priceId directly; list few recent subs.
  if (!customer) return NextResponse.json({ error: 'Customer creation failed' }, { status: 500 });
  const existingSubs = await stripe.subscriptions.list({ customer: customer.id, status: 'all', limit: 10, expand: ['data.latest_invoice.payment_intent'] });
    let reusableSub: Stripe.Subscription | null = null;
    for (const sub of existingSubs.data) {
      if (sub.items.data.some(i => (i.price.id === priceId)) && ['incomplete', 'trialing', 'active', 'past_due'].includes(sub.status)) {
        // If incomplete, ensure we have a payment_intent client_secret to return (latest invoice)
        reusableSub = sub;
        break;
      }
    }

    let subscription: Stripe.Subscription;
    if (reusableSub && reusableSub.latest_invoice) {
      subscription = reusableSub;
      console.log(JSON.stringify({ msg: 'subscription.reuse', subId: subscription.id, status: subscription.status, customerId: customer.id }));
    } else {
      // Idempotency key to avoid duplicate creates on fast double-submit (customer+price+qty)
      const idemKey = `sub:${customer.id}:${priceId}:${quantity}`;
      subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: priceId, quantity }],
        payment_behavior: 'default_incomplete',
        payment_settings: {
          save_default_payment_method: 'on_subscription',
          payment_method_types: ['card', 'link']
        },
        metadata: {
          priceId,
          email,
          userId: userId || '',
          productName: priceDetails.name,
          quantity: String(quantity),
          app: 'quantboost'
        },
        expand: ['latest_invoice.payment_intent']
      }, { idempotencyKey: idemKey });
      console.log(JSON.stringify({ msg: 'subscription.create', subId: subscription.id, customerId: customer.id, idemKey }));
    }

    // 3. Resolve PaymentIntent from latest invoice (expand above ensures object where possible)
  let invoice: Stripe.Invoice | null = null;
    if (typeof subscription.latest_invoice === 'string') {
      invoice = await stripe.invoices.retrieve(subscription.latest_invoice, { expand: ['payment_intent'] });
    } else {
      invoice = subscription.latest_invoice as Stripe.Invoice | null;
    }
  if (!invoice) return NextResponse.json({ error: 'Invoice missing' }, { status: 500 });

    // Resolve the invoice's PaymentIntent; never create a standalone PI to avoid orphaned charges
    let pi: Stripe.PaymentIntent | null = null;
    const resolveInvoicePI = async (): Promise<Stripe.PaymentIntent | null> => {
      const raw = (invoice as any).payment_intent;
      if (raw) return typeof raw === 'string' ? await stripe.paymentIntents.retrieve(raw) : raw as Stripe.PaymentIntent;
      return null;
    };

    pi = await resolveInvoicePI();

    // If not immediately available, perform a short bounded retry to allow Stripe to populate it
    if (!pi) {
      for (let attempt = 1; attempt <= 3 && !pi; attempt++) {
        await new Promise(r => setTimeout(r, 250 * attempt));
        const refreshed: Stripe.Invoice = await stripe.invoices.retrieve((invoice as Stripe.Invoice).id as string, { expand: ['payment_intent'] });
        invoice = refreshed;
        pi = await resolveInvoicePI();
        if (pi) {
          console.log(JSON.stringify({ msg: 'payment_intent.resolve.retry', attempt, id: pi.id }));
          break;
        }
      }
    }

    if (!pi) {
      // If still missing, return a retriable error rather than creating a separate PI
      console.warn('payment_intent.resolve.failed', { invoiceId: (invoice as Stripe.Invoice).id, subscriptionId: subscription.id });
      return NextResponse.json({ error: 'Payment is initializing, please retry shortly.' }, { status: 503 });
    }

    return NextResponse.json({
      clientSecret: pi.client_secret,
      subscriptionId: subscription.id,
      paymentIntentId: pi.id,
  customerId: customer.id,
  productName: priceDetails.name,
      description: priceDetails.description,
      reusedSubscription: reusableSub ? true : false,
      durationMs: Date.now() - started
    });
  } catch (err: any) {
    console.error('subscription.route.error', { message: err?.message, stack: err?.stack });
    return NextResponse.json({ error: 'Failed to create subscription' }, { status: 500 });
  }
}