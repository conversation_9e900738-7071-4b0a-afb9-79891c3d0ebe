"use client";

// Disable static generation for this page since it uses Supabase client
export const dynamic = 'force-dynamic';

import { useEffect, useState, Suspense, useRef, useMemo } from 'react';
import { useSearchParams } from 'next/navigation';
import { useSupabaseClient } from '../../hooks/useSupabaseClient';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button, Badge, Separator } from "@/components/ui";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { <PERSON><PERSON><PERSON>, Form<PERSON>ieldLabel, FormFieldError } from "@/components/ui/form-field";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/toast";
import { apiClient, Subscription, License, isApiSuccess, handleApiError } from '@/lib/api';
import Link from 'next/link';
// Stripe Elements for secure card collection
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

interface DashboardStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  totalLicenses: number;
  activeLicenses: number;
  activatedLicenses: number;
  assignedLicenses: number;
  availableLicenses: number;
}

interface SubscriptionWithLicenses extends Subscription {
  licenses: License[];
}

interface ChargeReceipt {
  id: string;
  amount: number;
  created_at: string;
  status: string;
  receipt_url?: string;
}

interface BillingOverview {
  nextBillingDate?: string;
  billingInterval?: string;
  nextBillingAmount?: number;
}

function DashboardContent() {
  const searchParams = useSearchParams();
  const { addToast } = useToast();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<{ id?: string; email?: string } | null>(null);
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithLicenses[]>([]);
  const [selectedSubscription, setSelectedSubscription] = useState<SubscriptionWithLicenses | null>(null);
  const [receipts, setReceipts] = useState<ChargeReceipt[]>([]);
  const [billingOverview, setBillingOverview] = useState<BillingOverview | null>(null);
  const [stats, setStats] = useState<DashboardStats>({
    totalSubscriptions: 0,
    activeSubscriptions: 0,
    totalLicenses: 0,
    activeLicenses: 0,
    activatedLicenses: 0,
    assignedLicenses: 0,
    availableLicenses: 0,
  });
  const [error, setError] = useState<string | null>(null);
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState("subscription");
  const [cancelLoading, setCancelLoading] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [undoLoading, setUndoLoading] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [currentPaymentMethod, setCurrentPaymentMethod] = useState<any>(null);
  // Simplified payment form state (remove email and raw card fields)
  const [paymentFormData, setPaymentFormData] = useState({
    billingName: '',
    billingAddress: {
      line1: '',
      line2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'US'
    }
  });
  const [paymentUpdateLoading, setPaymentUpdateLoading] = useState(false);
  const [paymentFormErrors, setPaymentFormErrors] = useState<{[key: string]: string}>({});
  const supabase = useSupabaseClient();
  const receiptsRetry = useRef(false);

  // Combined effect to handle auth tokens and fetch dashboard data
  useEffect(() => {
    const initializeDashboard = async () => {
      try {
        // Check for payment success
        const paymentSuccess = searchParams.get('payment_success');
        if (paymentSuccess === 'true') {
          setShowPaymentSuccess(true);
          setTimeout(() => setShowPaymentSuccess(false), 5000);
        }

        // Handle OAuth tokens if present
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        if (accessToken && refreshToken) {
          try {
            await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            });
          } catch (error) {
            console.error('Error setting OAuth session:', error);
          }
        }

        await fetchDashboardData();
      } catch (error) {
        console.error('Dashboard initialization error:', error);
        setError('Failed to initialize dashboard');
      }
    };

    initializeDashboard();
  }, [searchParams, supabase]);

  // Fetch payment method when subscription changes - COMMENTED OUT due to 404 error
  /* 
  useEffect(() => {
    if (selectedSubscription) {
      fetchCurrentPaymentMethod();
    }
  }, [selectedSubscription]);
  */

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('No authenticated user found');
      }
      setUser({ id: user.id, email: user.email });

      // Fetch subscriptions with licenses
      const { data: subscriptionsData } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id);

      if (subscriptionsData) {
        const subscriptionsWithLicenses = await Promise.all(
          subscriptionsData.map(async (sub: Subscription) => {
            const { data: licensesData } = await supabase
              .from('licenses')
              .select('*')
              .eq('subscription_id', sub.id);
            
            return {
              ...sub,
              licenses: licensesData || []
            };
          })
        );

        setSubscriptions(subscriptionsWithLicenses);
        if (subscriptionsWithLicenses.length > 0) {
          setSelectedSubscription(subscriptionsWithLicenses[0]);
        }

        // Calculate stats
        const totalSubscriptions = subscriptionsWithLicenses.length;
        const activeSubscriptions = subscriptionsWithLicenses.filter(s => s.status === 'active').length;
        const allLicenses = subscriptionsWithLicenses.flatMap(s => s.licenses);
        const totalLicenses = allLicenses.length;
        const activeLicenses = allLicenses.filter(l => l.status === 'active').length;
        const activatedLicenses = allLicenses.filter(l => l.email && l.email !== 'not-activated' && l.email !== '').length;
        const assignedLicenses = allLicenses.filter(l => l.email && l.email !== 'not-activated' && l.email !== '').length;
        const availableLicenses = allLicenses.filter(l => !l.email || l.email === 'not-activated' || l.email === '').length;

        setStats({
          totalSubscriptions,
          activeSubscriptions,
          totalLicenses,
          activeLicenses,
          activatedLicenses,
          assignedLicenses,
          availableLicenses,
        });

        // Fetch billing data
        await fetchBillingData(user.id, subscriptionsWithLicenses);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchBillingData = async (userId: string, subscriptions: Subscription[]) => {
    try {
      // Fetch receipts for the user
      const receiptsResponse = await fetch('/api/billing/receipts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId }),
      });

      if (receiptsResponse.ok) {
        const receiptsData = await receiptsResponse.json();
        // Accept both array response and wrapped object
        const parsed = Array.isArray(receiptsData) ? receiptsData : (receiptsData.receipts || []);
        setReceipts(parsed);
        // If just completed checkout or first load produced no receipts, try a one-time quick refresh
        if (!receiptsRetry.current && parsed.length === 0) {
          receiptsRetry.current = true;
          setTimeout(async () => {
            try {
              const r2 = await fetch('/api/billing/receipts', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ userId }),
              });
              if (r2.ok) {
                const d2 = await r2.json();
                setReceipts(Array.isArray(d2) ? d2 : (d2.receipts || []));
              }
            } catch {}
          }, 2500);
        }
      }

      // Fetch billing overview for the first active subscription
      const activeSubscription = subscriptions.find(s => s.status === 'active');
      if (activeSubscription) {
        try {
          // Fetch upcoming invoice for billing amount and interval
          const upcomingResponse = await fetch('/api/billing/upcoming-invoice', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
              subscriptionId: activeSubscription.stripe_subscription_id 
            }),
          });

          let nextBillingAmount = 0;
          let actualBillingInterval = null;
          if (upcomingResponse.ok) {
            const upcomingData = await upcomingResponse.json();
            nextBillingAmount = upcomingData.amount_due / 100; // Convert from cents
            actualBillingInterval = upcomingData.billing_interval;
          }

          // If we couldn't get billing interval, try the subscription details endpoint
          if (!actualBillingInterval) {
            try {
              const detailsResponse = await fetch('/api/billing/subscription-details', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                  subscriptionId: activeSubscription.stripe_subscription_id 
                }),
              });

              if (detailsResponse.ok) {
                const detailsData = await detailsResponse.json();
                actualBillingInterval = detailsData.billing_interval;
              }
            } catch (detailsError) {
              console.error('Error fetching subscription details:', detailsError);
            }
          }

          setBillingOverview({
            nextBillingDate: activeSubscription.current_period_end,
            billingInterval: actualBillingInterval || 'unknown',
            nextBillingAmount: activeSubscription.cancel_at_period_end ? 0 : nextBillingAmount,
          });
        } catch (error) {
          console.error('Error fetching billing overview:', error);
          
          // Final fallback - try to get at least the billing interval
          let fallbackInterval = null;
          try {
            const detailsResponse = await fetch('/api/billing/subscription-details', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ 
                subscriptionId: activeSubscription.stripe_subscription_id 
              }),
            });

            if (detailsResponse.ok) {
              const detailsData = await detailsResponse.json();
              fallbackInterval = detailsData.billing_interval;
            }
          } catch (fallbackError) {
            console.error('Error fetching fallback subscription details:', fallbackError);
          }

          // Set basic billing overview without amount
          setBillingOverview({
            nextBillingDate: activeSubscription.current_period_end,
            billingInterval: fallbackInterval || 'unknown',
            nextBillingAmount: activeSubscription.cancel_at_period_end ? 0 : undefined,
          });
        }
      }
    } catch (error) {
      console.error('Error fetching billing data:', error);
    }
  };

  // Refresh receipts when switching subscription (and we have a user)
  useEffect(() => {
    if (user?.id && selectedSubscription) {
      (async () => {
        try {
          const res = await fetch('/api/billing/receipts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId: user.id }),
          });
          if (res.ok) {
            const data = await res.json();
            setReceipts(Array.isArray(data) ? data : (data.receipts || []));
          }
        } catch {}
      })();
    }
  }, [selectedSubscription?.stripe_subscription_id]);

  const handleManageSubscription = async () => {
    try {
      setLoading(true);
      const response = await apiClient.createBillingPortalSession(window.location.href);
      if (isApiSuccess(response)) {
        window.location.href = response.data.url;
      } else {
        addToast({
          type: "error", 
          title: "Billing Portal Error",
          description: "Failed to redirect to billing portal"
        });
      }
    } catch (error) {
      console.error('Error managing subscription:', error);
      addToast({
        type: "error", 
        title: "Billing Portal Error",
        description: "Failed to redirect to billing portal"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    setShowCancelDialog(true);
  };

  const confirmCancelSubscription = async () => {
    if (!selectedSubscription) return;

    try {
      setCancelLoading(true);
      // Use Stripe API to cancel at period end
      const response = await fetch('/api/billing/cancel-subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          subscriptionId: selectedSubscription.stripe_subscription_id,
          cancelAtPeriodEnd: true
        }),
      });

      if (response.ok) {
        // Optimistically flip cancel flag locally so UI updates immediately
        setSelectedSubscription(prev => prev ? { ...prev, cancel_at_period_end: true } as any : prev);
        setSubscriptions(prev => prev.map(s => s.id === selectedSubscription.id ? ({ ...s, cancel_at_period_end: true } as any) : s));
        setShowCancelDialog(false);
        await fetchDashboardData(); // Refresh data
        addToast({
          type: "success",
          title: "Subscription Scheduled for Cancellation",
          description: "Your subscription will be canceled at the end of the current billing period."
        });
      } else {
        addToast({
          type: "error",
          title: "Cancellation Failed",
          description: "Failed to cancel subscription. Please try again or contact support."
        });
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      addToast({
        type: "error",
        title: "Cancellation Failed", 
        description: "Failed to cancel subscription. Please try again or contact support."
      });
    } finally {
      setCancelLoading(false);
    }
  };

  const handleUndoCancellation = async () => {
    if (!selectedSubscription) return;

    try {
      setUndoLoading(true);
      const response = await fetch('/api/billing/undo-cancellation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          subscriptionId: selectedSubscription.stripe_subscription_id
        }),
      });

      if (response.ok) {
        // Optimistically clear cancel flag locally
        setSelectedSubscription(prev => prev ? { ...prev, cancel_at_period_end: false } as any : prev);
        setSubscriptions(prev => prev.map(s => s.id === selectedSubscription.id ? ({ ...s, cancel_at_period_end: false } as any) : s));
        await fetchDashboardData(); // Refresh data
        addToast({
          type: "success",
          title: "Cancellation Undone",
          description: "Your subscription cancellation has been undone successfully!"
        });
      } else {
        addToast({
          type: "error",
          title: "Undo Failed",
          description: "Failed to undo subscription cancellation. Please try again or contact support."
        });
      }
    } catch (error) {
      console.error('Error undoing subscription cancellation:', error);
      addToast({
        type: "error",
        title: "Undo Failed",
        description: "Failed to undo subscription cancellation. Please try again or contact support."
      });
    } finally {
      setUndoLoading(false);
    }
  };

  // Utility function to calculate days until subscription ends
  const getDaysUntilEnd = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Check if subscription is cancelled but still active
  const isSubscriptionCancelled = () => {
    return selectedSubscription?.cancel_at_period_end === true;
  };

  // Format billing interval for display
  const formatBillingInterval = (interval: string | null | undefined) => {
    switch (interval) {
      case 'month':
        return 'monthly';
      case 'year':
        return 'annual';
      case 'quarter':
        return 'quarterly';
      case '3month':
      case '3_month':
      case '3months':
      case '3_months':
        return 'quarterly';
      case 'week':
        return 'weekly';
      case 'day':
        return 'daily';
      default:
        return interval || 'unknown';
    }
  };

  // Child component to handle Stripe Elements submission
  const StripePaymentForm = ({ subscriptionId }: { subscriptionId: string }) => {
    const stripe = useStripe();
    const elements = useElements();
    const cardElementOptions = useMemo(() => ({ hidePostalCode: true }), []);

    // Local form state to avoid re-rendering parent and remounting CardElement
    const [formData, setFormData] = useState({
      billingName: '',
      billingAddress: { line1: '', line2: '', city: '', state: '', postalCode: '', country: 'US' }
    });
    const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

    const validate = () => {
      const errors: { [key: string]: string } = {};
      if (!formData.billingName.trim()) errors.billingName = 'Billing name is required';
      if (!formData.billingAddress.line1.trim()) errors.addressLine1 = 'Address is required';
      if (!formData.billingAddress.city.trim()) errors.city = 'City is required';
      if (!formData.billingAddress.state.trim()) errors.state = 'State is required';
      if (!formData.billingAddress.postalCode.trim()) errors.postalCode = 'ZIP code is required';
      setFormErrors(errors);
      return Object.keys(errors).length === 0;
    };

    const onSubmit = async () => {
      if (!stripe || !elements) return;
      if (!validate()) {
        addToast({ type: 'error', title: 'Validation Error', description: 'Please fill in required fields.' });
        return;
      }
      try {
        setPaymentUpdateLoading(true);
        // 1) Create SetupIntent
        const siResp = await fetch('/api/billing/create-setup-intent', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ subscriptionId })
        });
        if (!siResp.ok) {
          const e = await siResp.json().catch(() => ({}));
          throw new Error(e.error || 'Failed to start payment update');
        }
        const { client_secret } = await siResp.json();

        // 2) Confirm with Elements
        const cardElement = elements.getElement(CardElement);
        if (!cardElement) throw new Error('Card input not ready');

        const result = await stripe.confirmCardSetup(client_secret, {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: formData.billingName,
              address: {
                line1: formData.billingAddress.line1,
                line2: formData.billingAddress.line2,
                city: formData.billingAddress.city,
                state: formData.billingAddress.state,
                postal_code: formData.billingAddress.postalCode,
                country: formData.billingAddress.country
              }
            }
          }
        });

        if (result.error) {
          throw new Error(result.error.message || 'Stripe confirmation failed');
        }

        const setupIntent = result.setupIntent;
        const paymentMethodId = setupIntent?.payment_method as string;
        if (!paymentMethodId) throw new Error('No payment method returned');

        // 3) Attach and set default on backend
        const setResp = await fetch('/api/billing/set-default-payment-method', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ subscriptionId, paymentMethodId })
        });
        if (!setResp.ok) {
          const e = await setResp.json().catch(() => ({}));
          throw new Error(e.error || 'Failed to set default payment method');
        }

        setShowPaymentForm(false);
        setFormErrors({});
        setFormData({ billingName: '', billingAddress: { line1: '', line2: '', city: '', state: '', postalCode: '', country: 'US' } });
        addToast({ type: 'success', title: 'Payment Method Updated', description: 'Your payment method has been updated successfully.' });
      } catch (err: any) {
        console.error('Error updating payment method:', err);
        addToast({ type: 'error', title: 'Update Failed', description: err.message || 'Failed to update payment method. Please try again.' });
      } finally {
        setPaymentUpdateLoading(false);
      }
    };

    return (
      <div className="p-6 bg-white border rounded-lg shadow-sm">
        <div className="space-y-4">
          <h4 className="font-medium text-lg">Update Payment Method</h4>
          <div className="space-y-3">
            <FormField>
              <FormFieldLabel required>Card</FormFieldLabel>
              <div className="p-3 border rounded-md bg-white">
                <CardElement options={cardElementOptions} />
              </div>
            </FormField>
            <FormField>
              <FormFieldLabel required>Full Name</FormFieldLabel>
              <Input
                type="text"
                placeholder="John Doe"
                value={formData.billingName}
                onChange={(e) => setFormData(prev => ({ ...prev, billingName: e.target.value }))}
              />
              <FormFieldError>{formErrors.billingName}</FormFieldError>
            </FormField>
            <FormField>
              <FormFieldLabel required>Address</FormFieldLabel>
              <Input
                type="text"
                placeholder="123 Main St"
                value={formData.billingAddress.line1}
                onChange={(e) => setFormData(prev => ({ ...prev, billingAddress: { ...prev.billingAddress, line1: e.target.value } }))}
              />
              <FormFieldError>{formErrors.addressLine1}</FormFieldError>
            </FormField>
            <FormField>
              <FormFieldLabel>Address Line 2</FormFieldLabel>
              <Input
                type="text"
                placeholder="Apt, suite, etc."
                value={formData.billingAddress.line2}
                onChange={(e) => setFormData(prev => ({ ...prev, billingAddress: { ...prev.billingAddress, line2: e.target.value } }))}
              />
            </FormField>
            <div className="grid grid-cols-3 gap-4">
              <FormField>
                <FormFieldLabel required>City</FormFieldLabel>
                <Input
                  type="text"
                  placeholder="New York"
                  value={formData.billingAddress.city}
                  onChange={(e) => setFormData(prev => ({ ...prev, billingAddress: { ...prev.billingAddress, city: e.target.value } }))}
                />
                <FormFieldError>{formErrors.city}</FormFieldError>
              </FormField>
              <FormField>
                <FormFieldLabel required>State</FormFieldLabel>
                <Input
                  type="text"
                  placeholder="NY"
                  value={formData.billingAddress.state}
                  onChange={(e) => setFormData(prev => ({ ...prev, billingAddress: { ...prev.billingAddress, state: e.target.value } }))}
                />
                <FormFieldError>{formErrors.state}</FormFieldError>
              </FormField>
              <FormField>
                <FormFieldLabel required>ZIP Code</FormFieldLabel>
                <Input
                  type="text"
                  placeholder="10001"
                  value={formData.billingAddress.postalCode}
                  onChange={(e) => {
                    const value = e.target.value.replace(/[^\w\s-]/gi, '');
                    setFormData(prev => ({ ...prev, billingAddress: { ...prev.billingAddress, postalCode: value } }));
                  }}
                />
                <FormFieldError>{formErrors.postalCode}</FormFieldError>
              </FormField>
            </div>
          </div>
          <div className="flex gap-2 pt-4 border-t">
            <Button onClick={onSubmit} disabled={paymentUpdateLoading || !stripe}>
              {paymentUpdateLoading ? 'Updating...' : 'Update Payment Method'}
            </Button>
            <Button variant="outline" onClick={() => { setShowPaymentForm(false); setFormErrors({}); }}>
              Cancel
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="flex justify-between items-center">
        <div>
          {/* Removed Dashboard H1 as requested */}
          <p className="text-muted-foreground">
            {`Welcome back${user?.email ? `, ${user.email}` : ''}!`}
          </p>
        </div>
      </div>

      {showPaymentSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-600 text-xl">✅</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">
                Payment Successful!
              </h3>
              <div className="mt-1 text-sm text-green-700">
                <p>Your subscription is now active. Welcome to QuantBoost!</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Subscriptions</CardTitle>
            <span className="text-2xl">📊</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalSubscriptions}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeSubscriptions} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Licenses</CardTitle>
            <span className="text-2xl">🎫</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalLicenses}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeLicenses} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned</CardTitle>
            <span className="text-2xl">👤</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.assignedLicenses}</div>
            <p className="text-xs text-muted-foreground">
              Ready for activation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <span className="text-2xl">🆓</span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.availableLicenses}</div>
            <p className="text-xs text-muted-foreground">
              Ready to assign
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Billing Information */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Billing Information</CardTitle>
            <CardDescription>
              Manage your billing and view payment history
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Compact Billing Overview with Actions */}
            {billingOverview && (
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <p className="text-xs font-medium text-gray-600">
                      {isSubscriptionCancelled() ? 'Subscription Ends' : 'Next Billing'}
                    </p>
                    <p className="text-sm font-bold text-gray-900">
                      {billingOverview.nextBillingDate ? (
                        isSubscriptionCancelled() ? (
                          `in ${getDaysUntilEnd(billingOverview.nextBillingDate)} days`
                        ) : (
                          new Date(billingOverview.nextBillingDate).toLocaleDateString()
                        )
                      ) : 'N/A'}
                    </p>
                  </div>
                  <Separator orientation="vertical" className="h-8" />
                  <Badge 
                    variant={isSubscriptionCancelled() ? "destructive" : "secondary"} 
                    className="text-xs"
                  >
                    {isSubscriptionCancelled() 
                      ? 'Ending soon'
                      : `${formatBillingInterval(billingOverview.billingInterval)} billing`
                    }
                  </Badge>
                  {billingOverview.nextBillingAmount !== undefined && (
                    // ...existing amount UI...
                    <>
                      <Separator orientation="vertical" className="h-8" />
                      <div className="text-center">
                        <p className="text-xs font-medium text-gray-600">Next Amount</p>
                        <p className="text-sm font-bold text-gray-900">
                          ${billingOverview.nextBillingAmount.toFixed(2)}
                        </p>
                      </div>
                    </>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    disabled={loading || !selectedSubscription}
                    onClick={() => setShowPaymentForm(!showPaymentForm)}
                  >
                    {showPaymentForm ? 'Cancel Update' : 'Update Payment'}
                  </Button>
                  {isSubscriptionCancelled() ? (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleUndoCancellation}
                      disabled={!selectedSubscription || undoLoading}
                      className="border-amber-300 text-amber-700 hover:bg-amber-100"
                    >
                      {undoLoading ? 'Processing...' : 'Undo Cancellation'}
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelSubscription}
                      disabled={!selectedSubscription || cancelLoading}
                      className="border-red-300 text-red-700 hover:bg-red-100"
                    >
                      {cancelLoading ? 'Canceling...' : 'Cancel Subscription'}
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* Update Payment Method (moved above Payment History) */}
            {showPaymentForm && selectedSubscription && (
              <Elements stripe={stripePromise}>
                <StripePaymentForm subscriptionId={selectedSubscription.stripe_subscription_id} />
              </Elements>
            )}

            {/* Payment History */}
            <div>
              <h4 className="text-lg font-medium mb-3">Payment History</h4>
              {receipts.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-sm text-muted-foreground">No payment receipts found.</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Payment receipts will appear here once you have made payments.
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Receipt</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {receipts.map((receipt) => (
                      <TableRow key={receipt.id}>
                        <TableCell>{new Date(receipt.created_at).toLocaleDateString()}</TableCell>
                        <TableCell>${(receipt.amount / 100).toFixed(2)}</TableCell>
                        <TableCell>
                          <span className={`text-xs px-2 py-1 rounded-full ${getReceiptStatusColor(receipt.status)}`}>
                            {receipt.status === 'succeeded' ? 'Paid' : receipt.status}
                          </span>
                        </TableCell>
                        <TableCell>
                          {receipt.receipt_url ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(receipt.receipt_url, '_blank')}
                            >
                              View Receipt
                            </Button>
                          ) : (
                            <span className="text-gray-400 text-sm">Not available</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cancellation Confirmation Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel your subscription? You will continue to have access until{' '}
              {billingOverview?.nextBillingDate ? (
                new Date(billingOverview.nextBillingDate).toLocaleDateString()
              ) : (
                'the end of your current billing period'
              )}
              .
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCancelDialog(false)}>
              Keep Subscription
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmCancelSubscription}
              disabled={cancelLoading}
            >
              {cancelLoading ? 'Canceling...' : 'Yes, Cancel Subscription'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default function Dashboard() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <DashboardContent />
    </Suspense>
  );
}

function getReceiptStatusColor(status: string) {
  switch (status) {
    case 'succeeded':
      return 'bg-green-100 text-green-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'failed':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
