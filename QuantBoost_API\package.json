{"name": "quantboost_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix", "docker:build": "docker build -t quantboost-api .", "docker:run": "docker run -p 3000:3000 --env-file .env quantboost-api", "docker:compose": "docker-compose up --build"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@azure/monitor-opentelemetry": "^1.11.1", "@supabase/supabase-js": "^2.49.4", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "winston": "^3.17.0"}, "devDependencies": {"dotenv-cli": "^8.0.0", "eslint": "^9.29.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.0"}}