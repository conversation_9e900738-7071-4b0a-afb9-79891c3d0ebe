import { Page, Frame, APIResponse } from '@playwright/test';

/**
 * Resilient Stripe Elements discovery & interaction utilities.
 */
const DEBUG = (process.env.DEBUG_STRIPE || '').toLowerCase() === 'true';
function d(msg: string, meta?: any) { if (DEBUG) console.log(`[stripe-helper] ${msg}`, meta ?? ''); }

async function findFrameWithSelector(page: Page, selector: string, timeout = 10000): Promise<Frame | null> {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    for (const frame of page.frames()) {
      try {
  const el = await frame.$(selector);
  if (el) { d(`Found '${selector}' in frame`, frame.url()); return frame; }
      } catch { /* ignore */ }
    }
    await page.waitForTimeout(150);
  }
  return null;
}

export async function fillLinkEmail(page: Page, email: string): Promise<boolean> {
  const direct = await page.$('input[type=email], input[autocomplete="email"], [data-testid=email-input]');
  if (direct) { await direct.fill(email); return true; }
  const frame = await findFrameWithSelector(page, 'input[type=email], input[autocomplete=email]');
  if (!frame) return false;
  const emailEl = await frame.$('input[type=email], input[autocomplete=email]');
  if (!emailEl) return false; await emailEl.fill(email); return true;
}

export async function fillCardDetails(page: Page, opts: { number: string; exp?: string; cvc?: string; postal?: string }): Promise<boolean> {
  const { number, exp = '1230', cvc = '123', postal } = opts;
  // Attempt classic multi-iframe Card Element model first
  const cardFrame = await findFrameWithSelector(page, 'input[name="cardnumber"], input[placeholder*="Card number" i], input[aria-label*="Card number" i]');
  if (cardFrame) {
    const numberEl = await cardFrame.$('input[name="cardnumber"], input[placeholder*="Card number" i], input[aria-label*="Card number" i]');
    if (!numberEl) return false; await numberEl.fill(number);
    const expFrame = (await findFrameWithSelector(page, 'input[name="exp-date"], input[placeholder*="MM / YY" i], input[placeholder*="MM/YY" i], input[aria-label*="Expiration" i]')) || cardFrame;
    const expEl = await expFrame.$('input[name="exp-date"], input[placeholder*="MM / YY" i], input[placeholder*="MM/YY" i], input[aria-label*="Expiration" i]'); if (expEl) await expEl.fill(exp);
    const cvcFrame = (await findFrameWithSelector(page, 'input[name="cvc"], input[placeholder*="CVC" i], input[placeholder*="CVV" i], input[aria-label*="CVC" i]')) || cardFrame;
    const cvcEl = await cvcFrame.$('input[name="cvc"], input[placeholder*="CVC" i], input[placeholder*="CVV" i], input[aria-label*="CVC" i]'); if (cvcEl) await cvcEl.fill(cvc);
    if (postal) {
      const postalFrame = (await findFrameWithSelector(page, 'input[name="postal"], input[autocomplete="postal-code"], input[placeholder*="ZIP" i], input[aria-label*="ZIP" i]')) || cardFrame;
      const postalEl = await postalFrame.$('input[name="postal"], input[autocomplete="postal-code"], input[placeholder*="ZIP" i], input[aria-label*="ZIP" i]'); if (postalEl) await postalEl.fill(postal);
    }
    return true;
  }

  // Fallback: Payment Element unified iframe approach.
  // Strategy: find a Stripe frame containing an input-like region, click it, then type the full sequence.
  const stripeFrames = page.frames().filter(f => /stripe/i.test(f.url()));
  d('Stripe frames detected for unified Payment Element fallback', stripeFrames.map(f => f.url()));
  for (const frame of stripeFrames) {
    try {
      // Try more specific selectors first
      const numberField = await frame.$('[data-elements-stable-field-type="cardNumber"] input, input[name="cardnumber"], input[aria-label*="Card" i], input[placeholder*="Card" i], input[type=tel]');
      const candidate = numberField || await frame.$('input, form [role="textbox"], [contenteditable="true"]');
      if (candidate) {
        await candidate.click({ force: true });
        // Type sequence: card number + space + exp + space + cvc + (optional postal) relying on internal field auto-advance
        await candidate.type(number.replace(/\s+/g, ''));
        await candidate.type(' ' + exp.replace('/', '') + ' ' + cvc);
        if (postal) await candidate.type(' ' + postal);
        return true;
      }
    } catch { /* continue */ }
  }
  return false;
}

export async function waitForPaymentReady(page: Page, timeout = 15000): Promise<boolean> {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    const stripeFrames = page.frames().filter(f => /stripe/i.test(f.url()));
    const stripeFramePresent = stripeFrames.length > 0;
    const paymentContainer = await page.$('[data-testid=qb-payment-element], .PaymentElement, [data-testid=PaymentElement]');
    // Detect classic cardnumber iframe even if container not matched
    let cardIframeFound = false;
    for (const f of stripeFrames) {
      try {
        if (await f.$('input[name="cardnumber"], input[placeholder*="Card number" i]')) { cardIframeFound = true; break; }
      } catch { /* ignore */ }
    }
    const submitVisible = await page.$('[data-testid=qb-complete-button], button[data-testid=submit], button[type=submit], button:has-text("Complete Purchase"), button:has-text("Complete"), button:has-text("Pay"), button:has-text("Subscribe")');
    if (stripeFramePresent && submitVisible && (paymentContainer || cardIframeFound)) return true;
    await page.waitForTimeout(250);
  }
  d('waitForPaymentReady timed out');
  return false;
}

/**
 * Wait for the app to finish creating / finalizing the Payment Intent (stage after email entry).
 * Heuristics:
 *  1. Network response containing create-payment-intent (or similar) 2xx
 *  2. Console log containing 'payment intent ready' | 'finalClientSecret'
 *  3. Email input becomes disabled / readonly
 *  4. PaymentElement container appears
 */
export async function waitForIntentReady(page: Page, timeout = 15000): Promise<boolean> {
  const start = Date.now();
  const consoleEvents: string[] = [];
  function onConsole(msg: any) { const txt = msg.text(); consoleEvents.push(txt); }
  page.on('console', onConsole);
  try {
    let networkHit = false;
    // Fire and forget network wait (best effort)
    const networkPromise = page.waitForResponse(resp => /payment-intent|create-payment-intent|finalize-intent/i.test(resp.url()) && resp.status() < 400, { timeout }).then(() => { networkHit = true; }).catch(()=>{});
    while (Date.now() - start < timeout) {
      // Condition 4 early quick check
      const paymentContainer = await page.$('.PaymentElement, [data-testid=PaymentElement]');
      if (paymentContainer) { d('Intent ready: PaymentElement container present'); return true; }
      // Condition 3 email disabled
      const emailInput = await page.$('input[type=email], input[autocomplete="email"], [data-testid=email-input]');
      if (emailInput) {
        const disabled = await emailInput.isDisabled().catch(()=>false);
        const ro = await emailInput.getAttribute('readonly');
        if (disabled || ro !== null) { d('Intent ready: email input locked'); return true; }
      }
      // Condition 2 console marker
      if (consoleEvents.some(c => /payment intent ready|finalClientSecret/i.test(c))) { d('Intent ready: console marker'); return true; }
      // Condition 1 network
      if (networkHit) { d('Intent ready: network create-payment-intent observed (awaiting container)'); }
      await page.waitForTimeout(200);
    }
    d('waitForIntentReady timed out', { consoleEvents });
    return false;
  } finally {
    page.off('console', onConsole);
  }
}

/** Collects diagnostic information about Stripe frames & Payment Element for troubleshooting. */
export async function collectStripeDiagnostics(page: Page) {
  const frames = page.frames().filter(f => /stripe/i.test(f.url()));
  const details: any[] = [];
  for (const f of frames) {
    try {
      const html = await f.evaluate(() => document.body.innerHTML.slice(0, 500)).catch(()=> 'eval-failed');
      const inputs = await f.$$eval('input', els => els.map(e => ({ name: (e as HTMLInputElement).name, placeholder: (e as HTMLInputElement).placeholder, aria: (e as HTMLElement).getAttribute('aria-label') })).slice(0,10)).catch(()=>[]);
      details.push({ url: f.url(), inputs, snippet: html });
    } catch {
      details.push({ url: f.url(), error: 'unreadable' });
    }
  }
  const paymentContainerExists = !!await page.$('[data-testid=qb-payment-element], .PaymentElement, [data-testid=PaymentElement]');
  d('Stripe diagnostics', { frames: details.length, paymentContainerExists });
  return { frameCount: frames.length, frames: details, paymentContainerExists };
}

/** Tick required consent checkboxes (terms & privacy) */
export async function acceptRequiredConsents(page: Page) {
  const selectors = ['[data-testid=qb-terms]', '[data-testid=qb-privacy]'];
  for (const sel of selectors) {
    const el = await page.$(sel);
    if (el) {
      try {
        const checked = await el.isChecked();
        if (!checked) await el.check({ force: true });
      } catch { /* ignore */ }
    }
  }
}

export async function submitPayment(page: Page): Promise<boolean> {
  const selectors = [
    'button[data-testid=submit]',
    'button[type=submit]',
    'button:has-text("Pay")',
    'button:has-text("Subscribe")',
    'button:has-text("Complete")',
    'button:has-text("Confirm")'
  ];
  for (const sel of selectors) {
    const el = await page.$(sel);
    if (el) {
      try {
        await el.waitForElementState('enabled', { timeout: 5000 }).catch(()=>{});
        await el.click();
        d('Clicked submit', sel);
        return true;
      } catch (e) { d('Submit click failed', { sel, e }); }
    }
  }
  d('No submit button found');
  return false;
}

export async function handleThreeDSIfPresent(page: Page, timeout = 15000): Promise<boolean> {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    const challengeFrame = page.frames().find(f => /stripe|3ds|authentication/i.test(f.url()));
    if (challengeFrame) {
      const complete = await challengeFrame.$('text=/Complete authentication/i, text=/Submit/i, text=/Authorize/i');
      if (complete) { await complete.click(); return true; }
    }
    await page.waitForTimeout(300);
  }
  return false;
}

export async function waitForReceiptOrSuccess(page: Page, timeout = 15000): Promise<boolean> {
  try { await page.waitForSelector('text=/thank you|success|receipt/i', { timeout }); return true; } catch { return false; }
}
