import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

export async function POST(request: NextRequest) {
  try {
    const { subscriptionId, paymentMethodId } = await request.json();
    if (!subscriptionId || !paymentMethodId) {
      return NextResponse.json({ error: 'subscriptionId and paymentMethodId are required' }, { status: 400 });
    }

    const key = (globalThis as any).process?.env?.STRIPE_SECRET_KEY as string | undefined;
    if (!key) {
      return NextResponse.json({ error: 'Stripe secret key not configured' }, { status: 500 });
    }
    const stripe = new Stripe(key, { apiVersion: '2025-03-31.basil' });

    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const customerId = subscription.customer as string;
    if (!customerId) {
      return NextResponse.json({ error: 'Customer not found for subscription' }, { status: 404 });
    }

    // Ensure payment method is attached to the customer (SetupIntent usually attaches it already)
    try {
      const pm = await stripe.paymentMethods.retrieve(paymentMethodId);
      const owner = typeof pm.customer === 'string' ? pm.customer : null;
      if (!owner) {
        await stripe.paymentMethods.attach(paymentMethodId, { customer: customerId });
      } else if (owner !== customerId) {
        return NextResponse.json({ error: 'Payment method belongs to a different customer' }, { status: 409 });
      }
    } catch (e: any) {
      // Ignore if already attached
      const code = e?.code || e?.raw?.code;
      if (code !== 'resource_already_exists' && code !== 'payment_method_unexpected_state') {
        throw e;
      }
    }

    // Update default on subscription and customer invoice settings
    await stripe.subscriptions.update(subscriptionId, { default_payment_method: paymentMethodId });
    await stripe.customers.update(customerId, { invoice_settings: { default_payment_method: paymentMethodId } });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error setting default payment method:', error);
    return NextResponse.json({ error: 'Failed to set default payment method' }, { status: 500 });
  }
}
