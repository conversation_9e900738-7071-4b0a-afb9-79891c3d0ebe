import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    const { subscriptionId, paymentMethodId } = await request.json();
    if (!subscriptionId || !paymentMethodId) {
      return NextResponse.json({ error: 'subscriptionId and paymentMethodId are required' }, { status: 400 });
    }

    // Auth: require JWT
    const authHeader = request.headers.get('authorization') || '';
    const jwt = authHeader.startsWith('Bearer ') ? authHeader.substring(7) : null;
    if (!jwt) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    if (!supabaseServiceKey || !supabaseUrl) {
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }
    const supabase = createClient(supabaseUrl, supabaseServiceKey, { global: { headers: { Authorization: `Bearer ${jwt}` } } });
    const { data: userResp, error: userErr } = await supabase.auth.getUser(jwt);
    if (userErr || !userResp?.user) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
    }
    const userId = userResp.user.id;

    // Ownership check against internal subscriptions table
    const { data: subRow, error: subErr } = await supabase
      .from('subscriptions')
      .select('stripe_subscription_id, user_id')
      .eq('stripe_subscription_id', subscriptionId)
      .single();
    if (subErr || !subRow) {
      return NextResponse.json({ error: 'Subscription not found' }, { status: 404 });
    }
    if (subRow.user_id !== userId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const key = (globalThis as any).process?.env?.STRIPE_SECRET_KEY as string | undefined;
    if (!key) {
      return NextResponse.json({ error: 'Stripe secret key not configured' }, { status: 500 });
    }
    const stripe = new Stripe(key, { apiVersion: '2025-03-31.basil' });

    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const customerId = subscription.customer as string;
    if (!customerId) {
      return NextResponse.json({ error: 'Customer not found for subscription' }, { status: 404 });
    }

    // Ensure payment method is attached to the customer (SetupIntent usually attaches it already)
    try {
      const pm = await stripe.paymentMethods.retrieve(paymentMethodId);
      const owner = typeof pm.customer === 'string' ? pm.customer : null;
      if (!owner) {
        await stripe.paymentMethods.attach(paymentMethodId, { customer: customerId });
      } else if (owner !== customerId) {
        return NextResponse.json({ error: 'Payment method belongs to a different customer' }, { status: 409 });
      }
    } catch (e: any) {
      // Ignore if already attached
      const code = e?.code || e?.raw?.code;
      if (code !== 'resource_already_exists' && code !== 'payment_method_unexpected_state') {
        throw e;
      }
    }

    // Update default on subscription and customer invoice settings
    await stripe.subscriptions.update(subscriptionId, { default_payment_method: paymentMethodId });
    await stripe.customers.update(customerId, { invoice_settings: { default_payment_method: paymentMethodId } });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error setting default payment method:', error);
    return NextResponse.json({ error: 'Failed to set default payment method' }, { status: 500 });
  }
}
