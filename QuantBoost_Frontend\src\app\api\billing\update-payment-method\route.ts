import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
});

export async function POST(request: NextRequest) {
  try {
    const { subscriptionId, paymentMethod } = await request.json();

    if (!subscriptionId || !paymentMethod) {
      return NextResponse.json(
        { error: 'Subscription ID and payment method details are required' },
        { status: 400 }
      );
    }

    // Get the subscription to find the customer
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    if (!subscription.customer) {
      return NextResponse.json(
        { error: 'Customer not found for this subscription' },
        { status: 404 }
      );
    }

    const customerId = subscription.customer as string;

    // Create a new payment method
    const newPaymentMethod = await stripe.paymentMethods.create({
      type: 'card',
      card: {
        number: paymentMethod.card.number.replace(/\s/g, ''), // Remove spaces
        exp_month: paymentMethod.card.exp_month,
        exp_year: paymentMethod.card.exp_year,
        cvc: paymentMethod.card.cvc,
      },
      billing_details: paymentMethod.billing_details,
    });

    // Attach the payment method to the customer
    await stripe.paymentMethods.attach(newPaymentMethod.id, {
      customer: customerId,
    });

    // Update the subscription's default payment method
    await stripe.subscriptions.update(subscriptionId, {
      default_payment_method: newPaymentMethod.id,
    });

    // Optionally, update the customer's invoice settings
    await stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: newPaymentMethod.id,
      },
    });

    return NextResponse.json({ 
      success: true, 
      paymentMethod: newPaymentMethod 
    });
  } catch (error: any) {
    console.error('Error updating payment method:', error);
    
    let errorMessage = 'Failed to update payment method';
    if (error.type === 'StripeCardError') {
      errorMessage = error.message;
    } else if (error.code === 'card_declined') {
      errorMessage = 'Your card was declined. Please try a different payment method.';
    } else if (error.code === 'expired_card') {
      errorMessage = 'Your card has expired. Please check your card details.';
    } else if (error.code === 'incorrect_cvc') {
      errorMessage = 'Your card\'s security code is incorrect.';
    } else if (error.code === 'processing_error') {
      errorMessage = 'An error occurred while processing your card. Please try again.';
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 400 }
    );
  }
}
