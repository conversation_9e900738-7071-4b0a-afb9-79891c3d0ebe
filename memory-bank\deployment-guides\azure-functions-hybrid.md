# Azure Functions + Static Web Apps Hybrid Solution

## Overview
Keep frontend on Static Web Apps while moving payment API routes to dedicated Azure Functions for reliable environment variable access.

## Step 1: Create Azure Functions App

### Setup Function App Resource
```bash
# Create Function App
az functionapp create \
  --resource-group quantboost-rg \
  --consumption-plan-location eastus \
  --runtime node \
  --runtime-version 18 \
  --functions-version 4 \
  --name quantboost-api \
  --storage-account quantbooststorage
```

### Configure Function App Settings
```bash
# Stripe Configuration
az functionapp config appsettings set \
  --name quantboost-api \
  --resource-group quantboost-rg \
  --settings \
    STRIPE_SECRET_KEY="sk_test_51R..." \
    STRIPE_WEBHOOK_SECRET="whsec_..." \
    SUPABASE_SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
    NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co" \
    CORS_ALLOWED_ORIGINS="https://purple-glacier-0ab50190f.1.azurestaticapps.net"
```

## Step 2: Create Azure Function for Payment Processing

### Project Structure
```
azure-functions/
├── package.json
├── host.json
├── local.settings.json
└── src/
    └── functions/
        ├── createPaymentIntent.js
        └── stripeWebhook.js
```

### package.json
```json
{
  "name": "quantboost-payment-functions",
  "version": "1.0.0",
  "scripts": {
    "build": "tsc",
    "watch": "tsc -w",
    "prestart": "npm run build",
    "start": "func start",
    "test": "echo \"No tests yet...\""
  },
  "dependencies": {
    "@azure/functions": "^4.0.0",
    "stripe": "^14.21.0",
    "@supabase/supabase-js": "^2.39.3"
  },
  "devDependencies": {
    "@azure/functions": "^4.0.0",
    "typescript": "^4.0.0"
  }
}
```

### src/functions/createPaymentIntent.js
```javascript
const { app } = require('@azure/functions');
const Stripe = require('stripe');

app.http('createPaymentIntent', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'checkout/create-payment-intent',
    handler: async (request, context) => {
        // CORS headers
        const corsHeaders = {
            'Access-Control-Allow-Origin': process.env.CORS_ALLOWED_ORIGINS || '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        };

        // Handle preflight requests
        if (request.method === 'OPTIONS') {
            return {
                status: 200,
                headers: corsHeaders
            };
        }

        try {
            // Environment variables are now reliably accessible
            const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
                apiVersion: '2025-03-31.basil',
            });

            const body = await request.json();
            const { priceId, email, quantity = 1 } = body;

            context.log('Creating subscription for:', { priceId, email, quantity });

            // Create customer
            const customer = await stripe.customers.create({
                email: email,
                metadata: {
                    source: 'quantboost_checkout'
                }
            });

            // Create subscription
            const subscription = await stripe.subscriptions.create({
                customer: customer.id,
                items: [{
                    price: priceId,
                    quantity: quantity
                }],
                payment_behavior: 'default_incomplete',
                payment_settings: {
                    save_default_payment_method: 'on_subscription'
                },
                expand: ['latest_invoice.payment_intent'],
                metadata: {
                    source: 'quantboost_checkout',
                    email: email
                }
            });

            // Extract payment intent
            const invoice = subscription.latest_invoice;
            const paymentIntent = invoice.payment_intent;

            if (!paymentIntent) {
                throw new Error('No payment intent found for subscription');
            }

            return {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
                body: JSON.stringify({
                    subscriptionId: subscription.id,
                    clientSecret: paymentIntent.client_secret,
                    customerId: customer.id
                })
            };

        } catch (error) {
            context.log.error('Error creating subscription:', error);
            
            return {
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                    ...corsHeaders
                },
                body: JSON.stringify({
                    error: 'Failed to create subscription',
                    details: error.message
                })
            };
        }
    }
});
```

### host.json
```json
{
  "version": "2.0",
  "logging": {
    "applicationInsights": {
      "samplingSettings": {
        "isEnabled": true,
        "excludedTypes": "Request"
      }
    }
  },
  "extensionBundle": {
    "id": "Microsoft.Azure.Functions.ExtensionBundle",
    "version": "[4.*, 5.0.0)"
  },
  "functionTimeout": "00:05:00"
}
```

## Step 3: Update Frontend to Use Azure Functions

### Update API calls in Frontend
```typescript
// lib/api.ts
const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://quantboost-api.azurewebsites.net/api'
  : 'http://localhost:7071/api';

export async function createPaymentIntent(data: {
  priceId: string;
  email: string;
  quantity?: number;
}) {
  const response = await fetch(`${API_BASE_URL}/checkout/create-payment-intent`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
}
```

### Update Environment Variables in Static Web Apps
```bash
# Only frontend environment variables needed now
az staticwebapp appsettings set \
  --name purple-glacier-0ab50190f \
  --setting-names \
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51R..." \
    NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co" \
    NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
    NEXT_PUBLIC_API_BASE_URL="https://quantboost-api.azurewebsites.net/api"
```

## Step 4: GitHub Actions for Functions Deployment

### .github/workflows/azure-functions.yml
```yaml
name: Deploy Azure Functions

on:
  push:
    branches: [ main ]
    paths: [ 'azure-functions/**' ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'azure-functions/package-lock.json'
    
    - name: Install dependencies
      run: |
        cd azure-functions
        npm ci
    
    - name: Deploy to Azure Functions
      uses: Azure/functions-action@v1
      with:
        app-name: 'quantboost-api'
        package: './azure-functions'
        publish-profile: ${{ secrets.AZURE_FUNCTIONAPP_PUBLISH_PROFILE }}
```

## Step 5: CORS Configuration

### Configure CORS in Azure Functions
```bash
# Allow Static Web Apps domain
az functionapp cors add \
  --name quantboost-api \
  --resource-group quantboost-rg \
  --allowed-origins "https://purple-glacier-0ab50190f.1.azurestaticapps.net"
```

## Step 6: Cost Analysis

### Hybrid Approach Costs:
- **Static Web Apps**: $9/month (frontend)
- **Azure Functions**: ~$0-20/month (consumption plan, depends on usage)
- **Total**: ~$9-29/month

### Benefits:
- **Lower cost** than full App Service
- **Reliable API routes** with proper environment variables
- **Scalable** serverless functions
- **Keep existing Static Web Apps setup** for frontend

## Step 7: Migration Timeline

### Phase 1 (Day 1-2): Azure Functions Setup
- Create Function App resource
- Develop payment processing functions
- Configure environment variables
- Test locally

### Phase 2 (Day 3): Integration
- Update frontend API calls
- Deploy functions to Azure
- Test end-to-end payment flow
- Configure CORS

### Phase 3 (Day 4): Production
- Update production environment variables
- Deploy to production
- Monitor and validate

## Pros and Cons

### Pros:
- **Cost-effective**: Lower than App Service
- **Reliable API**: Environment variables work properly
- **Scalable**: Serverless functions scale automatically
- **Minimal frontend changes**: Keep Static Web Apps

### Cons:
- **Complexity**: Managing two separate services
- **Cold starts**: Potential latency for infrequent requests
- **CORS management**: Additional configuration needed
- **Debugging**: More complex troubleshooting across services
