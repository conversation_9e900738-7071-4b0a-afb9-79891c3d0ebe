# issues.md - Bug Tracking and Debugging Context (Multi-Project Workspace)

**Purpose:** Provides a structured list for tracking known bugs and errors, clearly associating each issue with its relevant subproject.

**Instructions for Dane:**
*   **CRITICAL:** For every issue logged, specify the relevant `Subproject`.

---

## Tracked Issues

<!-- #Issue -->
*   **ID:** Issue-001
*   **Title:** [Brief, descriptive title]
*   **Subproject:** `[Enter Subproject Identifier, e.g., Frontend, Backend, VSTO, Shared Lib]`
*   **Status:** [Open | In Progress | Blocked | Resolved]
*   **Severity:** [High | Medium | Low] (Optional)
*   **Reported Date:** [Date]
*   **Description:**
    *   [Detailed description.]
    *   **Steps to Reproduce:**
        1.  [Step 1]
        2.  [Step 2]
    *   **Expected Behavior:** [What should happen]
    *   **Actual Behavior:** [What currently happens, include error messages verbatim]
*   **Affected Files/Components:**
    *   `[subproject-name]/path/to/relevant/file1.ext]`
    *   `[Component Name]`
*   **Assignee:** [Dane | Specific Person]
*   **Troubleshooting/Notes:**
    *   [Timestamp] Tried X, result was Y.
    *   [Add notes.]
*   **Resolution (if Status=Resolved):**
    *   [Description of the fix.]
    *   **Fixed In:** [Commit SHA or PR link]

---

<!-- #Issue -->
*   **ID:** Issue-002
*   **Title:** [...]
*   **Subproject:** `[Specify Subproject]`
*   **Status:** [...]
*   [...]

---

<!-- Add new issues following the format above -->