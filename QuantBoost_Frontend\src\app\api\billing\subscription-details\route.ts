import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

export async function POST(request: NextRequest) {
  try {
    const { subscriptionId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      );
    }

    const key = (globalThis as any).process?.env?.STRIPE_SECRET_KEY as string | undefined;
    if (!key) {
      return NextResponse.json({ error: 'Stripe secret key not configured' }, { status: 500 });
    }
    const stripe = new Stripe(key, { apiVersion: '2025-03-31.basil' });

    // Get the subscription with expanded price information
    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['items.data.price'],
    });

    // Extract billing interval and period information from the first price
    let billingInterval = null;
    let priceAmount = null;
    let currency = subscription.currency || 'usd';
    let currentPeriodStart = null;
    let currentPeriodEnd = null;

    if (subscription.items && subscription.items.data && subscription.items.data.length > 0) {
      const firstItem = subscription.items.data[0];
      if (firstItem.price && typeof firstItem.price === 'object') {
        if (firstItem.price.recurring && firstItem.price.recurring.interval) {
          billingInterval = firstItem.price.recurring.interval;
        }
        if (firstItem.price.unit_amount) {
          priceAmount = firstItem.price.unit_amount;
        }
      }
      // Get period information from subscription item
      currentPeriodStart = (firstItem as any).current_period_start ?? null;
      currentPeriodEnd = (firstItem as any).current_period_end ?? null;
    }

    return NextResponse.json({
      billing_interval: billingInterval,
      price_amount: priceAmount,
      currency: currency,
      status: subscription.status,
      current_period_start: currentPeriodStart,
      current_period_end: currentPeriodEnd,
      cancel_at_period_end: (subscription as any).cancel_at_period_end,
    });
  } catch (error) {
    console.error('Error retrieving subscription details:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve subscription details' },
      { status: 500 }
    );
  }
}
