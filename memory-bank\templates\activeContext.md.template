# activeContext.md - Current Task Focus & Volatile State (Multi-Project Workspace)

**Purpose:** Tracks the IMMEDIATE focus of work, including target subproject(s), recent actions, short-term decisions, and temporary notes.

**Instructions for Dane:**
*   **CRITICAL:** Always specify the `Target Subproject(s)` for the current task.
*   Update frequently during task execution.

---

## 1. Current Task

<!-- #CurrentTask -->
**Task Goal:** [Clearly state the specific, immediate goal.]
*   **Target Subproject(s):** `[List relevant subproject identifiers, e.g., Frontend, Backend]`
*   **Source/Ticket (if applicable):** [Link or ID]
*   **Related Plan Task/Subtask ID (from `plan.md`):** [e.g., Task 1.3.1]
*   **Related Issue(s) in `issues.md` (if applicable):** [e.g., `Issue-002`]

---

## 2. Files Currently In Focus

<!-- #ActiveFiles -->
**Files being actively viewed or modified NOW:**
*   `[subproject-name]/path/to/file1.ext]` - [Brief reason]
*   `[subproject-name]/path/to/file2.ext]` - [Brief reason]
*   [Include subproject path prefix.]

---

## 3. Recent Changes (This Session ONLY)

<!-- #RecentChanges -->
**Key actions taken very recently:**
*   [Timestamp/Action] Modified `[subproject-name]/[file]` to `[brief change description]`. Verified with `get_errors`.
*   [Timestamp/Action] Ran command `[command]` in context of `[Subproject Name]`. Output: `[brief summary]`.
*   [Timestamp/Action] Decided to use `[pattern/library]` for `[purpose]` within `[Subproject Name]`.

---

## 4. Active Decisions & Considerations (This Session ONLY)
<!-- #ActiveDecisions -->
**Decisions/Questions relevant ONLY to the immediate next steps (Prune aggressively):**
*   Decision: [e.g., Using approach X for current step]
*   Open Question: [e.g., How to handle edge case Y *before* next action?]
*   Consideration: [e.g., Check dependency Z *before* refactoring function C]
*   [Remove items once acted upon, answered, or when moving to a new primary task]

---

## 5. Immediate Next Steps (Micro-steps)

<!-- #NextSteps -->
**Very next actions planned:**
1.  [e.g., Call `insert_edit_into_file` on `frontend/path/to/file1.ext`...]
2.  [e.g., Run `get_errors` on `frontend/path/to/file1.ext`.]
3.  [e.g., Run command `(cd backend && npm test)`...]

---

## 6. Temporary Notes / Scratchpad

<!-- #Scratchpad -->
**Volatile notes:**
*   [Include subproject context if needed, e.g., `[Frontend]` variable name ideas...]

---