{"name": "quantboost-testing", "version": "0.1.0", "private": true, "description": "End-to-end Playwright testing suite for QuantBoost checkout and billing flows.", "scripts": {"preinstall": "node -e \"if(process.version.split('.')[0].replace('v','')<='18'){console.error('Node 20+ required');process.exit(1)}\"", "test": "playwright test -c playwright.config.ts", "test:headed": "playwright test -c playwright.config.ts --headed", "test:debug": "playwright test -c playwright.config.ts --debug", "test:checkout": "playwright test -c playwright.config.ts tests/checkout", "test:webhooks": "playwright test -c playwright.config.ts tests/webhooks", "test:performance": "playwright test -c playwright.config.ts tests/performance", "test:security": "playwright test -c playwright.config.ts tests/security", "report": "node playwright/utils/report-generator.js", "ci": "playwright test -c playwright.config.ts --reporter=line,html && node playwright/utils/report-generator.js"}, "devDependencies": {"@playwright/test": "^1.45.0", "typescript": "^5.4.0", "ts-node": "^10.9.2", "dotenv": "^16.4.5"}, "dependencies": {"@supabase/supabase-js": "^2.45.0", "stripe": "^16.0.0", "axios": "^1.7.2", "winston": "^3.13.0"}}