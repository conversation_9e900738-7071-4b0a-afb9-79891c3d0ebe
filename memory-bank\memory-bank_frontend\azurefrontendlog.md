2025-08-11T23:57:27  Welcome, you are now connected to log-streaming service.Starting Log Tail -n 10 of existing logs ----/appsvctmp/volatile/logs/runtime/container.log
2025-08-11T23:57:10.4748557Z ???? ULTRATHINK: Testing Supabase auth service health...
2025-08-11T23:57:10.6679371Z ??? Supabase database connection test: {
2025-08-11T23:57:10.6679843Z   success: false,
2025-08-11T23:57:10.6679870Z   error: '"failed to parse select parameter (count(*))" (line 1, column 6)'
2025-08-11T23:57:10.6679884Z }
2025-08-11T23:57:10.8579312Z ??? Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-11T23:57:10.8674766Z ??? Stripe event verified: charge.updated ID: evt_3Rv5a3E6FvhUKV1b1wuOS2sA
2025-08-11T23:57:11.2319079Z ???? Handling charge.updated
2025-08-11T23:57:11.2400412Z ???? Charge updated: py_3Rv5a3E6FvhUKV1b1WQ40Bgi, Status: succeeded
2025-08-11T23:57:11.6226755Z ??? Updated receipt for charge: py_3Rv5a3E6FvhUKV1b1WQ40BgiEnding Log Tail of existing logs ---Starting Live Log Stream ---
2025-08-11T23:57:46.3994450Z {"msg":"subscription.reuse","subId":"sub_1RunyYE6FvhUKV1bkZb0KzNJ","status":"incomplete","customerId":"cus_SqUywqOJhoA4pb"}
2025-08-11T23:57:46.7096242Z {"msg":"payment_intent.create.manual","id":"pi_3Rv5b4E6FvhUKV1b1Psh62El"}
2025-08-11T23:58:25.3369806Z {"msg":"subscription.create","subId":"sub_1Rv5bgE6FvhUKV1bz8l8LQDX","customerId":"cus_SqnCg0Te3dkwAz","idemKey":"sub:cus_SqnCg0Te3dkwAz:price_1RC3HTE6FvhUKV1bE9D6zf6e:1"}
2025-08-11T23:58:25.5933434Z {"msg":"payment_intent.create.manual","id":"pi_3Rv5bhE6FvhUKV1b0apGT7ef"}
2025-08-11T23:58:25.8301625Z 🚀 Webhook received at: 2025-08-11T23:58:25.813Z
2025-08-11T23:58:25.8318030Z 🔧 Environment check: {
2025-08-11T23:58:25.8323815Z   hasStripeSecret: true,
2025-08-11T23:58:25.8328328Z   hasWebhookSecret: true,
2025-08-11T23:58:25.8332777Z   hasSupabaseUrl: true,
2025-08-11T23:58:25.8414667Z   hasServiceKey: true,
2025-08-11T23:58:25.8414992Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-11T23:58:25.8415068Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-11T23:58:25.8415083Z }
2025-08-11T23:58:25.8415103Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-11T23:58:25.8828404Z 🚀 Webhook received at: 2025-08-11T23:58:25.873Z
2025-08-11T23:58:25.8912385Z 🔧 Environment check: {
2025-08-11T23:58:25.8912541Z   hasStripeSecret: true,
2025-08-11T23:58:25.8912560Z   hasWebhookSecret: true,
2025-08-11T23:58:25.8912576Z   hasSupabaseUrl: true,
2025-08-11T23:58:25.8912592Z   hasServiceKey: true,
2025-08-11T23:58:25.8912614Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-11T23:58:25.8912745Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-11T23:58:25.8912760Z }
2025-08-11T23:58:25.8912778Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-11T23:58:26.0781243Z ✅ Supabase database connection test: {
2025-08-11T23:58:26.0853611Z   success: false,
2025-08-11T23:58:26.0859384Z   error: '"failed to parse select parameter (count(*))" (line 1, column 6)'
2025-08-11T23:58:26.0863820Z }
2025-08-11T23:58:26.1461824Z ✅ Supabase database connection test: {
2025-08-11T23:58:26.1462291Z   success: false,
2025-08-11T23:58:26.1462323Z   error: '"failed to parse select parameter (count(*))" (line 1, column 6)'
2025-08-11T23:58:26.1462343Z }
2025-08-11T23:58:26.2289183Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-11T23:58:26.2661832Z ✅ Stripe event verified: customer.updated ID: evt_1Rv5bhE6FvhUKV1b94FNUezR
2025-08-11T23:58:26.2967507Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-11T23:58:26.2968166Z ✅ Stripe event verified: customer.subscription.created ID: evt_1Rv5bhE6FvhUKV1bof0ZhDmy
2025-08-11T23:58:26.6465467Z 👉 Handling customer.updated (invoice_settings) { id: 'cus_SqnCg0Te3dkwAz', default_payment_method: null }
2025-08-11T23:58:26.7378713Z 👉 Handling customer.subscription.created
2025-08-11T23:58:26.7388532Z 🔗 Subscription: sub_1Rv5bgE6FvhUKV1bz8l8LQDX, Customer: cus_SqnCg0Te3dkwAz, Status: incomplete
2025-08-11T23:58:26.7397267Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 1
2025-08-11T23:58:26.7496373Z 📅 Billing Period Debug: {
2025-08-11T23:58:26.7496630Z   current_period_start: undefined,
2025-08-11T23:58:26.7496650Z   current_period_end: undefined,
2025-08-11T23:58:26.7496667Z   current_period_start_iso: null,
2025-08-11T23:58:26.7496682Z   current_period_end_iso: null,
2025-08-11T23:58:26.7496695Z   created: 1754956704,
2025-08-11T23:58:26.7496708Z   start_date: 1754956704,
2025-08-11T23:58:26.7496723Z   billing_cycle_anchor: 1754956704,
2025-08-11T23:58:26.7496737Z   status: 'incomplete'
2025-08-11T23:58:26.7496751Z }
2025-08-11T23:58:26.9000590Z 📅 Price interval: year, interval_count: 1
2025-08-11T23:58:26.9001080Z 👤 Fetching customer details for: cus_SqnCg0Te3dkwAz
2025-08-11T23:58:27.1138889Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_SqnCg0Te3dkwAz {
2025-08-11T23:58:27.1139290Z   customerName: 'NO_NAME',
2025-08-11T23:58:27.1139317Z   fullName: 'NO_FULL_NAME',
2025-08-11T23:58:27.1139336Z   customerEmail: '<EMAIL>',
2025-08-11T23:58:27.1139352Z   customerKeys: [
2025-08-11T23:58:27.1139369Z     'id',                    'object',
2025-08-11T23:58:27.1139389Z     'address',               'balance',
2025-08-11T23:58:27.1139405Z     'created',               'currency',
2025-08-11T23:58:27.1139500Z     'default_source',        'delinquent',
2025-08-11T23:58:27.1139518Z     'description',           'discount',
2025-08-11T23:58:27.1139535Z     'email',                 'invoice_prefix',
2025-08-11T23:58:27.1139551Z     'invoice_settings',      'livemode',
2025-08-11T23:58:27.1139567Z     'metadata',              'name',
2025-08-11T23:58:27.1139583Z     'next_invoice_sequence', 'phone',
2025-08-11T23:58:27.1139599Z     'preferred_locales',     'shipping',
2025-08-11T23:58:27.1139615Z     'tax_exempt',            'test_clock'
2025-08-11T23:58:27.1139631Z   ],
2025-08-11T23:58:27.1139646Z   hasName: true,
2025-08-11T23:58:27.1139660Z   nameValue: null
2025-08-11T23:58:27.1139698Z }
2025-08-11T23:58:27.1139719Z 🔍 PARSED NAME DEBUG - Customer: cus_SqnCg0Te3dkwAz {
2025-08-11T23:58:27.1139735Z   originalFullName: null,
2025-08-11T23:58:27.1139751Z   parsedFirstName: 'EMPTY_FIRST',
2025-08-11T23:58:27.1139766Z   parsedLastName: 'EMPTY_LAST',
2025-08-11T23:58:27.1139785Z   parseResult: { firstName: null, lastName: null }
2025-08-11T23:58:27.1139798Z }
2025-08-11T23:58:27.1139820Z 📧 Customer details - email: <EMAIL>, firstName: null, lastName: null
2025-08-11T23:58:27.1139839Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1Rv5bgE6FvhUKV1bz8l8LQDX {
2025-08-11T23:58:27.1139854Z   stripeCustomerId: 'cus_SqnCg0Te3dkwAz',
2025-08-11T23:58:27.1139892Z   extractedEmail: '<EMAIL>',
2025-08-11T23:58:27.1139909Z   extractedFirstName: 'NO_FIRST_NAME',
2025-08-11T23:58:27.1139925Z   extractedLastName: 'NO_LAST_NAME',
2025-08-11T23:58:27.1139942Z   subscriptionStatus: 'incomplete',
2025-08-11T23:58:27.1153006Z   hasCustomerName: false
2025-08-11T23:58:27.1153072Z }
2025-08-11T23:58:27.1153102Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-11T23:58:27.1153128Z 🔍 ensureUserProfile called with: customer=cus_SqnCg0Te3dkwAz, email=<EMAIL>, firstName=null, lastName=null
2025-08-11T23:58:27.1153149Z 🔍 Looking for existing profile by stripe_customer_id: cus_SqnCg0Te3dkwAz
2025-08-11T23:58:27.2386384Z 🔍 Looking for existing profile by email: <EMAIL>
2025-08-11T23:58:27.3496209Z 👤 Creating new auth user and profile for: <EMAIL>
2025-08-11T23:58:27.3505393Z 🔄 Attempt 1 to create auth user for: <EMAIL>
2025-08-11T23:58:27.3513972Z 🔍 ULTRATHINK: Creating user with payload: {
2025-08-11T23:58:27.3514126Z   email: '<EMAIL>',
2025-08-11T23:58:27.3514154Z   email_confirm: true,
2025-08-11T23:58:27.3514170Z   metadata_keys: [
2025-08-11T23:58:27.3514186Z     'first_name',
2025-08-11T23:58:27.3514201Z     'last_name',
2025-08-11T23:58:27.3514255Z     'full_name',
2025-08-11T23:58:27.3514272Z     'source',
2025-08-11T23:58:27.3514288Z     'stripe_customer_id'
2025-08-11T23:58:27.3514304Z   ],
2025-08-11T23:58:27.3514320Z   email_domain: 'gmail.co',
2025-08-11T23:58:27.3514336Z   email_length: 25
2025-08-11T23:58:27.3514352Z }
2025-08-11T23:58:27.6727596Z ✅ Created auth user: 4bd207ab-ca61-4770-83db-a508f37c0cfc
2025-08-11T23:58:27.8979938Z 🔄 Duplicate email detected, looking for existing profile after race condition...
2025-08-11T23:58:27.9013824Z ❌ Error creating profile: {
2025-08-11T23:58:27.9014198Z   code: '23505',
2025-08-11T23:58:27.9014271Z   details: 'Key (email)=(<EMAIL>) already exists.',
2025-08-11T23:58:27.9014315Z   hint: null,
2025-08-11T23:58:27.9014370Z   message: 'duplicate key value violates unique constraint "profiles_email_key"'
2025-08-11T23:58:27.9014409Z }
2025-08-11T23:58:28.0202601Z ✅ Found existing profile after race condition: 4bd207ab-ca61-4770-83db-a508f37c0cfc
2025-08-11T23:58:28.1375595Z ✅ Successfully updated profile after race condition
2025-08-11T23:58:28.1381792Z ✅ User profile resolved: 4bd207ab-ca61-4770-83db-a508f37c0cfc
2025-08-11T23:58:28.1385219Z 👤 Updating profile team admin status: false
2025-08-11T23:58:28.2753228Z ✅ Profile updated - team admin: false
2025-08-11T23:58:28.2759327Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-11T23:58:28.4693664Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-11T23:58:28.4694115Z 💾 Creating/updating subscription record in database
2025-08-11T23:58:28.6224371Z ✅ Subscription created/updated in DB: 22cfc8e8-2cbf-422c-96c4-3ee7763e4a39
2025-08-11T23:58:28.7589648Z 🎫 License sync - target: 1, current: 0
2025-08-11T23:58:28.7595547Z 🎫 Creating 1 Basic-Individual licenses with status: pending
2025-08-11T23:58:28.8802370Z ✅ Successfully created 1 licenses
2025-08-11T23:58:28.8808192Z 🔄 Updating individual license statuses to: inactive
2025-08-11T23:58:29.0663419Z ✅ Individual license statuses updated
2025-08-11T23:58:29.0669231Z ✅ Subscription processing completed successfully
2025-08-11T23:59:45.2604322Z 🚀 Webhook received at: 2025-08-11T23:59:45.260Z
2025-08-11T23:59:45.2610190Z 🔧 Environment check: {
2025-08-11T23:59:45.2610381Z   hasStripeSecret: true,
2025-08-11T23:59:45.2610406Z   hasWebhookSecret: true,
2025-08-11T23:59:45.2610422Z   hasSupabaseUrl: true,
2025-08-11T23:59:45.2610437Z   hasServiceKey: true,
2025-08-11T23:59:45.2610460Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-11T23:59:45.2610476Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-11T23:59:45.2610490Z }
2025-08-11T23:59:45.2673165Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-11T23:59:45.4302318Z 🚀 Webhook received at: 2025-08-11T23:59:45.430Z
2025-08-11T23:59:45.4315732Z 🔧 Environment check: {
2025-08-11T23:59:45.4315974Z   hasStripeSecret: true,
2025-08-11T23:59:45.4315995Z   hasWebhookSecret: true,
2025-08-11T23:59:45.4316009Z   hasSupabaseUrl: true,
2025-08-11T23:59:45.4316024Z   hasServiceKey: true,
2025-08-11T23:59:45.4316047Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-11T23:59:45.4316066Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-11T23:59:45.4316087Z }
2025-08-11T23:59:45.4332696Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-11T23:59:45.5398897Z ✅ Supabase database connection test: {
2025-08-11T23:59:45.5399423Z   success: false,
2025-08-11T23:59:45.5399451Z   error: '"failed to parse select parameter (count(*))" (line 1, column 6)'
2025-08-11T23:59:45.5399466Z }
2025-08-11T23:59:45.5917044Z ✅ Supabase database connection test: {
2025-08-11T23:59:45.5917551Z   success: false,
2025-08-11T23:59:45.5917582Z   error: '"failed to parse select parameter (count(*))" (line 1, column 6)'
2025-08-11T23:59:45.5917598Z }
2025-08-11T23:59:45.6640102Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-11T23:59:45.6655886Z ✅ Stripe event verified: payment_intent.succeeded ID: evt_3Rv5bhE6FvhUKV1b00iqNlos
2025-08-11T23:59:45.7078114Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-11T23:59:45.7163769Z ✅ Stripe event verified: charge.succeeded ID: evt_3Rv5bhE6FvhUKV1b0iHv1wTq
2025-08-11T23:59:45.9243607Z 👉 Handling payment_intent.succeeded
2025-08-11T23:59:45.9286686Z 💳 Payment succeeded: pi_3Rv5bhE6FvhUKV1b0apGT7ef, Amount: 12000, Customer: cus_SqnCg0Te3dkwAz
2025-08-11T23:59:46.0381209Z 👉 Handling charge.succeeded
2025-08-11T23:59:46.0381612Z 💳 Charge succeeded: py_3Rv5bhE6FvhUKV1b0mSeSnSZ, Amount: 12000, Customer: cus_SqnCg0Te3dkwAz
2025-08-11T23:59:46.2814556Z ✅ Stored receipt for charge: py_3Rv5bhE6FvhUKV1b0mSeSnSZ
2025-08-11T23:59:46.4852592Z 🔄 Fetching complete subscription details from Stripe: sub_1Rv5bgE6FvhUKV1bz8l8LQDX
2025-08-11T23:59:46.6554314Z 📅 Payment Success Billing Period Debug: {
2025-08-11T23:59:46.6554672Z   current_period_start: undefined,
2025-08-11T23:59:46.6554701Z   current_period_end: undefined,
2025-08-11T23:59:46.6554723Z   current_period_start_iso: null,
2025-08-11T23:59:46.6554739Z   current_period_end_iso: null,
2025-08-11T23:59:46.6554754Z   created: 1754956704,
2025-08-11T23:59:46.6554767Z   start_date: 1754956704,
2025-08-11T23:59:46.6554818Z   billing_cycle_anchor: 1754956704,
2025-08-11T23:59:46.6554833Z   status: 'incomplete'
2025-08-11T23:59:46.6554846Z }
2025-08-11T23:59:46.6554867Z ⚠️ Missing billing period data after payment success, using fallback calculation...
2025-08-11T23:59:46.8257446Z ✅ Calculated payment success billing periods: {
2025-08-11T23:59:46.8257799Z   current_period_start: '2025-08-11T23:58:24.000Z',
2025-08-11T23:59:46.8257827Z   current_period_end: '2026-08-11T23:58:24.000Z'
2025-08-11T23:59:46.8257849Z }
2025-08-11T23:59:47.0580637Z ✅ Updated subscription with complete billing period data: 22cfc8e8-2cbf-422c-96c4-3ee7763e4a39
2025-08-11T23:59:47.0581010Z 🔄 Updating licenses to active for subscription: 22cfc8e8-2cbf-422c-96c4-3ee7763e4a39
2025-08-11T23:59:47.1778366Z ✅ Updated individual subscription and licenses to active status with expiry: 2026-08-11T23:58:24.000Z
2025-08-11T23:59:49.8837255Z 🚀 Webhook received at: 2025-08-11T23:59:49.883Z
2025-08-11T23:59:49.8875021Z 🔧 Environment check: {
2025-08-11T23:59:49.8875197Z   hasStripeSecret: true,
2025-08-11T23:59:49.8875214Z   hasWebhookSecret: true,
2025-08-11T23:59:49.8875231Z   hasSupabaseUrl: true,
2025-08-11T23:59:49.8875246Z   hasServiceKey: true,
2025-08-11T23:59:49.8875332Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-11T23:59:49.8875351Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-11T23:59:49.8875365Z }
2025-08-11T23:59:49.8875383Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-11T23:59:50.0096630Z ✅ Supabase database connection test: {
2025-08-11T23:59:50.0097109Z   success: false,
2025-08-11T23:59:50.0097140Z   error: '"failed to parse select parameter (count(*))" (line 1, column 6)'
2025-08-11T23:59:50.0097154Z }
2025-08-11T23:59:50.1225914Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-11T23:59:50.1226525Z ✅ Stripe event verified: charge.updated ID: evt_3Rv5bhE6FvhUKV1b006sd464
2025-08-11T23:59:50.3547561Z 👉 Handling charge.updated
2025-08-11T23:59:50.3565156Z 🔄 Charge updated: py_3Rv5bhE6FvhUKV1b0mSeSnSZ, Status: succeeded
2025-08-11T23:59:50.5967402Z ✅ Updated receipt for charge: py_3Rv5bhE6FvhUKV1b0mSeSnSZ