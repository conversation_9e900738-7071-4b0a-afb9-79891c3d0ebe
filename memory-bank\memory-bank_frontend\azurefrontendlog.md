2025-08-13T05:20:11  Welcome, you are now connected to log-streaming service.Starting Log Tail -n 10 of existing logs ----/appsvctmp/volatile/logs/runtime/container.log
2025-08-13T05:20:03.2115297Z ?????? Missing billing period data, attempting to retrieve from Stripe...
2025-08-13T05:20:03.3924094Z ??? Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-13T05:20:03.3930472Z ???? Creating/updating subscription record in database
2025-08-13T05:20:03.5310861Z ??? Subscription created/updated in DB: 4442c6e4-0829-4975-8fab-dd6e7466c4db
2025-08-13T05:20:03.7130837Z ???? License sync - target: 1, current: 0
2025-08-13T05:20:03.7134593Z ???? Creating 1 Basic-Individual licenses with status: pending
2025-08-13T05:20:04.0034763Z ??? Successfully created 1 licenses
2025-08-13T05:20:04.0040330Z ???? Updating individual license statuses to: inactive
2025-08-13T05:20:04.3062134Z ??? Individual license statuses updated
2025-08-13T05:20:04.3066300Z ??? Subscription processing completed successfullyEnding Log Tail of existing logs ---Starting Live Log Stream ---
2025-08-13T05:20:48.0947193Z {"msg":"subscription.create","subId":"sub_1RvX7CE6FvhUKV1bwzwxLs1q","customerId":"cus_SrFcHe8MevpwiA","idemKey":"sub:cus_SrFcHe8MevpwiA:price_1RC3HTE6FvhUKV1bE9D6zf6e:1"}
2025-08-13T05:20:48.3883872Z {"msg":"payment_intent.create.manual","id":"pi_3RvX7EE6FvhUKV1b1ItHtW99"}
2025-08-13T05:20:48.4532806Z 🚀 Webhook received at: 2025-08-13T05:20:48.453Z
2025-08-13T05:20:48.4535925Z 🔧 Environment check: {
2025-08-13T05:20:48.4536006Z   hasStripeSecret: true,
2025-08-13T05:20:48.4536022Z   hasWebhookSecret: true,
2025-08-13T05:20:48.4536035Z   hasSupabaseUrl: true,
2025-08-13T05:20:48.4536051Z   hasServiceKey: true,
2025-08-13T05:20:48.4536072Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-13T05:20:48.4536145Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-13T05:20:48.4536158Z }
2025-08-13T05:20:48.4621441Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-13T05:20:48.6653063Z 🚀 Webhook received at: 2025-08-13T05:20:48.665Z
2025-08-13T05:20:48.6670324Z 🔧 Environment check: {
2025-08-13T05:20:48.6670472Z   hasStripeSecret: true,
2025-08-13T05:20:48.6670493Z   hasWebhookSecret: true,
2025-08-13T05:20:48.6670510Z   hasSupabaseUrl: true,
2025-08-13T05:20:48.6670529Z   hasServiceKey: true,
2025-08-13T05:20:48.6670552Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-13T05:20:48.6670642Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-13T05:20:48.6670661Z }
2025-08-13T05:20:48.6749642Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-13T05:20:48.7212019Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-13T05:20:48.8322820Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-13T05:20:49.0080262Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-13T05:20:49.0257641Z ✅ Stripe event verified: customer.updated ID: evt_1RvX7EE6FvhUKV1bmWz9Ui68
2025-08-13T05:20:49.0990600Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-13T05:20:49.1158516Z ✅ Stripe event verified: customer.subscription.created ID: evt_1RvX7EE6FvhUKV1bKL5BaHgh
2025-08-13T05:20:49.5153633Z 👉 Handling customer.subscription.created
2025-08-13T05:20:49.5155171Z 🔗 Subscription: sub_1RvX7CE6FvhUKV1bwzwxLs1q, Customer: cus_SrFcHe8MevpwiA, Status: incomplete
2025-08-13T05:20:49.5155674Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 1
2025-08-13T05:20:49.5157136Z 📅 Billing Period Debug: {
2025-08-13T05:20:49.5157190Z   current_period_start: undefined,
2025-08-13T05:20:49.5157304Z   current_period_end: undefined,
2025-08-13T05:20:49.5157357Z   current_period_start_iso: null,
2025-08-13T05:20:49.5157372Z   current_period_end_iso: null,
2025-08-13T05:20:49.5157387Z   created: 1755062446,
2025-08-13T05:20:49.5157401Z   start_date: 1755062446,
2025-08-13T05:20:49.5157416Z   billing_cycle_anchor: 1755062446,
2025-08-13T05:20:49.5157429Z   status: 'incomplete'
2025-08-13T05:20:49.5157443Z }
2025-08-13T05:20:49.5295457Z 👉 Handling customer.updated (invoice_settings) { id: 'cus_SrFcHe8MevpwiA', default_payment_method: null }
2025-08-13T05:20:49.6802384Z 📅 Price interval: year, interval_count: 1
2025-08-13T05:20:49.6804078Z 👤 Fetching customer details for: cus_SrFcHe8MevpwiA
2025-08-13T05:20:49.8483434Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_SrFcHe8MevpwiA {
2025-08-13T05:20:49.8483752Z   customerName: 'NO_NAME',
2025-08-13T05:20:49.8483776Z   fullName: 'NO_FULL_NAME',
2025-08-13T05:20:49.8483795Z   customerEmail: '<EMAIL>',
2025-08-13T05:20:49.8483810Z   customerKeys: [
2025-08-13T05:20:49.8483828Z     'id',                    'object',
2025-08-13T05:20:49.8483849Z     'address',               'balance',
2025-08-13T05:20:49.8483866Z     'created',               'currency',
2025-08-13T05:20:49.8483966Z     'default_source',        'delinquent',
2025-08-13T05:20:49.8483984Z     'description',           'discount',
2025-08-13T05:20:49.8484001Z     'email',                 'invoice_prefix',
2025-08-13T05:20:49.8484018Z     'invoice_settings',      'livemode',
2025-08-13T05:20:49.8484034Z     'metadata',              'name',
2025-08-13T05:20:49.8484050Z     'next_invoice_sequence', 'phone',
2025-08-13T05:20:49.8484066Z     'preferred_locales',     'shipping',
2025-08-13T05:20:49.8484083Z     'tax_exempt',            'test_clock'
2025-08-13T05:20:49.8484098Z   ],
2025-08-13T05:20:49.8484113Z   hasName: true,
2025-08-13T05:20:49.8484128Z   nameValue: null
2025-08-13T05:20:49.8484166Z }
2025-08-13T05:20:49.8486516Z 🔍 PARSED NAME DEBUG - Customer: cus_SrFcHe8MevpwiA {
2025-08-13T05:20:49.8486588Z   originalFullName: null,
2025-08-13T05:20:49.8486606Z   parsedFirstName: 'EMPTY_FIRST',
2025-08-13T05:20:49.8486622Z   parsedLastName: 'EMPTY_LAST',
2025-08-13T05:20:49.8486640Z   parseResult: { firstName: null, lastName: null }
2025-08-13T05:20:49.8486655Z }
2025-08-13T05:20:49.8487712Z 📧 Customer details - email: <EMAIL>, firstName: null, lastName: null
2025-08-13T05:20:49.8488879Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1RvX7CE6FvhUKV1bwzwxLs1q {
2025-08-13T05:20:49.8488923Z   stripeCustomerId: 'cus_SrFcHe8MevpwiA',
2025-08-13T05:20:49.8488943Z   extractedEmail: '<EMAIL>',
2025-08-13T05:20:49.8488958Z   extractedFirstName: 'NO_FIRST_NAME',
2025-08-13T05:20:49.8488974Z   extractedLastName: 'NO_LAST_NAME',
2025-08-13T05:20:49.8488990Z   subscriptionStatus: 'incomplete',
2025-08-13T05:20:49.8489007Z   hasCustomerName: false
2025-08-13T05:20:49.8489022Z }
2025-08-13T05:20:49.8489592Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-13T05:20:49.8490229Z 🔍 ensureUserProfile called with: customer=cus_SrFcHe8MevpwiA, email=<EMAIL>, firstName=null, lastName=null
2025-08-13T05:20:49.8490642Z 🔍 Looking for existing profile by stripe_customer_id: cus_SrFcHe8MevpwiA
2025-08-13T05:20:50.0494492Z 🔍 Looking for existing profile by email: <EMAIL>
2025-08-13T05:20:50.2563113Z 👤 Creating new auth user and profile for: <EMAIL>
2025-08-13T05:20:50.2564819Z 🔄 Attempt 1 to create auth user for: <EMAIL>
2025-08-13T05:20:50.2566684Z 🔍 ULTRATHINK: Creating user with payload: {
2025-08-13T05:20:50.2566838Z   email: '<EMAIL>',
2025-08-13T05:20:50.2566866Z   email_confirm: true,
2025-08-13T05:20:50.2566881Z   metadata_keys: [
2025-08-13T05:20:50.2566896Z     'first_name',
2025-08-13T05:20:50.2566911Z     'last_name',
2025-08-13T05:20:50.2566925Z     'full_name',
2025-08-13T05:20:50.2566940Z     'source',
2025-08-13T05:20:50.2566956Z     'stripe_customer_id'
2025-08-13T05:20:50.2566971Z   ],
2025-08-13T05:20:50.2566987Z   email_domain: 'gmail.com',
2025-08-13T05:20:50.2567001Z   email_length: 27
2025-08-13T05:20:50.2567015Z }
2025-08-13T05:20:50.5862474Z ✅ Created auth user: b17666e3-aaac-42b7-ab30-5600d00d7526
2025-08-13T05:20:50.8264557Z ❌ Error creating profile: {
2025-08-13T05:20:50.8264879Z   code: '23505',
2025-08-13T05:20:50.8264918Z   details: 'Key (email)=(<EMAIL>) already exists.',
2025-08-13T05:20:50.8264935Z   hint: null,
2025-08-13T05:20:50.8264955Z   message: 'duplicate key value violates unique constraint "profiles_email_key"'
2025-08-13T05:20:50.8264969Z }
2025-08-13T05:20:50.8266799Z 🔄 Duplicate email detected, looking for existing profile after race condition...
2025-08-13T05:20:51.0411035Z ✅ Found existing profile after race condition: b17666e3-aaac-42b7-ab30-5600d00d7526
2025-08-13T05:20:51.2906760Z ✅ Successfully updated profile after race condition
2025-08-13T05:20:51.2914015Z ✅ User profile resolved: b17666e3-aaac-42b7-ab30-5600d00d7526
2025-08-13T05:20:51.2914883Z 👤 Updating profile team admin status: false
2025-08-13T05:20:51.5332831Z ✅ Profile updated - team admin: false
2025-08-13T05:20:51.5333205Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-13T05:20:51.7225361Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-13T05:20:51.7225569Z 💾 Creating/updating subscription record in database
2025-08-13T05:20:51.9238956Z ✅ Subscription created/updated in DB: 3accfc90-082a-40ab-a54f-16e26f6dd14e
2025-08-13T05:20:52.1479333Z 🎫 License sync - target: 1, current: 0
2025-08-13T05:20:52.1481156Z 🎫 Creating 1 Basic-Individual licenses with status: pending
2025-08-13T05:20:52.3782198Z ✅ Successfully created 1 licenses
2025-08-13T05:20:52.3783775Z 🔄 Updating individual license statuses to: inactive
2025-08-13T05:20:52.6696092Z ✅ Individual license statuses updated
2025-08-13T05:20:52.6696302Z ✅ Subscription processing completed successfully
2025-08-13T05:21:17.1550778Z {"msg":"subscription.create","subId":"sub_1RvX7fE6FvhUKV1bQt5a7GMX","customerId":"cus_SrFc3USbTUfAqq","idemKey":"sub:cus_SrFc3USbTUfAqq:price_1RC3HTE6FvhUKV1bE9D6zf6e:1"}
2025-08-13T05:21:17.4293685Z {"msg":"payment_intent.create.manual","id":"pi_3RvX7hE6FvhUKV1b1Hgo0YR8"}
2025-08-13T05:21:17.5778973Z 🚀 Webhook received at: 2025-08-13T05:21:17.577Z
2025-08-13T05:21:17.5788834Z 🔧 Environment check: {
2025-08-13T05:21:17.5788996Z   hasStripeSecret: true,
2025-08-13T05:21:17.5789020Z   hasWebhookSecret: true,
2025-08-13T05:21:17.5789034Z   hasSupabaseUrl: true,
2025-08-13T05:21:17.5789047Z   hasServiceKey: true,
2025-08-13T05:21:17.5789067Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-13T05:21:17.5789081Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-13T05:21:17.5789094Z }
2025-08-13T05:21:17.5892118Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-13T05:21:17.7402537Z 🚀 Webhook received at: 2025-08-13T05:21:17.740Z
2025-08-13T05:21:17.7410815Z 🔧 Environment check: {
2025-08-13T05:21:17.7410967Z   hasStripeSecret: true,
2025-08-13T05:21:17.7410988Z   hasWebhookSecret: true,
2025-08-13T05:21:17.7411003Z   hasSupabaseUrl: true,
2025-08-13T05:21:17.7411016Z   hasServiceKey: true,
2025-08-13T05:21:17.7411035Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-13T05:21:17.7411052Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-13T05:21:17.7411064Z }
2025-08-13T05:21:17.7581265Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-13T05:21:17.8523805Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-13T05:21:17.9963459Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-13T05:21:18.1201667Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-13T05:21:18.1232993Z ✅ Stripe event verified: customer.updated ID: evt_1RvX7hE6FvhUKV1bjVnWMV1z
2025-08-13T05:21:18.2595403Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-13T05:21:18.2674143Z ✅ Stripe event verified: customer.subscription.created ID: evt_1RvX7hE6FvhUKV1bVXvP8ock
2025-08-13T05:21:18.5854493Z 👉 Handling customer.updated (invoice_settings) { id: 'cus_SrFc3USbTUfAqq', default_payment_method: null }
2025-08-13T05:21:18.6914578Z 👉 Handling customer.subscription.created
2025-08-13T05:21:18.6924938Z 🔗 Subscription: sub_1RvX7fE6FvhUKV1bQt5a7GMX, Customer: cus_SrFc3USbTUfAqq, Status: incomplete
2025-08-13T05:21:18.6932499Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 1
2025-08-13T05:21:18.6940654Z 📅 Billing Period Debug: {
2025-08-13T05:21:18.6940780Z   current_period_start: undefined,
2025-08-13T05:21:18.6940800Z   current_period_end: undefined,
2025-08-13T05:21:18.6940816Z   current_period_start_iso: null,
2025-08-13T05:21:18.6940831Z   current_period_end_iso: null,
2025-08-13T05:21:18.6940881Z   created: 1755062475,
2025-08-13T05:21:18.6940896Z   start_date: 1755062475,
2025-08-13T05:21:18.6940910Z   billing_cycle_anchor: 1755062475,
2025-08-13T05:21:18.6940923Z   status: 'incomplete'
2025-08-13T05:21:18.6940937Z }
2025-08-13T05:21:18.8667927Z 📅 Price interval: year, interval_count: 1
2025-08-13T05:21:18.8668255Z 👤 Fetching customer details for: cus_SrFc3USbTUfAqq
2025-08-13T05:21:19.0296638Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_SrFc3USbTUfAqq {
2025-08-13T05:21:19.0297114Z   customerName: 'NO_NAME',
2025-08-13T05:21:19.0297135Z   fullName: 'NO_FULL_NAME',
2025-08-13T05:21:19.0297228Z   customerEmail: '<EMAIL>',
2025-08-13T05:21:19.0297243Z   customerKeys: [
2025-08-13T05:21:19.0297258Z     'id',                    'object',
2025-08-13T05:21:19.0297273Z     'address',               'balance',
2025-08-13T05:21:19.0297288Z     'created',               'currency',
2025-08-13T05:21:19.0297302Z     'default_source',        'delinquent',
2025-08-13T05:21:19.0297318Z     'description',           'discount',
2025-08-13T05:21:19.0297333Z     'email',                 'invoice_prefix',
2025-08-13T05:21:19.0297347Z     'invoice_settings',      'livemode',
2025-08-13T05:21:19.0297361Z     'metadata',              'name',
2025-08-13T05:21:19.0297394Z     'next_invoice_sequence', 'phone',
2025-08-13T05:21:19.0297409Z     'preferred_locales',     'shipping',
2025-08-13T05:21:19.0297424Z     'tax_exempt',            'test_clock'
2025-08-13T05:21:19.0297437Z   ],
2025-08-13T05:21:19.0297451Z   hasName: true,
2025-08-13T05:21:19.0297464Z   nameValue: null
2025-08-13T05:21:19.0297477Z }
2025-08-13T05:21:19.0297495Z 🔍 PARSED NAME DEBUG - Customer: cus_SrFc3USbTUfAqq {
2025-08-13T05:21:19.0297508Z   originalFullName: null,
2025-08-13T05:21:19.0297522Z   parsedFirstName: 'EMPTY_FIRST',
2025-08-13T05:21:19.0297537Z   parsedLastName: 'EMPTY_LAST',
2025-08-13T05:21:19.0297567Z   parseResult: { firstName: null, lastName: null }
2025-08-13T05:21:19.0297581Z }
2025-08-13T05:21:19.0297600Z 📧 Customer details - email: <EMAIL>, firstName: null, lastName: null
2025-08-13T05:21:19.0297616Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1RvX7fE6FvhUKV1bQt5a7GMX {
2025-08-13T05:21:19.0297631Z   stripeCustomerId: 'cus_SrFc3USbTUfAqq',
2025-08-13T05:21:19.0297645Z   extractedEmail: '<EMAIL>',
2025-08-13T05:21:19.0297659Z   extractedFirstName: 'NO_FIRST_NAME',
2025-08-13T05:21:19.0297674Z   extractedLastName: 'NO_LAST_NAME',
2025-08-13T05:21:19.0297688Z   subscriptionStatus: 'incomplete',
2025-08-13T05:21:19.0297702Z   hasCustomerName: false
2025-08-13T05:21:19.0297714Z }
2025-08-13T05:21:19.0297750Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-13T05:21:19.0297772Z 🔍 ensureUserProfile called with: customer=cus_SrFc3USbTUfAqq, email=<EMAIL>, firstName=null, lastName=null
2025-08-13T05:21:19.0297789Z 🔍 Looking for existing profile by stripe_customer_id: cus_SrFc3USbTUfAqq
2025-08-13T05:21:19.1358032Z 🔍 Looking for existing profile by email: <EMAIL>
2025-08-13T05:21:19.3560455Z 👤 Creating new auth user and profile for: <EMAIL>
2025-08-13T05:21:19.3560855Z 🔄 Attempt 1 to create auth user for: <EMAIL>
2025-08-13T05:21:19.3560880Z 🔍 ULTRATHINK: Creating user with payload: {
2025-08-13T05:21:19.3560899Z   email: '<EMAIL>',
2025-08-13T05:21:19.3560996Z   email_confirm: true,
2025-08-13T05:21:19.3561013Z   metadata_keys: [
2025-08-13T05:21:19.3561028Z     'first_name',
2025-08-13T05:21:19.3561043Z     'last_name',
2025-08-13T05:21:19.3561059Z     'full_name',
2025-08-13T05:21:19.3561073Z     'source',
2025-08-13T05:21:19.3561091Z     'stripe_customer_id'
2025-08-13T05:21:19.3561105Z   ],
2025-08-13T05:21:19.3561122Z   email_domain: 'gmail.com',
2025-08-13T05:21:19.3561137Z   email_length: 27
2025-08-13T05:21:19.3561151Z }
2025-08-13T05:21:19.6665769Z ✅ Created auth user: 88f64fa0-dbe8-43ed-975e-7971d9765a91
2025-08-13T05:21:19.9135863Z ❌ Error creating profile: {
2025-08-13T05:21:19.9136463Z   code: '23505',
2025-08-13T05:21:19.9136541Z   details: 'Key (email)=(<EMAIL>) already exists.',
2025-08-13T05:21:19.9136581Z   hint: null,
2025-08-13T05:21:19.9136737Z   message: 'duplicate key value violates unique constraint "profiles_email_key"'
2025-08-13T05:21:19.9136782Z }
2025-08-13T05:21:19.9137445Z 🔄 Duplicate email detected, looking for existing profile after race condition...