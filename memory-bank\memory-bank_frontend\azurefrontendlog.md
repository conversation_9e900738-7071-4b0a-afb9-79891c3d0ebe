2025-08-11T23:20:28.2751763Z  ✓ Ready in 3.8s
2025-08-11T23:20:47.5444977Z {"msg":"subscription.create","subId":"sub_1Rv51GE6FvhUKV1bwzfinJHI","customerId":"cus_SqmatCZEbUzYSi","idemKey":"sub:cus_SqmatCZEbUzYSi:price_1RC3HTE6FvhUKV1bE9D6zf6e:1"}
2025-08-11T23:20:47.8047029Z {"msg":"payment_intent.create.manual","id":"pi_3Rv51HE6FvhUKV1b0XbmsKLY"}
2025-08-11T23:20:48.1233323Z 🚀 Webhook received at: 2025-08-11T23:20:48.123Z
2025-08-11T23:20:48.1402542Z 🔧 Environment check: {
2025-08-11T23:20:48.1402642Z   hasStripeSecret: true,
2025-08-11T23:20:48.1402660Z   hasWebhookSecret: true,
2025-08-11T23:20:48.1402675Z   hasSupabaseUrl: true,
2025-08-11T23:20:48.1402689Z   hasServiceKey: true,
2025-08-11T23:20:48.1402712Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-11T23:20:48.1402728Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-11T23:20:48.1402741Z }
2025-08-11T23:20:48.1403295Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-11T23:20:48.1933459Z 🚀 Webhook received at: 2025-08-11T23:20:48.193Z
2025-08-11T23:20:48.2108352Z 🔧 Environment check: {
2025-08-11T23:20:48.2109620Z   hasStripeSecret: true,
2025-08-11T23:20:48.2109686Z   hasWebhookSecret: true,
2025-08-11T23:20:48.2109730Z   hasSupabaseUrl: true,
2025-08-11T23:20:48.2109771Z   hasServiceKey: true,
2025-08-11T23:20:48.2109830Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-11T23:20:48.2109876Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-11T23:20:48.2109916Z }
2025-08-11T23:20:48.2109973Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-11T23:20:48.6916966Z ✅ Supabase database connection test: {
2025-08-11T23:20:48.6917365Z   success: false,
2025-08-11T23:20:48.6917394Z   error: '"failed to parse select parameter (count(*))" (line 1, column 6)'
2025-08-11T23:20:48.6917410Z }
2025-08-11T23:20:48.7618213Z ✅ Supabase database connection test: {
2025-08-11T23:20:48.7618640Z   success: false,
2025-08-11T23:20:48.7618672Z   error: '"failed to parse select parameter (count(*))" (line 1, column 6)'
2025-08-11T23:20:48.7618690Z }
2025-08-11T23:20:49.0048150Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-11T23:20:49.0237383Z ✅ Stripe event verified: customer.updated ID: evt_1Rv51HE6FvhUKV1bjUeplqZ6
2025-08-11T23:20:49.0261189Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-11T23:20:49.0441774Z ✅ Stripe event verified: customer.subscription.created ID: evt_1Rv51HE6FvhUKV1bfyc53hiJ
2025-08-11T23:20:49.3599784Z 👉 Handling customer.updated (invoice_settings) { id: 'cus_SqmatCZEbUzYSi', default_payment_method: null }
2025-08-11T23:20:49.4427355Z 👉 Handling customer.subscription.created
2025-08-11T23:20:49.4427589Z 🔗 Subscription: sub_1Rv51GE6FvhUKV1bwzfinJHI, Customer: cus_SqmatCZEbUzYSi, Status: incomplete
2025-08-11T23:20:49.4427612Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 1
2025-08-11T23:20:49.4427687Z 📅 Billing Period Debug: {
2025-08-11T23:20:49.4427705Z   current_period_start: undefined,
2025-08-11T23:20:49.4427720Z   current_period_end: undefined,
2025-08-11T23:20:49.4427734Z   current_period_start_iso: null,
2025-08-11T23:20:49.4427747Z   current_period_end_iso: null,
2025-08-11T23:20:49.4427761Z   created: 1754954446,
2025-08-11T23:20:49.4427775Z   start_date: 1754954446,
2025-08-11T23:20:49.4427790Z   billing_cycle_anchor: 1754954446,
2025-08-11T23:20:49.4427803Z   status: 'incomplete'
2025-08-11T23:20:49.4427817Z }
2025-08-11T23:20:49.6040897Z 📅 Price interval: year, interval_count: 1
2025-08-11T23:20:49.6050214Z 👤 Fetching customer details for: cus_SqmatCZEbUzYSi
2025-08-11T23:20:49.7876427Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_SqmatCZEbUzYSi {
2025-08-11T23:20:49.7876962Z   customerName: 'NO_NAME',
2025-08-11T23:20:49.7876987Z   fullName: 'NO_FULL_NAME',
2025-08-11T23:20:49.7877006Z   customerEmail: 'test0811-420pm@gmail.c',
2025-08-11T23:20:49.7877021Z   customerKeys: [
2025-08-11T23:20:49.7877037Z     'id',                    'object',
2025-08-11T23:20:49.7877052Z     'address',               'balance',
2025-08-11T23:20:49.7877073Z     'created',               'currency',
2025-08-11T23:20:49.7877161Z     'default_source',        'delinquent',
2025-08-11T23:20:49.7877178Z     'description',           'discount',
2025-08-11T23:20:49.7877194Z     'email',                 'invoice_prefix',
2025-08-11T23:20:49.7877210Z     'invoice_settings',      'livemode',
2025-08-11T23:20:49.7877225Z     'metadata',              'name',
2025-08-11T23:20:49.7877244Z     'next_invoice_sequence', 'phone',
2025-08-11T23:20:49.7877260Z     'preferred_locales',     'shipping',
2025-08-11T23:20:49.7877275Z     'tax_exempt',            'test_clock'
2025-08-11T23:20:49.7877290Z   ],
2025-08-11T23:20:49.7877304Z   hasName: true,
2025-08-11T23:20:49.7877318Z   nameValue: null
2025-08-11T23:20:49.7877355Z }
2025-08-11T23:20:49.7955620Z 🔍 PARSED NAME DEBUG - Customer: cus_SqmatCZEbUzYSi {
2025-08-11T23:20:49.7955765Z   originalFullName: null,
2025-08-11T23:20:49.7955784Z   parsedFirstName: 'EMPTY_FIRST',
2025-08-11T23:20:49.7955800Z   parsedLastName: 'EMPTY_LAST',
2025-08-11T23:20:49.7955817Z   parseResult: { firstName: null, lastName: null }
2025-08-11T23:20:49.7955832Z }
2025-08-11T23:20:49.7955852Z 📧 Customer details - email: test0811-420pm@gmail.c, firstName: null, lastName: null
2025-08-11T23:20:49.7955870Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1Rv51GE6FvhUKV1bwzfinJHI {
2025-08-11T23:20:49.7955886Z   stripeCustomerId: 'cus_SqmatCZEbUzYSi',
2025-08-11T23:20:49.7955952Z   extractedEmail: 'test0811-420pm@gmail.c',
2025-08-11T23:20:49.7955968Z   extractedFirstName: 'NO_FIRST_NAME',
2025-08-11T23:20:49.7955983Z   extractedLastName: 'NO_LAST_NAME',
2025-08-11T23:20:49.7955998Z   subscriptionStatus: 'incomplete',
2025-08-11T23:20:49.7956013Z   hasCustomerName: false
2025-08-11T23:20:49.7956025Z }
2025-08-11T23:20:49.7956044Z 👤 Creating/finding user profile for test0811-420pm@gmail.c
2025-08-11T23:20:49.7956065Z 🔍 ensureUserProfile called with: customer=cus_SqmatCZEbUzYSi, email=test0811-420pm@gmail.c, firstName=null, lastName=null
2025-08-11T23:20:49.7980963Z 🔍 Looking for existing profile by stripe_customer_id: cus_SqmatCZEbUzYSi
2025-08-11T23:20:49.9076502Z 🔍 Looking for existing profile by email: test0811-420pm@gmail.c
2025-08-11T23:20:50.1220426Z 👤 Creating new auth user and profile for: test0811-420pm@gmail.c
2025-08-11T23:20:50.1224676Z 🔄 Attempt 1 to create auth user for: test0811-420pm@gmail.c
2025-08-11T23:20:50.1228949Z 🔍 ULTRATHINK: Creating user with payload: {
2025-08-11T23:20:50.1229118Z   email: 'test0811-420pm@gmail.c',
2025-08-11T23:20:50.1229148Z   email_confirm: true,2025-08-11T23:20:50.1229163Z   metadata_keys: [
2025-08-11T23:20:50.1229183Z     'first_name',
2025-08-11T23:20:50.1229199Z     'last_name',
2025-08-11T23:20:50.1229213Z     'full_name',
2025-08-11T23:20:50.1229279Z     'source',
2025-08-11T23:20:50.1229296Z     'stripe_customer_id'
2025-08-11T23:20:50.1229311Z   ],
2025-08-11T23:20:50.1229327Z   email_domain: 'gmail.c',
2025-08-11T23:20:50.1229341Z   email_length: 22
2025-08-11T23:20:50.1229357Z }
2025-08-11T23:20:50.3530086Z ✅ Created auth user: e9c614b3-828c-4e39-8088-f12f3475bff5
2025-08-11T23:20:50.5482780Z ❌ Error creating profile: {
2025-08-11T23:20:50.5483174Z   code: '23505',
2025-08-11T23:20:50.5483215Z   details: 'Key (email)=(test0811-420pm@gmail.c) already exists.',
2025-08-11T23:20:50.5483232Z   hint: null,
2025-08-11T23:20:50.5483252Z   message: 'duplicate key value violates unique constraint "profiles_email_key"'
2025-08-11T23:20:50.5483264Z }
2025-08-11T23:20:50.5487806Z 🔄 Duplicate email detected, looking for existing profile after race condition...
2025-08-11T23:20:50.7991535Z ✅ Found existing profile after race condition: e9c614b3-828c-4e39-8088-f12f3475bff5
2025-08-11T23:20:50.9035852Z ✅ Successfully updated profile after race condition
2025-08-11T23:20:50.9040062Z ✅ User profile resolved: e9c614b3-828c-4e39-8088-f12f3475bff5
2025-08-11T23:20:50.9042068Z 👤 Updating profile team admin status: false
2025-08-11T23:20:51.0971710Z ✅ Profile updated - team admin: false
2025-08-11T23:20:51.0971982Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-11T23:20:51.2695354Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-11T23:20:51.2695694Z 💾 Creating/updating subscription record in database
2025-08-11T23:20:51.3691763Z ✅ Subscription created/updated in DB: 77bc1e17-a1fe-4f6d-bbba-3203e97ffda3
2025-08-11T23:20:51.5013094Z 🎫 License sync - target: 1, current: 0
2025-08-11T23:20:51.5184695Z 🎫 Creating 1 Basic-Individual licenses with status: pending
2025-08-11T23:20:51.6217157Z ✅ Successfully created 1 licenses
2025-08-11T23:20:51.6217555Z 🔄 Updating individual license statuses to: inactive
2025-08-11T23:20:52.0063145Z ✅ Individual license statuses updated
2025-08-11T23:20:52.0063445Z ✅ Subscription processing completed successfully
2025-08-11T23:20:52.5212889Z Terminated
2025-08-11T23:21:55  No new trace in the past 1 min(s).
2025-08-11T23:22:55  No new trace in the past 2 min(s).