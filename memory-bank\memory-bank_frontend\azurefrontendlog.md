2025-08-08T01:14:02.0540720Z   nodeEnv: 'production',
2025-08-08T01:14:02.0540764Z   hasStripeSecret: true,
2025-08-08T01:14:02.0540779Z   hasStripePublishable: true,
2025-08-08T01:14:02.0540792Z   hasSupabaseUrl: true,
2025-08-08T01:14:02.0540806Z   hasSupabaseAnon: true,
2025-08-08T01:14:02.0540819Z   hasSupabaseService: true,
2025-08-08T01:14:02.0540839Z   stripeValidation: { isValid: true, missing: [] },
2025-08-08T01:14:02.0540854Z   supabaseValidation: { isValid: true, missing: [] },
2025-08-08T01:14:02.0540867Z   timestamp: '2025-08-08T01:14:02.024Z'
2025-08-08T01:14:02.0540880Z }
2025-08-08T01:14:02.0849962Z ✅ Creating payment intent with email: <EMAIL>
2025-08-08T01:14:02.0857935Z ✅ Creating payment intent for email: <EMAIL>
2025-08-08T01:14:02.1027406Z 🔍 Looking for existing Stripe customer with email: <EMAIL>
2025-08-08T01:14:02.9810543Z 🆕 Creating new Stripe customer for email: <EMAIL>
2025-08-08T01:14:03.2906560Z ✅ Created new Stripe customer: cus_SpJV4HKdl7yyXX
2025-08-08T01:14:03.2925805Z 🔄 Creating subscription for customer cus_SpJV4HKdl7yyXX <NAME_EMAIL>
2025-08-08T01:14:04.5817803Z ✅ Created subscription sub_1RteshE6FvhUKV1b0yWtWFR2 for customer cus_SpJV4HKdl7yyXX
2025-08-08T01:14:04.5818029Z 📧 Subscription metadata email: <EMAIL>
2025-08-08T01:14:04.5818051Z No payment intent found on invoice, creating one manually
2025-08-08T01:14:04.5818069Z Invoice details: {
2025-08-08T01:14:04.5818083Z   id: 'in_1RteshE6FvhUKV1bUobApDyF',
2025-08-08T01:14:04.5818099Z   status: 'open',
2025-08-08T01:14:04.5818111Z   amount_due: 12000,
2025-08-08T01:14:04.5818124Z   attempted: false
2025-08-08T01:14:04.5818136Z }
2025-08-08T01:14:04.8705236Z Created payment intent: pi_3RtesiE6FvhUKV1b0v447hN7
2025-08-08T01:14:05.1598415Z 🚀 Webhook received at: 2025-08-08T01:14:05.159Z
2025-08-08T01:14:05.1609475Z 🔧 Environment check: {
2025-08-08T01:14:05.1609664Z   hasStripeSecret: true,
2025-08-08T01:14:05.1609684Z   hasWebhookSecret: true,
2025-08-08T01:14:05.1609700Z   hasSupabaseUrl: true,
2025-08-08T01:14:05.1609715Z   hasServiceKey: true,
2025-08-08T01:14:05.1609733Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-08T01:14:05.1609748Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-08T01:14:05.1609760Z }
2025-08-08T01:14:05.1791632Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-08T01:14:05.6471439Z ✅ Supabase database connection test: {
2025-08-08T01:14:05.6471742Z   success: false,
2025-08-08T01:14:05.6471843Z   error: '"failed to parse select parameter (count(*))" (line 1, column 6)'
2025-08-08T01:14:05.6471857Z }
2025-08-08T01:14:06.0601622Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-08T01:14:06.1997629Z ✅ Stripe event verified: customer.subscription.created ID: evt_1RtesiE6FvhUKV1bUEKnA9PJ
2025-08-08T01:14:06.7576414Z 👉 Handling customer.subscription.created
2025-08-08T01:14:06.7634363Z 🔗 Subscription: sub_1RteshE6FvhUKV1b0yWtWFR2, Customer: cus_SpJV4HKdl7yyXX, Status: incomplete
2025-08-08T01:14:06.7634449Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 1
2025-08-08T01:14:06.7634473Z 📅 Billing Period Debug: {
2025-08-08T01:14:06.7634488Z   current_period_start: undefined,
2025-08-08T01:14:06.7634502Z   current_period_end: undefined,
2025-08-08T01:14:06.7634516Z   current_period_start_iso: null,
2025-08-08T01:14:06.7634529Z   current_period_end_iso: null,
2025-08-08T01:14:06.7634543Z   created: 1754615643,
2025-08-08T01:14:06.7634556Z   start_date: 1754615643,
2025-08-08T01:14:06.7634571Z   billing_cycle_anchor: 1754615643,
2025-08-08T01:14:06.7634618Z   status: 'incomplete'
2025-08-08T01:14:06.7634631Z }
2025-08-08T01:14:06.9204292Z 📅 Price interval: year, interval_count: 1
2025-08-08T01:14:06.9204802Z 👤 Fetching customer details for: cus_SpJV4HKdl7yyXX
2025-08-08T01:14:07.0977305Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_SpJV4HKdl7yyXX {
2025-08-08T01:14:07.0980382Z   customerName: 'NO_NAME',
2025-08-08T01:14:07.0981908Z   fullName: 'NO_FULL_NAME',
2025-08-08T01:14:07.0983412Z   customerEmail: '<EMAIL>',
2025-08-08T01:14:07.0984883Z   customerKeys: [
2025-08-08T01:14:07.0986381Z     'id',                    'object',
2025-08-08T01:14:07.0987851Z     'address',               'balance',
2025-08-08T01:14:07.0989339Z     'created',               'currency',
2025-08-08T01:14:07.1056231Z     'default_source',        'delinquent',
2025-08-08T01:14:07.1056324Z     'description',           'discount',
2025-08-08T01:14:07.1056342Z     'email',                 'invoice_prefix',
2025-08-08T01:14:07.1056357Z     'invoice_settings',      'livemode',
2025-08-08T01:14:07.1056371Z     'metadata',              'name',
2025-08-08T01:14:07.1056385Z     'next_invoice_sequence', 'phone',
2025-08-08T01:14:07.1056399Z     'preferred_locales',     'shipping',
2025-08-08T01:14:07.1056413Z     'tax_exempt',            'test_clock'
2025-08-08T01:14:07.1056472Z   ],
2025-08-08T01:14:07.1056485Z   hasName: true,
2025-08-08T01:14:07.1056499Z   nameValue: null
2025-08-08T01:14:07.1056512Z }
2025-08-08T01:14:07.1056531Z 🔍 PARSED NAME DEBUG - Customer: cus_SpJV4HKdl7yyXX {
2025-08-08T01:14:07.1056545Z   originalFullName: null,
2025-08-08T01:14:07.1056558Z   parsedFirstName: 'EMPTY_FIRST',
2025-08-08T01:14:07.1056572Z   parsedLastName: 'EMPTY_LAST',
2025-08-08T01:14:07.1056587Z   parseResult: { firstName: null, lastName: null }
2025-08-08T01:14:07.1057051Z }
2025-08-08T01:14:07.1057091Z 📧 Customer details - email: <EMAIL>, firstName: null, lastName: null
2025-08-08T01:14:07.1057108Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1RteshE6FvhUKV1b0yWtWFR2 {
2025-08-08T01:14:07.1057121Z   stripeCustomerId: 'cus_SpJV4HKdl7yyXX',2025-08-08T01:14:07.1057135Z   extractedEmail: '<EMAIL>',
2025-08-08T01:14:07.1057149Z   extractedFirstName: 'NO_FIRST_NAME',
2025-08-08T01:14:07.1057163Z   extractedLastName: 'NO_LAST_NAME',
2025-08-08T01:14:07.1057177Z   subscriptionStatus: 'incomplete',
2025-08-08T01:14:07.1057190Z   hasCustomerName: false
2025-08-08T01:14:07.1057202Z }
2025-08-08T01:14:07.1057218Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-08T01:14:07.1057236Z 🔍 ensureUserProfile called with: customer=cus_SpJV4HKdl7yyXX, email=<EMAIL>, firstName=null, lastName=null
2025-08-08T01:14:07.1057270Z 🔍 Looking for existing profile by stripe_customer_id: cus_SpJV4HKdl7yyXX
2025-08-08T01:14:07.2890032Z 🔍 Looking for existing profile by email: <EMAIL>
2025-08-08T01:14:07.4918426Z 👤 Creating new auth user and profile for: <EMAIL>
2025-08-08T01:14:07.4922333Z 🔄 Attempt 1 to create auth user for: <EMAIL>
2025-08-08T01:14:07.4926225Z 🔍 ULTRATHINK: Creating user with payload: {
2025-08-08T01:14:07.4926385Z   email: '<EMAIL>',
2025-08-08T01:14:07.4926406Z   email_confirm: true,
2025-08-08T01:14:07.4926422Z   metadata_keys: [
2025-08-08T01:14:07.4926471Z     'first_name',
2025-08-08T01:14:07.4926484Z     'last_name',
2025-08-08T01:14:07.4926496Z     'full_name',
2025-08-08T01:14:07.4926508Z     'source',
2025-08-08T01:14:07.4926521Z     'stripe_customer_id'
2025-08-08T01:14:07.4926534Z   ],
2025-08-08T01:14:07.4926549Z   email_domain: 'gmail.com',
2025-08-08T01:14:07.4926561Z   email_length: 24
2025-08-08T01:14:07.4926574Z }
2025-08-08T01:14:07.8084069Z ✅ Created auth user: f6dc974c-757b-44f2-865f-fa910bc90ed5
2025-08-08T01:14:07.9474851Z 🔄 Duplicate email detected, looking for existing profile after race condition...
2025-08-08T01:14:07.9479102Z ❌ Error creating profile: {
2025-08-08T01:14:07.9479170Z   code: '23505',
2025-08-08T01:14:07.9483805Z   details: 'Key (email)=(<EMAIL>) already exists.',
2025-08-08T01:14:07.9483893Z   hint: null,
2025-08-08T01:14:07.9483912Z   message: 'duplicate key value violates unique constraint "profiles_email_key"'
2025-08-08T01:14:07.9483925Z }
2025-08-08T01:14:08.0988317Z ✅ Found existing profile after race condition: f6dc974c-757b-44f2-865f-fa910bc90ed5
2025-08-08T01:14:08.3509623Z ✅ Successfully updated profile after race condition
2025-08-08T01:14:08.3515021Z ✅ User profile resolved: f6dc974c-757b-44f2-865f-fa910bc90ed5
2025-08-08T01:14:08.3518588Z 👤 Updating profile team admin status: false
2025-08-08T01:14:08.6016128Z ✅ Profile updated - team admin: false
2025-08-08T01:14:08.6021755Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-08T01:14:08.8555799Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-08T01:14:08.8556500Z 💾 Creating/updating subscription record in database
2025-08-08T01:14:08.9065299Z ✅ Subscription created/updated in DB: b6a5e111-9a29-49ad-84dc-10083ea1c7e4
2025-08-08T01:14:09.0504124Z 🎫 License sync - target: 1, current: 0
2025-08-08T01:14:09.0512827Z 🎫 Creating 1 Basic-Individual licenses with status: pending
2025-08-08T01:14:09.1808914Z ✅ Successfully created 1 licenses
2025-08-08T01:14:09.1818096Z 🔄 Updating individual license statuses to: inactive
2025-08-08T01:14:09.5221338Z ✅ Individual license statuses updated
2025-08-08T01:14:09.5225026Z ✅ Subscription processing completed successfully
2025-08-08T01:14:14  Stream terminated due to timeout 120 min(s).