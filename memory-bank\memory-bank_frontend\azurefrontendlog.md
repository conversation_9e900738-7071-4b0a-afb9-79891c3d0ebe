2025-08-19T03:45:20  No new trace in the past 6 min(s).
2025-08-19T03:46:10.382Z INFO  - Pulling image: mcr.microsoft.com/appsvc/msitokenservice:stage4
2025-08-19T03:46:10.582Z INFO  - stage4 Pulling from appsvc/msitokenservice
2025-08-19T03:46:10.594Z INFO  -  Digest: sha256:15c4360552ca5d886db18e4eaa6a95993305c67dae381aa8505592cd7c45bd46
2025-08-19T03:46:10.596Z INFO  -  Status: Image is up to date for mcr.microsoft.com/appsvc/msitokenservice:stage4
2025-08-19T03:46:10.633Z INFO  - Pull Image successful, Time taken: 0 Seconds
2025-08-19T03:46:10.927Z INFO  - Starting container for site
2025-08-19T03:46:10.928Z INFO  - docker run -d --expose=8081 --name app-quantboost-frontend-staging_0_ab117711_msiProxy -e WEBSITE_ROLE_INSTANCE_ID=0 -e WEBSITE_HOSTNAME=app-quantboost-frontend-staging.azurewebsites.net -e WEBSITE_INSTANCE_ID=14081d20018b15b1623fc720b48f16fa62465e5170c988d77c1ce70393fc7d40 mcr.microsoft.com/appsvc/msitokenservice:stage4
2025-08-19T03:46:10.929Z INFO  - Logging is not enabled for this container.Please use https://aka.ms/linux-diagnostics to enable logging to see container logs here..600Z INFO  - Initiating warmup request to container app-quantboost-frontend-staging_0_ab117711 for site app-quantboost-frontend-staging
2025-08-19T03:47:10.759Z INFO  - Waiting for response to warmup request for container app-quantboost-frontend-staging_0_ab117711. Elapsed time = 19.6184821 sec
2025-08-19T03:47:34.937Z INFO  - Waiting for response to warmup request for container app-quantboost-frontend-staging_0_ab117711. Elapsed time = 43.7966573 sec
2025-08-19T03:47:58.146Z INFO  - Waiting for response to warmup request for container app-quantboost-frontend-staging_0_ab117711. Elapsed time = 67.0060443 sec
2025-08-19T03:48:23.069Z INFO  - Waiting for response to warmup request for container app-quantboost-frontend-staging_0_ab117711. Elapsed time = 91.9287539 sec
2025-08-19T03:48:47.480Z INFO  - Waiting for response to warmup request for container app-quantboost-frontend-staging_0_ab117711. Elapsed time = 116.3402791 sec
2025-08-19T03:49:11.028Z INFO  - Waiting for response to warmup request for container app-quantboost-frontend-staging_0_ab117711. Elapsed time = 139.887561 sec
2025-08-19T03:46:50.024590754Z    _____
2025-08-19T03:46:50.024737286Z   /  _  \ __________ _________   ____
2025-08-19T03:46:50.024745567Z  /  /_\  \\___   /  |  \_  __ \_/ __ \
2025-08-19T03:46:50.024751432Z /    |    \/    /|  |  /|  | \/\  ___/
2025-08-19T03:46:50.024756891Z \____|__  /_____ \____/ |__|    \___  >
2025-08-19T03:46:50.024783231Z         \/      \/                  \/
2025-08-19T03:46:50.024789185Z A P P   S E R V I C E   O N   L I N U X
2025-08-19T03:46:50.024794351Z
2025-08-19T03:46:50.024799207Z Documentation: http://aka.ms/webapp-linux
2025-08-19T03:46:50.024804205Z NodeJS quickstart: https://aka.ms/node-qs
2025-08-19T03:46:50.024809301Z NodeJS Version : v22.17.0
2025-08-19T03:46:50.024814353Z Note: Any data outside '/home' is not persisted
2025-08-19T03:46:50.024819739Z
2025-08-19T03:47:09.110297911Z Starting OpenBSD Secure Shell server: sshd.
2025-08-19T03:47:09.170973630Z WEBSITES_INCLUDE_CLOUD_CERTS is not set to true.
2025-08-19T03:47:09.340522241Z Updating certificates in /etc/ssl/certs...
2025-08-19T03:47:57.415629261Z rehash: warning: skipping ca-certificates.crt,it does not contain exactly one certificate or CRL
2025-08-19T03:47:57.655891166Z 4 added, 0 removed; done.
2025-08-19T03:47:57.655955759Z Running hooks in /etc/ca-certificates/update.d...
2025-08-19T03:47:57.655965696Z done.
2025-08-19T03:47:57.734310515Z CA certificates copied and updated successfully.
2025-08-19T03:47:58.598758800Z Starting periodic command scheduler: cron.
2025-08-19T03:47:59.756939301Z Could not find build manifest file at '/home/<USER>/wwwroot/oryx-manifest.toml'
2025-08-19T03:47:59.756999552Z Could not find operation ID in manifest. Generating an operation id...
2025-08-19T03:47:59.757011249Z Build Operation ID: 6508b65e-420d-4ae1-bc36-d8e4986e13b4
2025-08-19T03:48:05.325360387Z Environment Variables for Application Insight's IPA Codeless Configuration exists..
2025-08-19T03:48:05.412533550Z Writing output script to '/opt/startup/startup.sh'
2025-08-19T03:48:05.674797867Z Running #!/bin/sh
2025-08-19T03:48:05.674866964Z
2025-08-19T03:48:05.674876610Z # Enter the source directory to make sure the script runs where the user expects
2025-08-19T03:48:05.674885194Z cd "/home/<USER>/wwwroot"
2025-08-19T03:48:05.721750674Z
2025-08-19T03:48:05.721776868Z export NODE_PATH=/usr/local/lib/node_modules:$NODE_PATH
2025-08-19T03:48:05.721786090Z if [ -z "$PORT" ]; then
2025-08-19T03:48:05.721794292Z 		export PORT=8080
2025-08-19T03:48:05.721802503Z fi
2025-08-19T03:48:05.721810140Z
2025-08-19T03:48:05.721817676Z npm start
2025-08-19T03:48:14.569072504Z npm info using npm@10.9.2
2025-08-19T03:48:14.600112378Z npm info using node@v22.17.0
2025-08-19T03:48:54.160458180Z npm http fetch GET 200 https://registry.npmjs.org/npm 11630ms
2025-08-19T03:48:58.924015768Z
2025-08-19T03:48:58.924064308Z > quantboostai@0.1.2 start
2025-08-19T03:48:58.924073724Z > node server.js
2025-08-19T03:48:58.924080802Z
2025-08-19T03:49:19.685188783Z    ▲ Next.js 15.2.4
2025-08-19T03:49:19.692890208Z    - Local:        http://7656cd2338a5:8080
2025-08-19T03:49:19.694276815Z    - Network:      http://7656cd2338a5:8080
2025-08-19T03:49:19.694303756Z
2025-08-19T03:49:19.694312617Z  ✓ Starting...
2025-08-19T03:49:19.865397812Z [Error: Could not find a production build in the './.next' directory. Try building your app with 'next build' before starting the production server. https://nextjs.org/docs/messages/production-start-no-build-id]
2025-08-19T03:49:19.923838419Z npm notice
2025-08-19T03:49:19.923877707Z npm notice New major version of npm available! 10.9.2 -> 11.5.2
2025-08-19T03:49:19.923887868Z npm notice Changelog: https://github.com/npm/cli/releases/tag/v11.5.2
2025-08-19T03:49:19.923896124Z npm notice To update run: npm install -g npm@11.5.2
2025-08-19T03:49:19.923904063Z npm notice
2025-08-19T03:49:21.614Z ERROR - Container app-quantboost-frontend-staging_0_ab117711 for site app-quantboost-frontend-staging has exited, failing site start
2025-08-19T03:49:21.651Z ERROR - Container app-quantboost-frontend-staging_0_ab117711 didn't respond to HTTP pings on port: 8080. Failing site start. See container logs for debugging.
2025-08-19T03:49:21.811Z INFO  - Stopping site app-quantboost-frontend-staging because it failed during startup.