2025-08-13T04:11:58  Welcome, you are now connected to log-streaming service.Starting Log Tail -n 10 of existing logs ----/appsvctmp/volatile/logs/runtime/container.log
2025-08-13T04:11:19.9126613Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-13T04:11:19.9126630Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-13T04:11:19.9126643Z }
2025-08-13T04:11:19.9237071Z ???? ULTRATHINK: Testing Supabase auth service health...
2025-08-13T04:11:20.0319801Z ??? Supabase database connection test: { success: true, error: undefined }
2025-08-13T04:11:20.2391836Z ??? Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-13T04:11:20.2423730Z ??? Stripe event verified: charge.updated ID: evt_3RvW0nE6FvhUKV1b1E2GtiSd
2025-08-13T04:11:20.7192923Z ???? Handling charge.updated
2025-08-13T04:11:20.7202249Z ???? Charge updated: py_3RvW0nE6FvhUKV1b12KHd8bz, Status: succeeded
2025-08-13T04:11:21.0985679Z ??? Updated receipt for charge: py_3RvW0nE6FvhUKV1b12KHd8bzEnding Log Tail of existing logs ---Starting Live Log Stream ---
2025-08-13T04:12:31.5423098Z {"msg":"subscription.create","subId":"sub_1RvW38E6FvhUKV1bdM6yUkVe","customerId":"cus_SrEW5OpRkEc0oO","idemKey":"sub:cus_SrEW5OpRkEc0oO:price_1RC3HTE6FvhUKV1bE9D6zf6e:1"}
2025-08-13T04:12:31.8050331Z {"msg":"payment_intent.create.manual","id":"pi_3RvW39E6FvhUKV1b1SpeqZUH"}
2025-08-13T04:12:32.0089121Z 🚀 Webhook received at: 2025-08-13T04:12:32.008Z
2025-08-13T04:12:32.0122527Z 🔧 Environment check: {
2025-08-13T04:12:32.0296044Z   hasStripeSecret: true,
2025-08-13T04:12:32.0300650Z   hasWebhookSecret: true,
2025-08-13T04:12:32.0305077Z   hasSupabaseUrl: true,
2025-08-13T04:12:32.0309524Z   hasServiceKey: true,
2025-08-13T04:12:32.0314137Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-13T04:12:32.0318629Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-13T04:12:32.0323075Z }
2025-08-13T04:12:32.0492646Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-13T04:12:32.0511746Z 🚀 Webhook received at: 2025-08-13T04:12:32.051Z
2025-08-13T04:12:32.0519609Z 🔧 Environment check: {
2025-08-13T04:12:32.0519738Z   hasStripeSecret: true,
2025-08-13T04:12:32.0519762Z   hasWebhookSecret: true,
2025-08-13T04:12:32.0519777Z   hasSupabaseUrl: true,
2025-08-13T04:12:32.0519791Z   hasServiceKey: true,
2025-08-13T04:12:32.0519843Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-13T04:12:32.0519861Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-13T04:12:32.0519876Z }
2025-08-13T04:12:32.0691806Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-13T04:12:32.2820961Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-13T04:12:32.3918598Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-13T04:12:32.5242625Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-13T04:12:32.5264712Z ✅ Stripe event verified: customer.subscription.created ID: evt_1RvW39E6FvhUKV1beB4yDq45
2025-08-13T04:12:32.5756882Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-13T04:12:32.5767580Z ✅ Stripe event verified: customer.updated ID: evt_1RvW39E6FvhUKV1byLNePdVF
2025-08-13T04:12:32.9681649Z 👉 Handling customer.updated (invoice_settings) { id: 'cus_SrEW5OpRkEc0oO', default_payment_method: null }
2025-08-13T04:12:33.0092437Z 👉 Handling customer.subscription.created
2025-08-13T04:12:33.0135195Z 🔗 Subscription: sub_1RvW38E6FvhUKV1bdM6yUkVe, Customer: cus_SrEW5OpRkEc0oO, Status: incomplete
2025-08-13T04:12:33.0135315Z 💰 Product: prod_S6Fn893jGxRhKk, Quantity: 1
2025-08-13T04:12:33.0135344Z 📅 Billing Period Debug: {
2025-08-13T04:12:33.0135362Z   current_period_start: undefined,
2025-08-13T04:12:33.0135434Z   current_period_end: undefined,
2025-08-13T04:12:33.0135452Z   current_period_start_iso: null,
2025-08-13T04:12:33.0135468Z   current_period_end_iso: null,
2025-08-13T04:12:33.0135483Z   created: 1755058350,
2025-08-13T04:12:33.0135498Z   start_date: 1755058350,
2025-08-13T04:12:33.0135515Z   billing_cycle_anchor: 1755058350,
2025-08-13T04:12:33.0135531Z   status: 'incomplete'
2025-08-13T04:12:33.0135546Z }
2025-08-13T04:12:33.1776115Z 📅 Price interval: year, interval_count: 1
2025-08-13T04:12:33.1776515Z 👤 Fetching customer details for: cus_SrEW5OpRkEc0oO
2025-08-13T04:12:33.3438502Z 🔍 STRIPE CUSTOMER DEBUG - ID: cus_SrEW5OpRkEc0oO {
2025-08-13T04:12:33.3438987Z   customerName: 'NO_NAME',
2025-08-13T04:12:33.3439010Z   fullName: 'NO_FULL_NAME',
2025-08-13T04:12:33.3439031Z   customerEmail: '<EMAIL>',
2025-08-13T04:12:33.3439046Z   customerKeys: [
2025-08-13T04:12:33.3439062Z     'id',                    'object',
2025-08-13T04:12:33.3439083Z     'address',               'balance',
2025-08-13T04:12:33.3439099Z     'created',               'currency',
2025-08-13T04:12:33.3439116Z     'default_source',        'delinquent',
2025-08-13T04:12:33.3439132Z     'description',           'discount',
2025-08-13T04:12:33.3439221Z     'email',                 'invoice_prefix',
2025-08-13T04:12:33.3439238Z'invoice_settings',      'livemode',
2025-08-13T04:12:33.3439254Z     'metadata',              'name',
2025-08-13T04:12:33.3439271Z     'next_invoice_sequence', 'phone',
2025-08-13T04:12:33.3439287Z     'preferred_locales',     'shipping',
2025-08-13T04:12:33.3439304Z     'tax_exempt',            'test_clock'
2025-08-13T04:12:33.3439319Z   ],
2025-08-13T04:12:33.3439335Z   hasName: true,
2025-08-13T04:12:33.3439349Z   nameValue: null
2025-08-13T04:12:33.3439364Z }
2025-08-13T04:12:33.3439385Z 🔍 PARSED NAME DEBUG - Customer: cus_SrEW5OpRkEc0oO {
2025-08-13T04:12:33.3439419Z   originalFullName: null,
2025-08-13T04:12:33.3439436Z   parsedFirstName: 'EMPTY_FIRST',
2025-08-13T04:12:33.3439452Z   parsedLastName: 'EMPTY_LAST',
2025-08-13T04:12:33.3439470Z   parseResult: { firstName: null, lastName: null }
2025-08-13T04:12:33.3439485Z }
2025-08-13T04:12:33.3439506Z 📧 Customer details - email: <EMAIL>, firstName: null, lastName: null
2025-08-13T04:12:33.3439524Z 🔍 SUBSCRIPTION EVENT DEBUG - sub_1RvW38E6FvhUKV1bdM6yUkVe {
2025-08-13T04:12:33.3439541Z   stripeCustomerId: 'cus_SrEW5OpRkEc0oO',
2025-08-13T04:12:33.3439557Z   extractedEmail: '<EMAIL>',
2025-08-13T04:12:33.3439574Z   extractedFirstName: 'NO_FIRST_NAME',
2025-08-13T04:12:33.3439604Z   extractedLastName: 'NO_LAST_NAME',
2025-08-13T04:12:33.3439620Z   subscriptionStatus: 'incomplete',
2025-08-13T04:12:33.3445009Z   hasCustomerName: false
2025-08-13T04:12:33.3445041Z }
2025-08-13T04:12:33.3445067Z 👤 Creating/finding user <NAME_EMAIL>
2025-08-13T04:12:33.3445093Z 🔍 ensureUserProfile called with: customer=cus_SrEW5OpRkEc0oO, email=<EMAIL>, firstName=null, lastName=null
2025-08-13T04:12:33.3445112Z 🔍 Looking for existing profile by stripe_customer_id: cus_SrEW5OpRkEc0oO
2025-08-13T04:12:33.4401501Z 🔍 Looking for existing profile by email: <EMAIL>
2025-08-13T04:12:33.6409721Z 👤 Creating new auth user and profile for: <EMAIL>
2025-08-13T04:12:33.6418377Z 🔄 Attempt 1 to create auth user for: <EMAIL>
2025-08-13T04:12:33.6426051Z 🔍 ULTRATHINK: Creating user with payload: {
2025-08-13T04:12:33.6426197Z   email: '<EMAIL>',
2025-08-13T04:12:33.6426222Z   email_confirm: true,
2025-08-13T04:12:33.6426237Z   metadata_keys: [
2025-08-13T04:12:33.6426249Z     'first_name',
2025-08-13T04:12:33.6426264Z     'last_name',
2025-08-13T04:12:33.6426276Z     'full_name',
2025-08-13T04:12:33.6426289Z     'source',
2025-08-13T04:12:33.6426342Z     'stripe_customer_id'
2025-08-13T04:12:33.6426356Z   ],
2025-08-13T04:12:33.6426371Z   email_domain: 'gmail.com',
2025-08-13T04:12:33.6426383Z   email_length: 26
2025-08-13T04:12:33.6426396Z }
2025-08-13T04:12:33.8565531Z ✅ Created auth user: e4240442-f615-4793-b58e-a6b6115fd3aa
2025-08-13T04:12:33.9815812Z ❌ Error creating profile: {
2025-08-13T04:12:33.9816091Z   code: '23505',
2025-08-13T04:12:33.9816126Z   details: 'Key (email)=(<EMAIL>) already exists.',
2025-08-13T04:12:33.9816139Z   hint: null,
2025-08-13T04:12:33.9816156Z   message: 'duplicate key value violates unique constraint "profiles_email_key"'
2025-08-13T04:12:33.9816168Z }
2025-08-13T04:12:33.9816662Z 🔄 Duplicate email detected, looking for existing profile after race condition...
2025-08-13T04:12:35.6977441Z ✅ Found existing profile after race condition: e4240442-f615-4793-b58e-a6b6115fd3aa
2025-08-13T04:12:35.6977787Z ✅ Successfully updated profile after race condition
2025-08-13T04:12:35.6977847Z ✅ User profile resolved: e4240442-f615-4793-b58e-a6b6115fd3aa
2025-08-13T04:12:35.6977896Z 👤 Updating profile team admin status: false
2025-08-13T04:12:35.6978041Z ✅ Profile updated - team admin: false
2025-08-13T04:12:35.6978098Z ⚠️ Missing billing period data, attempting to retrieve from Stripe...
2025-08-13T04:12:35.6978151Z ✅ Retrieved fresh billing data: { current_period_start: null, current_period_end: null }
2025-08-13T04:12:35.6978199Z 💾 Creating/updating subscription record in database
2025-08-13T04:12:35.6978250Z ✅ Subscription created/updated in DB: 3c1fc16d-03a3-42fc-bbd1-2241772e9125
2025-08-13T04:12:35.6978295Z 🎫 License sync - target: 1, current: 0
2025-08-13T04:12:35.6978343Z 🎫 Creating 1 Basic-Individual licenses with status: pending
2025-08-13T04:12:35.6978387Z ✅ Successfully created 1 licenses
2025-08-13T04:12:35.6978435Z 🔄 Updating individual license statuses to: inactive
2025-08-13T04:12:35.6978502Z ✅ Individual license statuses updated
2025-08-13T04:12:35.6978550Z ✅ Subscription processing completed successfully
2025-08-13T04:12:48.8519378Z 🚀 Webhook received at: 2025-08-13T04:12:48.851Z
2025-08-13T04:12:48.8595523Z 🔧 Environment check: {
2025-08-13T04:12:48.8595808Z   hasStripeSecret: true,
2025-08-13T04:12:48.8595828Z   hasWebhookSecret: true,
2025-08-13T04:12:48.8595845Z   hasSupabaseUrl: true,
2025-08-13T04:12:48.8595860Z   hasServiceKey: true,
2025-08-13T04:12:48.8595884Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-13T04:12:48.8595898Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-13T04:12:48.8596020Z }
2025-08-13T04:12:48.8610769Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-13T04:12:48.8914874Z 🚀 Webhook received at: 2025-08-13T04:12:48.891Z
2025-08-13T04:12:48.8920446Z 🔧 Environment check: {
2025-08-13T04:12:48.8920586Z   hasStripeSecret: true,
2025-08-13T04:12:48.8920606Z   hasWebhookSecret: true,
2025-08-13T04:12:48.8920621Z   hasSupabaseUrl: true,
2025-08-13T04:12:48.8920637Z   hasServiceKey: true,
2025-08-13T04:12:48.8920656Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-13T04:12:48.8920672Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-13T04:12:48.8920716Z }
2025-08-13T04:12:48.9011073Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-13T04:12:49.1215295Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-13T04:12:49.1426180Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-13T04:12:49.3815786Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-13T04:12:49.3869485Z ✅ Stripe event verified: payment_intent.succeeded ID: evt_3RvW39E6FvhUKV1b1Ol5sKNL
2025-08-13T04:12:49.4726478Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-13T04:12:49.4829949Z ✅ Stripe event verified: charge.succeeded ID: evt_3RvW39E6FvhUKV1b1IPCPIBf
2025-08-13T04:12:50.0457757Z 👉 Handling charge.succeeded
2025-08-13T04:12:50.0467376Z 💳 Charge succeeded: ch_3RvW39E6FvhUKV1b1NV718Od, Amount: 12000, Customer: cus_SrEW5OpRkEc0oO
2025-08-13T04:12:50.0520391Z 👉 Handling payment_intent.succeeded
2025-08-13T04:12:50.0527817Z 💳 Payment succeeded: pi_3RvW39E6FvhUKV1b1SpeqZUH, Amount: 12000, Customer: cus_SrEW5OpRkEc0oO
2025-08-13T04:12:50.3491001Z ✅ Stored receipt for charge: ch_3RvW39E6FvhUKV1b1NV718Od
2025-08-13T04:12:50.5878982Z 🔄 Fetching complete subscription details from Stripe: sub_1RvW38E6FvhUKV1bdM6yUkVe
2025-08-13T04:12:50.9294397Z 📅 Payment Success Billing Period Debug: {
2025-08-13T04:12:50.9295034Z   current_period_start: undefined,
2025-08-13T04:12:50.9295066Z   current_period_end: undefined,
2025-08-13T04:12:50.9295082Z   current_period_start_iso: null,
2025-08-13T04:12:50.9295099Z   current_period_end_iso: null,
2025-08-13T04:12:50.9295115Z   created: 1755058350,
2025-08-13T04:12:50.9295133Z   start_date: 1755058350,
2025-08-13T04:12:50.9295151Z   billing_cycle_anchor: 1755058350,
2025-08-13T04:12:50.9295165Z   status: 'incomplete'
2025-08-13T04:12:50.9295180Z }
2025-08-13T04:12:50.9306343Z ⚠️ Missing billing period data after payment success, using fallback calculation...
2025-08-13T04:12:51.0833629Z ✅ Calculated payment success billing periods: {
2025-08-13T04:12:51.0834028Z   current_period_start: '2025-08-13T04:12:30.000Z',
2025-08-13T04:12:51.0834057Z   current_period_end: '2026-08-13T04:12:30.000Z'
2025-08-13T04:12:51.0834080Z }
2025-08-13T04:12:51.3051368Z ✅ Updated subscription with complete billing period data: 3c1fc16d-03a3-42fc-bbd1-2241772e9125
2025-08-13T04:12:51.3055495Z 🔄 Updating licenses to active for subscription: 3c1fc16d-03a3-42fc-bbd1-2241772e9125
2025-08-13T04:12:51.4077480Z 🚀 Webhook received at: 2025-08-13T04:12:51.391Z
2025-08-13T04:12:51.4080662Z 🔧 Environment check: {
2025-08-13T04:12:51.4082201Z   hasStripeSecret: true,
2025-08-13T04:12:51.4083700Z   hasWebhookSecret: true,
2025-08-13T04:12:51.4085164Z   hasSupabaseUrl: true,
2025-08-13T04:12:51.4086646Z   hasServiceKey: true,
2025-08-13T04:12:51.4088112Z   supabaseUrl: 'https://izoutrnsxaao...',
2025-08-13T04:12:51.4089795Z   serviceKeyPrefix: 'sb_secret_pL6hGD8Q7l...'
2025-08-13T04:12:51.4091275Z }
2025-08-13T04:12:51.4092761Z 🔍 ULTRATHINK: Testing Supabase auth service health...
2025-08-13T04:12:51.5141101Z ✅ Supabase database connection test: { success: true, error: undefined }
2025-08-13T04:12:51.5428148Z ✅ Updated individual subscription and licenses to active status with expiry: 2026-08-13T04:12:30.000Z
2025-08-13T04:12:51.6691315Z ✅ Supabase auth service test: { success: true, error: undefined, userCount: 1 }
2025-08-13T04:12:51.6719539Z ✅ Stripe event verified: charge.updated ID: evt_3RvW39E6FvhUKV1b1kbd7bQv
2025-08-13T04:12:52.0115992Z 👉 Handling charge.updated
2025-08-13T04:12:52.0116416Z 🔄 Charge updated: ch_3RvW39E6FvhUKV1b1NV718Od, Status: succeeded
2025-08-13T04:12:52.4507189Z ✅ Updated receipt for charge: ch_3RvW39E6FvhUKV1b1NV718Od