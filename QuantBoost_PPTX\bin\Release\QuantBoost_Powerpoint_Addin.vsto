﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="QuantBoost_Powerpoint_Addin.vsto" version="*******" publicKeyToken="c6092c9ef89afb5c" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="QuantBoost_Powerpoint_Addin" asmv2:product="QuantBoost_Powerpoint_Addin" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.8.1" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="QuantBoost_Powerpoint_Addin.dll.manifest" size="48300">
      <assemblyIdentity name="QuantBoost_Powerpoint_Addin.dll" version="*******" publicKeyToken="c6092c9ef89afb5c" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>k7UG5snITGc/0J94wTbAfBi6Ff3sRLSGo6UuZkpz8N8=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=M16R2\danep" issuerKeyHash="582f05a61c8fcb2605b6d499994cc4e005b61052" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>Z/d30Yy3mdFiisPO4ZxMhG6yxbJw8KfJZNJCU1nXaTY=</DigestValue></Reference></SignedInfo><SignatureValue>PCdtDwINfNBVYTTYeP3IiW9SoEhRzRVa9/HjE79uyXbbvbPbTKyFoiGpCbM6u4T7+kQiTGy8Vycd24/M6229AXeNCBMHi+E2bndlAyFVldIns8auiWDqXsr5oEWCNw2kGIrqsYMsPa4EOzJdnQDjoxVBsW67JzcVevSE9YloGmU=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>urkeVljnx8Q4mQoWMIfwWHGhl8gf3Gh6XjHiKtBdOUnFTJQOFQm5FnqJN/ttKHjS0hdureML04AJfyRo+W2TgNFM+6AU2kCB4kiflUg6A9+NRIUE5wDnzGgVAjHXAwwttrJK6W+MP/7TnAJwLUz2EnGRzjLQd92a2J16iGG3Ozk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="3669d7595342d264c9a7f070b2c5b26e844c9ce1cec38a62d199b78cd177f767" Description="" Url=""><as:assemblyIdentity name="QuantBoost_Powerpoint_Addin.vsto" version="*******" publicKeyToken="c6092c9ef89afb5c" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=M16R2\danep</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>GIBnpEXUEfnTywWEfpQKhuXQcClBxyAC4RBZc0Y2Zts=</DigestValue></Reference></SignedInfo><SignatureValue>Ii+MSZUpUgz9UxRQ5smqC15OmD4DdN6SA+EeoIlglBQafhFocFjHXu1XNIa+Mzpthu5IsRiYYa+UFY/AgluDTjEjTPTzsSi4dQpzL650gPizVzvXubUzyJQKskwiqHF6Q4JI9lFyR8lAkZqxWMOrK5+C1YKp3pEEX2Y+Tm4UNAs=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>urkeVljnx8Q4mQoWMIfwWHGhl8gf3Gh6XjHiKtBdOUnFTJQOFQm5FnqJN/ttKHjS0hdureML04AJfyRo+W2TgNFM+6AU2kCB4kiflUg6A9+NRIUE5wDnzGgVAjHXAwwttrJK6W+MP/7TnAJwLUz2EnGRzjLQd92a2J16iGG3Ozk=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIBxTCCAS6gAwIBAgIQFG7X9mMlX4NMhpnYtde0iDANBgkqhkiG9w0BAQsFADAhMR8wHQYDVQQDHhYATQAxADYAUgAyAFwAZABhAG4AZQBwMB4XDTI1MDQxNzE5MjcwNloXDTI2MDQxODAxMjcwNlowITEfMB0GA1UEAx4WAE0AMQA2AFIAMgBcAGQAYQBuAGUAcDCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAurkeVljnx8Q4mQoWMIfwWHGhl8gf3Gh6XjHiKtBdOUnFTJQOFQm5FnqJN/ttKHjS0hdureML04AJfyRo+W2TgNFM+6AU2kCB4kiflUg6A9+NRIUE5wDnzGgVAjHXAwwttrJK6W+MP/7TnAJwLUz2EnGRzjLQd92a2J16iGG3OzkCAwEAATANBgkqhkiG9w0BAQsFAAOBgQCIWebVlWri2hRdROSu/IqnmkTk0lIcZPbSoCdaMyBh3BatDofCGDfd5uXPqEOk4kVQV8zXOCs/ihVcEe8SOLQUjGxktNS3dghBwp/RT8RP6qHhGNN4o+nCvwE2Wr38ad3IUJntBS4pdYDGrqT9Aeb4eojUdAxSCG+Snm3Ka7KHhw==</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>