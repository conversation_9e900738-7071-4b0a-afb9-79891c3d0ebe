-- CRITICAL FIX: Update trigger function to resolve auth user creation failures
-- This fixes the "Database error creating new user" issue
-- Run this in Supabase SQL Editor

-- First, get the current function definition to backup
-- (Optional: Check current function first)
-- select pg_get_functiondef(oid) from pg_proc where proname = 'handle_new_user_profile_creation';

-- Drop and recreate the function with proper security definer and search path
DROP FUNCTION IF EXISTS public.handle_new_user_profile_creation() CASCADE;

-- Recreate with proper security settings
CREATE OR REPLACE FUNCTION public.handle_new_user_profile_creation()
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Create profile when new auth user is created
  INSERT INTO public.profiles (
    id,
    email,
    full_name,
    first_name,
    last_name,
    is_team_admin,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
    FALS<PERSON>,
    NOW(),
    NOW()
  );
  RETURN NEW;
END;
$$ 
LANGUAGE plpgsql 
SECURITY DEFINER 
SET search_path = public, auth;

-- Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION public.handle_new_user_profile_creation();

-- Fix the update_updated_at_column function as well
DROP FUNCTION IF EXISTS public.update_updated_at_column() CASCADE;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ 
LANGUAGE plpgsql 
SECURITY DEFINER 
SET search_path = public;

-- Recreate trigger for webhook_events if needed
DROP TRIGGER IF EXISTS update_webhook_events_updated_at ON webhook_events;
CREATE TRIGGER update_webhook_events_updated_at
    BEFORE UPDATE ON webhook_events
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Also fix the create_license_for_user function
-- First check if it exists and get its definition
-- select pg_get_functiondef(oid) from pg_proc where proname = 'create_license_for_user';

-- You'll need to recreate this function with SECURITY DEFINER as well
-- (I can help with this once you provide the current function definition)

-- Test the fix by creating a test user (optional - remove if not needed)
-- SELECT auth.admin_create_user('<EMAIL>', 'password123', true);
