import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

// Mid-tier hardening: reuse recent pending SetupIntent; deterministic idempotency key; basic auth verification placeholder.

export async function POST(request: NextRequest) {
  const started = Date.now();
  try {
    const { subscriptionId } = await request.json();
    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    // Extract Supabase auth JWT from Authorization header (Bearer) or cookie if present
    const authHeader = request.headers.get('authorization') || '';
    const jwt = authHeader.startsWith('Bearer ') ? authHeader.substring(7) : null;
    if (!jwt) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Server service key for RLS bypass (ensure secured in env)
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    if (!supabaseServiceKey || !supabaseUrl) {
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 });
    }
    const supabase = createClient(supabaseUrl, supabaseServiceKey, { global: { headers: { Authorization: `Bearer ${jwt}` } } });

    // Verify user token is valid
    const { data: userResp, error: userErr } = await supabase.auth.getUser(jwt);
    if (userErr || !userResp?.user) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
    }
    const userId = userResp.user.id;

    // Confirm subscription record belongs to this user in our database before touching Stripe
    const { data: subRow, error: subRowErr } = await supabase
      .from('subscriptions')
      .select('stripe_subscription_id, user_id')
      .eq('stripe_subscription_id', subscriptionId)
      .single();

    if (subRowErr || !subRow) {
      return NextResponse.json({ error: 'Subscription not found' }, { status: 404 });
    }
    if (subRow.user_id !== userId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const key = (globalThis as any).process?.env?.STRIPE_SECRET_KEY as string | undefined;
    if (!key) {
      return NextResponse.json({ error: 'Stripe secret key not configured' }, { status: 500 });
    }
    const stripe = new Stripe(key, { apiVersion: '2025-03-31.basil' });

    // Retrieve subscription & customer
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    const customerId = subscription.customer as string | undefined;
    if (!customerId) {
      return NextResponse.json({ error: 'Customer not found for subscription' }, { status: 404 });
    }

  // AuthZ check has been completed against internal DB above.

    // List a few recent setup intents to attempt reuse (limit small for performance)
    const intents = await stripe.setupIntents.list({
      customer: customerId,
      limit: 5,
    });

    const now = Date.now();
    const reusable = intents.data.find(si => {
      const ageMs = now - (si.created * 1000);
      const status = si.status;
      const okStatus = status === 'requires_payment_method' || status === 'requires_confirmation';
      const sameSub = si.metadata?.subscription_id === subscriptionId || !si.metadata?.subscription_id; // allow legacy intents without metadata
      const noError = !('last_setup_error' in si) || !(si as any).last_setup_error;
      return okStatus && sameSub && ageMs < 60000 && noError; // < 60s old
    });

    if (reusable) {
      console.log(JSON.stringify({
        msg: 'setup_intent.reuse',
        intentId: reusable.id,
        subscriptionId,
        customerId,
        ageMs: now - (reusable.created * 1000),
        durationMs: now - started
      }));
      return NextResponse.json({ client_secret: reusable.client_secret, reused: true });
    }

    // Deterministic idempotency key (one active create per subscription at a time)
    const idempotencyKey = `pmupd:${subscriptionId}`;

    // Create with metadata; single lightweight retry on transient errors
    let lastErr: any = null;
    for (let attempt = 1; attempt <= 2; attempt++) {
      try {
        const setupIntent = await stripe.setupIntents.create({
          customer: customerId,
          usage: 'off_session',
          metadata: {
            purpose: 'pm_update',
            subscription_id: subscriptionId,
            customer_id: customerId,
            app: 'quantboost'
          }
        }, { idempotencyKey });
        console.log(JSON.stringify({
          msg: 'setup_intent.create',
          intentId: setupIntent.id,
          subscriptionId,
          customerId,
          reused: false,
          attempt,
          durationMs: Date.now() - started
        }));
        return NextResponse.json({ client_secret: setupIntent.client_secret, reused: false });
      } catch (e: any) {
        lastErr = e;
        // Retry only on Stripe 5xx or rate limit
        if (attempt === 1 && (e?.statusCode >= 500 || e?.statusCode === 429)) {
          await new Promise(r => setTimeout(r, 250 + Math.random() * 150));
          continue;
        }
        break;
      }
    }

    console.error('setup_intent.create.failed', { subscriptionId, customerId, error: lastErr?.message });
    return NextResponse.json({ error: 'Failed to create setup intent' }, { status: 500 });
  } catch (error: any) {
    console.error('setup_intent.route.error', { message: error?.message });
    return NextResponse.json({ error: 'Failed to create setup intent' }, { status: 500 });
  }
}
