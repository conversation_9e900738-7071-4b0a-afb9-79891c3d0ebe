# environmentContext.md - Runtime and IDE Environment (Multi-Project Workspace)

**Purpose:** Specifies constraints and configurations related to the OS, IDE (VS Code), extensions, containerization, and environment quirks, noting subproject differences where they exist.

**Instructions for Dane:**
*   Global items (OS, Shell, Core IDE) are top-level.
*   Specify if Extensions/Settings are primarily for a certain subproject type (e.g., `[Frontend]`, `[VSTO]`).
*   Group Containerization details if they relate to specific subprojects.
*   Prefix Known Issues with the relevant subproject if applicable (`[SubprojectName]`).
*   Create subsections under "Environment Validation Steps" for each subproject.

---

## 1. Operating System & Shell

<!-- #OS -->
**Target Operating System(s):** [Specify primary OS - often global]

<!-- #Shell -->
**Development Shell:** [Specify expected shell - often global]

---

## 2. IDE Configuration (VS Code)

<!-- #IDE -->
**IDE:** Visual Studio Code (VS Code)

<!-- #IDEExtensions -->
**Required VS Code Extensions:**
*   `[Global]` [Extension Name (e.g., GitLens)]: [ID] - [Purpose]
*   `[Frontend]` [Extension Name (e.g., ESLint)]: [ID] - [Purpose]
*   `[Frontend]` [Extension Name (e.g., Prettier)]: [ID] - [Purpose]
*   `[Backend]` [Extension Name (e.g., Docker)]: [ID] - [Purpose]
*   `[VSTO]` [Extension Name (e.g., C# Dev Kit)]: [ID] - [Purpose]
*   `[Global]` GitHub Copilot: [ID: `github.copilot`] - **Essential**
*   `[Global]` GitHub Copilot Chat: [ID: `github.copilot-chat`] - **Essential**
*   [Add others, tagging relevance]

<!-- #IDESettings -->
**Key VS Code Settings (.vscode/settings.json):**
*   `[Global]` [Setting Name (e.g., `files.eol`)]: [Value]
*   `[Frontend]` [Setting Name (e.g., `editor.defaultFormatter` for TS/JS)]: [`esbenp.prettier-vscode`]
*   `[VSTO]` [Setting Name (e.g., `omnisharp.useModernNet`)]: [`false` - if targeting .NET Framework]
*   [List key settings, tagging relevance]

---

## 3. Containerization (If Used)

<!-- #Containerization -->
**Container Technology:** [e.g., Docker, Podman, None]
*   `[Backend]` **Primary Image(s)/Compose:** [e.g., `backend/docker-compose.yml` for DB/Redis]
*   `[Shared]` **Build Images:** [e.g., Common builder image defined in `docker/Dockerfile.build`]
*   **Key Commands:** [Specify commands per context, e.g., `(cd backend && docker-compose up)`]

---

## 4. Known Environment Issues & Quirks

<!-- #KnownIssues -->
**Current Issues:**
*   `[VSTO]` **Issue 1:** [e.g., Visual Studio debugger occasionally fails to attach on first attempt.]
    *   **Workaround/Fix:** [e.g., Stop debugging, clean solution, restart VS, try again.]
*   `[Frontend]` **Issue 2:** [e.g., Specific npm package X requires manual deletion of `node_modules` before `npm install` on Windows.]
    *   **Workaround/Fix:** [e.g., Run `rm -rf node_modules && npm install`.]
*   [Add other known problems, tagging the relevant subproject.]

---

## 5. Environment Validation Steps

<!-- #Validation -->
**How to Verify Setup:**

### Shared / Global
*   [ ] Run `git --version` - Expected Output: [e.g., `git version 2.x.x`]
*   [ ] [Other global checks]

### Frontend Subproject
*   [ ] Run `(cd frontend && node -v)` - Expected Output: [e.g., `v18.x.x`]
*   [ ] Run `(cd frontend && npm install)` - Expected Output: Completes without critical errors.
*   [ ] Run `(cd frontend && npm run lint)` - Expected Output: No linting errors.
*   [ ] Successfully start dev server via `(cd frontend && npm run dev)`.

### Backend Subproject
*   [ ] Run `(cd backend && node -v)` - Expected Output: [e.g., `v18.x.x`]
*   [ ] Run `(cd backend && docker-compose ps)` - Expected Output: Shows running `postgres`, `redis` containers.
*   [ ] Run `(cd backend && npm install)` - Expected Output: Completes without critical errors.
*   [ ] Run `(cd backend && npx prisma migrate status)` - Expected Output: Database schema is up to date.
*   [ ] Successfully start dev server via `(cd backend && npm run start:dev)`.

### VSTO Add-in Subproject
*   [ ] Build Solution in Visual Studio - Expected Output: Build Succeeded.
*   [ ] Start Debugging (F5) in Visual Studio - Expected Output: PowerPoint launches with add-in loaded.

<!-- Add validation sections for other subprojects -->

---