"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence, LayoutGroup } from 'motion/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button, Input } from "@/components/ui";
import { useToast } from "@/components/ui/toast";
import { apiClient, Subscription, License, isApiSuccess, handleApiError } from '@/lib/api';

interface SubscriptionWithLicenses extends Subscription {
  licenses: License[];
}

interface TeamMember {
  id: string;
  email: string;
  status: 'active' | 'assigned' | 'pending';
  assignedDate?: string;
  licenseId: string;
}

interface TeamAssignmentCardProps {
  subscription: SubscriptionWithLicenses;
  onDataRefresh: () => Promise<void>;
}

// Animation variants for smooth transitions
const cardVariants = {
  hidden: { opacity: 0, y: 20, scale: 0.95 },
  visible: { 
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: {
      type: "spring" as const,
      stiffness: 300,
      damping: 30
    }
  },
  exit: { 
    opacity: 0, 
    y: -20, 
    scale: 0.95,
    transition: {
      duration: 0.2
    }
  }
};

const memberCardVariants = {
  hidden: { opacity: 0, x: -20, scale: 0.9 },
  visible: { 
    opacity: 1, 
    x: 0, 
    scale: 1,
    transition: {
      type: "spring" as const,
      stiffness: 400,
      damping: 25
    }
  },
  exit: { 
    opacity: 0, 
    x: 20, 
    scale: 0.9,
    transition: {
      duration: 0.3
    }
  }
};

const licenseCountVariants = {
  initial: { scale: 1 },
  updated: { 
    scale: [1, 1.2, 1],
    transition: {
      duration: 0.5,
      times: [0, 0.5, 1]
    }
  }
};

export function TeamAssignmentCard({ subscription, onDataRefresh }: TeamAssignmentCardProps) {
  const [inviteEmail, setInviteEmail] = useState('');
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const { addToast } = useToast();

  // Helper functions (extracted from team page)
  const getAvailableLicenses = (): License[] => {
    return subscription.licenses.filter(license => 
      license.status === 'unassigned' || (!license.user_id && !license.email)
    );
  };

  const getAssignedLicenses = (): License[] => {
    return subscription.licenses.filter(license => 
      license.status === 'assigned' || license.status === 'active'
    );
  };

  const getTeamMembers = (): TeamMember[] => {
    return getAssignedLicenses().map(license => ({
      id: license.id,
      email: license.email || 'Pending...',
      status: license.status as 'active' | 'assigned',
      assignedDate: license.assigned_at,
      licenseId: license.id
    }));
  };

  const handleInviteTeamMember = async () => {
    if (!inviteEmail.trim()) return;

    setActionLoading('invite');
    try {
      const response = await apiClient.inviteTeamMember(subscription.id, {
        target_email: inviteEmail.trim()
      });

      if (isApiSuccess(response)) {
        addToast({
          type: 'success',
          title: '🎉 Invitation Sent!',
          description: `Team invitation sent to ${inviteEmail}`
        });
        setInviteEmail('');
        await onDataRefresh();
      } else {
        const errorMsg = handleApiError(response, 'Failed to send invitation');
        addToast({
          type: 'error',
          title: '❌ Invitation Failed',
          description: errorMsg || 'Failed to send invitation'
        });
      }
    } catch (error) {
      console.error('Error inviting team member:', error);
      addToast({
        type: 'error',
        title: '❌ Error',
        description: 'Failed to send invitation'
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleUnassignLicense = async (licenseId: string, email: string) => {
    setActionLoading(`unassign-${licenseId}`);
    try {
      const response = await apiClient.unassignTeamLicense(subscription.id, licenseId);

      if (isApiSuccess(response)) {
        addToast({
          type: 'success',
          title: '✅ License Removed',
          description: `License removed from ${email}`
        });
        await onDataRefresh();
      } else {
        const errorMsg = handleApiError(response, 'Failed to unassign license');
        addToast({
          type: 'error',
          title: '❌ Removal Failed',
          description: errorMsg || 'Failed to unassign license'
        });
      }
    } catch (error) {
      console.error('Error unassigning license:', error);
      addToast({
        type: 'error',
        title: '❌ Error',
        description: 'Failed to unassign license'
      });
    } finally {
      setActionLoading(null);
    }
  };

  const availableLicenses = getAvailableLicenses().length;
  const assignedLicenses = getAssignedLicenses().length;
  const teamMembers = getTeamMembers();

  return (
    <LayoutGroup>
      <motion.div
        layout
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {/* Team Assignment Header Card */}
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
          <CardHeader>
            <motion.div layout>
              <CardTitle className="flex items-center gap-2">
                👥 Team Assignment
                <motion.span 
                  className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full"
                  variants={licenseCountVariants}
                  animate={availableLicenses > 0 ? "initial" : "updated"}
                  key={`available-${availableLicenses}`}
                >
                  {availableLicenses} available
                </motion.span>
              </CardTitle>
              <CardDescription>
                Invite team members and assign licenses to your {subscription.plan_id} subscription
              </CardDescription>
            </motion.div>
          </CardHeader>
          <CardContent>
            <motion.div layout className="space-y-4">
              {/* License Overview */}
              <div className="grid grid-cols-3 gap-4 p-4 bg-white rounded-lg border">
                <motion.div 
                  layout
                  className="text-center"
                >
                  <div className="text-2xl font-bold text-gray-900">{subscription.quantity}</div>
                  <div className="text-xs text-gray-500">Total Licenses</div>
                </motion.div>
                <motion.div 
                  layout
                  className="text-center"
                >
                  <motion.div 
                    className="text-2xl font-bold text-green-600"
                    animate={{ scale: assignedLicenses > 0 ? [1, 1.1, 1] : 1 }}
                    transition={{ duration: 0.3 }}
                    key={`assigned-${assignedLicenses}`}
                  >
                    {assignedLicenses}
                  </motion.div>
                  <div className="text-xs text-gray-500">Assigned</div>
                </motion.div>
                <motion.div 
                  layout
                  className="text-center"
                >
                  <motion.div 
                    className="text-2xl font-bold text-blue-600"
                    animate={{ scale: availableLicenses > 0 ? [1, 1.1, 1] : 1 }}
                    transition={{ duration: 0.3 }}
                    key={`available-counter-${availableLicenses}`}
                  >
                    {availableLicenses}
                  </motion.div>
                  <div className="text-xs text-gray-500">Available</div>
                </motion.div>
              </div>

              {/* Quick Invite Form */}
              {availableLicenses > 0 && (
                <motion.div 
                  layout
                  className="p-4 bg-white rounded-lg border border-dashed border-blue-300"
                  whileHover={{ borderColor: '#3B82F6' }}
                >
                  <div className="flex gap-2">
                    <Input
                      type="email"
                      placeholder="Enter team member email..."
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleInviteTeamMember()}
                      className="flex-1"
                    />
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button 
                        onClick={handleInviteTeamMember}
                        disabled={!inviteEmail.trim() || actionLoading === 'invite'}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        {actionLoading === 'invite' ? '📧 Sending...' : '📧 Send Invite'}
                      </Button>
                    </motion.div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    💡 Tip: Team members will receive an email with setup instructions
                  </p>
                </motion.div>
              )}
            </motion.div>
          </CardContent>
        </Card>

        {/* Team Members List */}
        {teamMembers.length > 0 && (
          <Card>
            <CardHeader>
              <motion.div layout>
                <CardTitle>Team Members ({teamMembers.length})</CardTitle>
                <CardDescription>
                  Manage your current team member licenses
                </CardDescription>
              </motion.div>
            </CardHeader>
            <CardContent>
              <motion.div layout className="space-y-3">
                <AnimatePresence mode="popLayout">
                  {teamMembers.map((member, index) => (
                    <motion.div
                      key={member.id}
                      layoutId={`member-${member.id}`}
                      variants={memberCardVariants}
                      initial="hidden"
                      animate="visible"
                      exit="exit"
                      transition={{ delay: index * 0.1 }}
                      className="p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all duration-200"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <motion.div
                            className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-sm font-semibold"
                            whileHover={{ scale: 1.1 }}
                          >
                            {member.email.charAt(0).toUpperCase()}
                          </motion.div>
                          <div>
                            <div className="font-medium text-gray-900">{member.email}</div>
                            <div className="text-xs text-gray-500">
                              Added {member.assignedDate 
                                ? new Date(member.assignedDate).toLocaleDateString() 
                                : 'recently'}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <motion.span 
                            className={`text-xs px-2 py-1 rounded-full ${
                              member.status === 'active' 
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}
                            whileHover={{ scale: 1.05 }}
                          >
                            {member.status === 'active' ? '✅ Active' : '⏳ Pending'}
                          </motion.span>
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleUnassignLicense(member.licenseId, member.email)}
                              disabled={actionLoading === `unassign-${member.licenseId}`}
                              className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
                            >
                              {actionLoading === `unassign-${member.licenseId}` ? '⏳' : '🗑️'}
                            </Button>
                          </motion.div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </motion.div>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {teamMembers.length === 0 && (
          <motion.div
            layout
            variants={cardVariants}
            initial="hidden"
            animate="visible"
          >
            <Card className="border-dashed border-gray-300">
              <CardContent className="p-8 text-center">
                <div className="text-4xl mb-4">👋</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to build your team?</h3>
                <p className="text-gray-500 mb-4">
                  You have {availableLicenses} available license{availableLicenses !== 1 ? 's' : ''} to assign to team members.
                </p>
                {availableLicenses === 0 && (
                  <p className="text-sm text-blue-600">
                    💡 <a href="/pricing" className="underline">Upgrade your plan</a> to add more team members
                  </p>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </motion.div>
    </LayoutGroup>
  );
}