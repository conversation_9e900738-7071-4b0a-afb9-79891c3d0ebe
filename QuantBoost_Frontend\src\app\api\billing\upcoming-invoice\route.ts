import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

export async function POST(request: NextRequest) {
  try {
    const { subscriptionId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      );
    }

    const key = (globalThis as any).process?.env?.STRIPE_SECRET_KEY as string | undefined;
    if (!key) {
      return NextResponse.json({ error: 'Stripe secret key not configured' }, { status: 500 });
    }
    const stripe = new Stripe(key, {
      apiVersion: '2025-03-31.basil',
    });

    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['items.data.price'],
    });

    if ((subscription as any).cancel_at_period_end) {
      let billingInterval: string | null = null;
      if (subscription.items?.data?.length) {
        const first = subscription.items.data[0];
        if (first.price && typeof first.price === 'object' && first.price.recurring) {
          billingInterval = first.price.recurring.interval;
        }
      }
      return NextResponse.json({
        amount_due: 0,
        currency: subscription.currency || 'usd',
        period_start: null,
        period_end: null,
        subtotal: 0,
        total: 0,
        billing_interval: billingInterval,
      });
    }

    let totalAmount = 0;
    let billingInterval: string | null = null;
    if (subscription.items && subscription.items.data) {
      for (const item of subscription.items.data) {
        if (item.price && typeof item.price === 'object' && item.price.unit_amount) {
          totalAmount += item.price.unit_amount * (item.quantity || 1);
          if (!billingInterval && item.price.recurring?.interval) {
            billingInterval = item.price.recurring.interval;
          }
        }
      }
    }

    let periodStart = null;
    let periodEnd = null;
    if (subscription.items && subscription.items.data && subscription.items.data.length > 0) {
      const firstItem = subscription.items.data[0];
      periodStart = (firstItem as any).current_period_start ?? null;
      periodEnd = (firstItem as any).current_period_end ?? null;
    }

    // If nothing computed, default to zero
    if (!totalAmount) {
      return NextResponse.json({
        amount_due: 0,
        currency: subscription.currency || 'usd',
        period_start: periodStart,
        period_end: periodEnd,
        subtotal: 0,
        total: 0,
        billing_interval: billingInterval,
      });
    }

    return NextResponse.json({
      amount_due: totalAmount,
      currency: subscription.currency || 'usd',
      period_start: periodStart,
      period_end: periodEnd,
      subtotal: totalAmount,
      total: totalAmount,
      billing_interval: billingInterval,
    });
  } catch (error) {
    console.error('Error retrieving subscription info:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve billing information' },
      { status: 500 }
    );
  }
}
