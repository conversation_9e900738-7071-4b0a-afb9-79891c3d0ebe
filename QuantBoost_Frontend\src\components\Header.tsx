"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence, Variants } from 'motion/react';
import Link from 'next/link';
import AnimatedQuantBoostLogo from '@/components/AnimatedQuantBoostLogo';

// Animation variants
const logoVariants: Variants = {
  idle: { 
    scale: 1,
    rotate: 0,
    transition: { 
      duration: 0.6,
      ease: "easeOut"
    }
  },
  hover: { 
    scale: 1.08,
    rotate: 2,
    transition: { 
      duration: 0.08,
      type: "spring",
      stiffness: 800,
      damping: 15
    }
  }
};

const navItemVariants: Variants = {
  idle: { 
    scale: 1,
    color: "rgb(0, 0, 0)"
  },
  hover: { 
    scale: 1.02,
    color: "rgb(16, 185, 129)",
    transition: { duration: 0.2 }
  }
};

const headerButtonVariants: Variants = {
  idle: { 
    scale: 1, 
    y: 0,
    boxShadow: "0 0 0 0px rgba(16, 185, 129, 0)"
  },
  hover: { 
    scale: 1.05,
    y: -1,
    boxShadow: "0 4px 12px -2px rgba(16, 185, 129, 0.3)",
    transition: { 
      type: "spring",
      stiffness: 400,
      damping: 17
    }
  },
  tap: {
    scale: 0.98,
    transition: { duration: 0.1 }
  }
};

const primaryButtonVariants: Variants = {
  idle: { 
    scale: 1, 
    y: 0,
    boxShadow: "0 4px 14px 0 rgba(16, 185, 129, 0.2)"
  },
  hover: { 
    scale: 1.05,
    y: -2,
    boxShadow: "0 20px 25px -5px rgba(16, 185, 129, 0.4)",
    transition: { 
      type: "spring",
      stiffness: 400,
      damping: 17
    }
  },
  tap: {
    scale: 0.97,
    transition: { duration: 0.1 }
  }
};

const dropdownVariants: Variants = {
  hidden: { 
    opacity: 0, 
    scale: 0.95,
    y: -10,
    transition: {
      duration: 0.15,
      ease: "easeIn"
    }
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    y: 0,
    transition: {
      duration: 0.2,
      ease: "easeOut",
      staggerChildren: 0.05
    }
  }
};

const dropdownItemVariants: Variants = {
  hidden: { opacity: 0, x: -10 },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: { duration: 0.15 }
  }
};

interface HeaderProps {
  showDrawAnimation?: boolean;
}

export default function Header({ showDrawAnimation = false }: HeaderProps) {
  const [featuresOpen, setFeaturesOpen] = useState(false);
  const [resourcesOpen, setResourcesOpen] = useState(false);

  return (
    <motion.header 
      className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b shadow-sm px-6 py-3"
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ 
        type: "spring",
        stiffness: 300,
        damping: 30,
        duration: 0.6
      }}
    >
      <div className="flex justify-between items-center max-w-7xl mx-auto">
        {/* Animated Logo with QuantBoost Text */}
        <Link href="/" className="flex items-center gap-3 cursor-pointer">
          <motion.div
            variants={logoVariants}
            initial="idle"
            animate="idle"
            whileHover="hover"
          >
            <AnimatedQuantBoostLogo 
              size="2.5rem"
              showDrawAnimation={showDrawAnimation}
            />
          </motion.div>
          <span className="text-xl font-bold text-gray-900 tracking-tight">QuantBoost</span>
        </Link>
        
        {/* Enhanced Navigation - Centered */}
        <nav className="hidden md:flex gap-6 absolute left-1/2 transform -translate-x-1/2">
          {/* Features Dropdown */}
          <div 
            className="relative"
            onMouseEnter={() => setFeaturesOpen(true)}
            onMouseLeave={() => setFeaturesOpen(false)}
          >
            <motion.button 
              className="flex items-center gap-1 py-2 font-medium text-gray-700"
              variants={navItemVariants}
              initial="idle"
              whileHover="hover"
            >
              Features
              <motion.span
                animate={{ rotate: featuresOpen ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="text-xs"
              >
                ▾
              </motion.span>
            </motion.button>
            
            <AnimatePresence>
              {featuresOpen && (
                <motion.div
                  variants={dropdownVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  className="absolute left-0 mt-1 w-56 bg-white border border-gray-200 rounded-lg shadow-xl overflow-hidden z-50"
                >
                  <motion.a 
                    href="/features/excel-link" 
                    className="block px-4 py-3 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 transition-colors duration-150"
                    variants={dropdownItemVariants}
                    whileHover={{ x: 4 }}
                  >
                    <div className="font-medium">Excel Link</div>
                    <div className="text-xs text-gray-500">Connect Excel to PowerPoint</div>
                  </motion.a>
                  <motion.a 
                    href="/features/excel-trace" 
                    className="block px-4 py-3 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 transition-colors duration-150"
                    variants={dropdownItemVariants}
                    whileHover={{ x: 4 }}
                  >
                    <div className="font-medium">Excel Trace</div>
                    <div className="text-xs text-gray-500">Trace formula dependencies</div>
                  </motion.a>
                  <motion.a 
                    href="/features/worksheet-size-analyzer" 
                    className="block px-4 py-3 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 transition-colors duration-150"
                    variants={dropdownItemVariants}
                    whileHover={{ x: 4 }}
                  >
                    <div className="font-medium">Excel Size Analyzer</div>
                    <div className="text-xs text-gray-500">Optimize worksheet performance</div>
                  </motion.a>
                  <motion.a 
                    href="/features/slide-size-analyzer" 
                    className="block px-4 py-3 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 transition-colors duration-150"
                    variants={dropdownItemVariants}
                    whileHover={{ x: 4 }}
                  >
                    <div className="font-medium">PowerPoint Size Analyzer</div>
                    <div className="text-xs text-gray-500">Analyze presentation file size</div>
                  </motion.a>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          {/* Resources Dropdown */}
          <div 
            className="relative"
            onMouseEnter={() => setResourcesOpen(true)}
            onMouseLeave={() => setResourcesOpen(false)}
          >
            <motion.button 
              className="flex items-center gap-1 py-2 font-medium text-gray-700"
              variants={navItemVariants}
              initial="idle"
              whileHover="hover"
            >
              Resources
              <motion.span
                animate={{ rotate: resourcesOpen ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                className="text-xs"
              >
                ▾
              </motion.span>
            </motion.button>
            
            <AnimatePresence>
              {resourcesOpen && (
                <motion.div
                  variants={dropdownVariants}
                  initial="hidden"
                  animate="visible"
                  exit="hidden"
                  className="absolute left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-xl overflow-hidden z-50"
                >
                  <motion.a 
                    href="/download" 
                    className="block px-4 py-3 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 transition-colors duration-150"
                    variants={dropdownItemVariants}
                    whileHover={{ x: 4 }}
                  >
                    <div className="font-medium">Download</div>
                    <div className="text-xs text-gray-500">Get QuantBoost</div>
                  </motion.a>
                  <motion.a 
                    href="/help" 
                    className="block px-4 py-3 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 transition-colors duration-150"
                    variants={dropdownItemVariants}
                    whileHover={{ x: 4 }}
                  >
                    <div className="font-medium">Help Center</div>
                    <div className="text-xs text-gray-500">Documentation & support</div>
                  </motion.a>
                  <motion.a 
                    href="/training" 
                    className="block px-4 py-3 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 transition-colors duration-150"
                    variants={dropdownItemVariants}
                    whileHover={{ x: 4 }}
                  >
                    <div className="font-medium">Training</div>
                    <div className="text-xs text-gray-500">Learn best practices</div>
                  </motion.a>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          {/* Pricing Link */}
          <motion.a 
            href="/pricing" 
            className="py-2 font-medium text-gray-700"
            variants={navItemVariants}
            initial="idle"
            whileHover="hover"
          >
            Pricing
          </motion.a>
        </nav>
        
        {/* Enhanced Action Buttons */}
        <div className="flex gap-3">
          <motion.div
            variants={headerButtonVariants}
            initial="idle"
            whileHover="hover"
            whileTap="tap"
            className="px-6 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:border-emerald-300 hover:text-emerald-600 transition-colors duration-200 cursor-pointer"
          >
            <Link href="/auth/login" className="block w-full h-full">
              Log In
            </Link>
          </motion.div>
          
          <motion.div
            variants={primaryButtonVariants}
            initial="idle"
            whileHover="hover"
            whileTap="tap"
            className="px-6 py-2 rounded-lg bg-emerald-500 text-white font-medium hover:bg-emerald-600 transition-colors duration-200 cursor-pointer"
          >
            <a href="/start-trial" className="block w-full h-full">
              Free Trial
            </a>
          </motion.div>
        </div>
      </div>
    </motion.header>
  );
}