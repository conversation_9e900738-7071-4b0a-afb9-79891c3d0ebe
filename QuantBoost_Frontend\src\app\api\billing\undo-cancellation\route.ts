import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

export async function POST(request: NextRequest) {
  try {
    const { subscriptionId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    const key = (globalThis as any).process?.env?.STRIPE_SECRET_KEY as string | undefined;
    if (!key) {
      return NextResponse.json({ error: 'Stripe secret key not configured' }, { status: 500 });
    }
    const stripe = new Stripe(key, { apiVersion: '2025-03-31.basil' });

    // Undo cancellation by setting cancel_at_period_end to false
    const idempotencyKey = `undo-cancel-${subscriptionId}`;
    let updatedSubscription = await stripe.subscriptions.update(
      subscriptionId,
      { cancel_at_period_end: false },
      { idempotencyKey }
    );

    // Verify status reflects undo; if still marked to cancel, retry once
    if ((updatedSubscription as any).cancel_at_period_end) {
      updatedSubscription = await stripe.subscriptions.update(
        subscriptionId,
        { cancel_at_period_end: false },
        { idempotencyKey }
      );
    }

    return NextResponse.json({ 
      success: true, 
      subscription: {
        id: updatedSubscription.id,
        cancel_at_period_end: (updatedSubscription as any).cancel_at_period_end,
        current_period_end: (updatedSubscription as any).current_period_end,
        status: updatedSubscription.status
      }
    });

  } catch (error) {
    console.error('Error undoing subscription cancellation:', error);
    return NextResponse.json(
      { error: 'Failed to undo subscription cancellation' }, 
      { status: 500 }
    );
  }
}
