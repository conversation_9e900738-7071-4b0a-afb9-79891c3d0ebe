import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
});

export async function POST(request: NextRequest) {
  try {
    const { subscriptionId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    // Undo cancellation by setting cancel_at_period_end to false
    const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });

    return NextResponse.json({ 
      success: true, 
      subscription: {
        id: updatedSubscription.id,
        cancel_at_period_end: (updatedSubscription as any).cancel_at_period_end,
        current_period_end: (updatedSubscription as any).current_period_end,
        status: updatedSubscription.status
      }
    });

  } catch (error) {
    console.error('Error undoing subscription cancellation:', error);
    return NextResponse.json(
      { error: 'Failed to undo subscription cancellation' }, 
      { status: 500 }
    );
  }
}
