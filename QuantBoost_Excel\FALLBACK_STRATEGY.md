# Fallback Strategy for QAT Detection

## Current Situation
Based on the debug logs, Office does NOT change the Control.Id when buttons are added to QAT. This makes reliable detection challenging.

## Approach A: Conservative Icon Sizing
Use medium-sized icons that work reasonably well in both contexts:

```csharp
// Use 32x32 or 48x48 icons for all contexts
// These provide good quality in both ribbon and QAT
if (dpiScale >= 1.5f)
    resourceName = "QuantBoost_Excel.Resources.trace_48x48.png";
else
    resourceName = "QuantBoost_Excel.Resources.trace_32x32.png";
```

## Approach B: Request Pattern Detection
Enhance timing-based detection:

```csharp
// Track rapid successive requests (QAT behavior)
// Track initial vs subsequent requests
// Use machine learning-like pattern recognition
```

## Approach C: User Configuration
Add a user preference for icon sizing:

```csharp
// Registry setting: PreferSmallIcons (true/false)
// UI option in settings dialog
// Default to medium sizing
```

## Approach D: Separate QAT Buttons
Create dedicated QAT-optimized buttons with different IDs:

```xml
<!-- In ribbon XML -->
<button id="btnTracePrecedentsQAT" 
        label="Excel Trace (QAT)"
        getImage="GetExcelTraceImageSmall"
        size="normal"/>
```

## Recommendation
Given the Office behavior discovered, **Approach A (Conservative Sizing)** may be the most reliable immediate solution while we continue investigating other options.
