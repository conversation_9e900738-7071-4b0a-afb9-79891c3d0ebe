// --- START OF REVISED QuantBoostRibbon.cs ---

#region Using Directives

using System;
using System.IO;
using System.Reflection;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Linq;
using System.Collections.Generic;
using Office = Microsoft.Office.Core;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using Excel = Microsoft.Office.Interop.Excel; // Keep if ExcelLinkService uses it directly
using Microsoft.Office.Tools;
using stdole;

// Project specific namespaces
using QuantBoost_Licensing;
using QuantBoost_Shared.Security;
using QuantBoost_Shared.UI;
using QuantBoost_Shared.Utilities;
using QuantBoost_Powerpoint_Addin.ExcelLink; // Assuming ExcelLinkService is here
using QuantBoost_Powerpoint_Addin.Analysis;
// UI namespace is current, so no need for 'using QuantBoost_Powerpoint_Addin.UI;' here

#endregion

namespace QuantBoost_Powerpoint_Addin.UI
{
    [ComVisible(true)]
    public class QuantBoostRibbon : Office.IRibbonExtensibility
    {
        #region Fields
        private Office.IRibbonUI _ribbonUI;
        private static CustomTaskPane _analyzeTaskPane;
        private static QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.UI.WpfHostControl _analyzePaneControl; // Updated to use WPF Host Control
        private static CustomTaskPane _linkManagerTaskPane;
        private static QuantBoost_Powerpoint_Addin.Features.ExcelLink.UI.WpfHostControl _linkManagerPaneControl;
        private ExcelLinkService _excelLinkService;
        private ExcelLinkService ExcelLinkSvc
        {
            get
            {
                if (_excelLinkService == null)
                {
                    if (!EnsureThisAddInReady("Excel Link Service Access", false))
                    {
                        throw new InvalidOperationException("Add-in core not ready for Excel Link Service initialization.");
                    }
                    try
                    {
                        _excelLinkService = new ExcelLinkService(Globals.ThisAddIn.Application);
                        ErrorHandlingService.LogException(null, "ExcelLinkService lazy initialized successfully.");
                    }
                    catch (Exception ex)
                    {
                        ErrorHandlingService.LogException(ex, "Failed to lazy-initialize ExcelLinkService.");
                        throw new InvalidOperationException("Failed to initialize Excel Link Service.", ex);
                    }
                }
                return _excelLinkService;
            }
        }
        #endregion

        #region Constructor
        public QuantBoostRibbon()
        {
            ErrorHandlingService.LogException(null, "QuantBoostRibbon Constructor executed.");
        }
        #endregion

        #region IRibbonExtensibility Implementation
        public string GetCustomUI(string ribbonID)
        {
            if (ribbonID != "Microsoft.PowerPoint.Presentation") return null;
            string resourceName = "QuantBoost_Powerpoint_Addin.UI.QuantBoostRibbon.xml";
            ErrorHandlingService.LogException(null, $"GetCustomUI: Attempting to load Ribbon XML '{resourceName}'.");
            System.Diagnostics.Debug.WriteLine($"[DEBUG] GetCustomUI: Attempting to load Ribbon XML: '{resourceName}'");
            Stream stream = null;
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                // --- DEBUG CODE FOR RESOURCE NAMES ---
                // System.Diagnostics.Debug.WriteLine($"[DEBUG] Available Manifest Resources in assembly '{assembly.GetName().Name}':");
                // string[] allResources = assembly.GetManifestResourceNames();
                // if (!allResources.Any()) System.Diagnostics.Debug.WriteLine("  >> [CRITICAL ERROR] NO EMBEDDED RESOURCES FOUND!");
                // foreach (string resNameInAssembly in allResources) System.Diagnostics.Debug.WriteLine($"  - {resNameInAssembly}");
                // if (!allResources.Contains(resourceName)) System.Diagnostics.Debug.WriteLine($"  >> [MISMATCH INFO] '{resourceName}' NOT FOUND (case-sensitive).");
                // else System.Diagnostics.Debug.WriteLine($"  >> [MATCH INFO] '{resourceName}' WAS FOUND.");
                // --- END DEBUG CODE ---
                stream = assembly.GetManifestResourceStream(resourceName);
                if (stream == null)
                {
                    string errorMessage = $"Ribbon XML resource not found: '{resourceName}'. Check Build Action (Embedded Resource) and name.";
                    System.Diagnostics.Debug.WriteLine($"[ERROR] GetCustomUI: {errorMessage}");
                    var exToLog = new FileNotFoundException(errorMessage, resourceName); // Added resourceName to exception
                    ErrorHandlingService.LogException(exToLog, "GetCustomUI - Resource Stream Null");
                    throw exToLog;
                }
                using (StreamReader reader = new StreamReader(stream))
                {
                    stream = null; // Handed off to reader
                    string xmlContent = reader.ReadToEnd();
                    System.Diagnostics.Debug.WriteLine($"[SUCCESS] GetCustomUI: Loaded Ribbon XML: {resourceName}");
                    ErrorHandlingService.LogException(null, $"Successfully loaded Ribbon XML: {resourceName}");
                    return xmlContent;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[EXCEPTION] GetCustomUI: {ex.ToString()}");
                bool isOurFileNotFound = ex is FileNotFoundException && ex.Message.Contains("Ribbon XML resource not found");
                if (!isOurFileNotFound) ErrorHandlingService.HandleException(ex, $"Unexpected Fatal Error Loading Ribbon XML: {resourceName}");
                return null;
            }
            finally { stream?.Dispose(); }
        }

        public void OnLoad(Office.IRibbonUI ribbonUI)
        {
            ErrorHandlingService.LogException(null, "QuantBoostRibbon OnLoad executed.");
            _ribbonUI = ribbonUI;
            Task.Delay(500).ContinueWith(t => // Use t for task parameter
            {
                QuantBoost_Shared.Utilities.AsyncHelper.RunOnUIThread(() =>
                {
                    try
                    {
                        ErrorHandlingService.LogException(null, "Triggering initial license UI update from OnLoad delay.");
                        var licenseManager = Globals.ThisAddIn?._licensingManager;
                        LicenseDetails currentLicenseSDK = licenseManager?.CurrentLicense;
                        LicenseDetailsClient clientDetails = Globals.ThisAddIn.MapToClientDetails(currentLicenseSDK);
                        UpdateLicenseUI(clientDetails);
                    }
                    catch (Exception ex) { ErrorHandlingService.LogException(ex, "Error during delayed initial license UI update."); }
                });
            }, TaskScheduler.Default);
        }
        #endregion

        #region Ribbon Callbacks - Analyze Group
        public void OnAnalyzeClick(Office.IRibbonControl control)
        {
            try
            {
                ErrorHandlingService.LogException(null, "OnAnalyzeClick triggered.");
                if (!EnsureThisAddInReady("Analyze Pane")) return;
                if (_analyzeTaskPane == null)
                {
                    ErrorHandlingService.LogException(null, "Creating Analyze Task Pane...");
                    _analyzePaneControl = new QuantBoost_Powerpoint_Addin.Features.SizeAnalyzer.UI.WpfHostControl();
                    _analyzeTaskPane = Globals.ThisAddIn.CustomTaskPanes.Add(_analyzePaneControl, "QuantBoost Presentation Size Analyzer");
                    _analyzeTaskPane.DockPosition = Office.MsoCTPDockPosition.msoCTPDockPositionRight;
                    _analyzeTaskPane.Width = 800; // Wide enough to show all columns comfortably
                    _analyzeTaskPane.VisibleChanged += AnalyzeTaskPane_VisibleChanged;
                    ErrorHandlingService.LogException(null, "Analyze Task Pane created.");
                }
                _analyzeTaskPane.Visible = !_analyzeTaskPane.Visible;
                ErrorHandlingService.LogException(null, $"Analyze Task Pane visibility set to: {_analyzeTaskPane.Visible}");
            }
            catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error in Analyze button click"); }
        }
        #endregion

        #region Ribbon Callbacks - Licensing Group
        public void OnManageAccountClick(Office.IRibbonControl control)
        {
            try
            {
                ErrorHandlingService.LogException(null, "OnManageAccountClick triggered.");
                if (!EnsureThisAddInReady("Manage Account Dialog")) return;
                var licenseManager = Globals.ThisAddIn._licensingManager;
                if (licenseManager == null)
                {
                    ToastNotifier.ShowToast("Licensing service is unavailable.", 3000, Color.OrangeRed);
                    ErrorHandlingService.LogException(null, "Manage Account: Licensing Manager is null."); return;
                }
                LicenseDetails currentLicenseSDK = licenseManager.CurrentLicense;
                LicenseDetailsClient clientDetails = Globals.ThisAddIn.MapToClientDetails(currentLicenseSDK);
                string productId = ThisAddIn.PRODUCT_ID;
                string deviceId = Globals.ThisAddIn.GetOrCreateDeviceId();
                using (var dlg = new LicenseDialog(clientDetails, licenseManager as IQuantBoostLicensingManager, productId, deviceId))
                {
                    dlg.TriggerLicenseStatusUpdate = () => Globals.ThisAddIn.TriggerLicenseStatusUpdateForRibbon();
                    dlg.GetApiBaseUrl = () => Globals.ThisAddIn.GetApiBaseUrl();
                    dlg.ShowDialog();
                }
                InvalidateRibbonControl("btnManageAccount");
                InvalidateRibbonControl("btnLogout");
            }
            catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error showing Manage Account dialog"); }
        }

        public string GetManageAccountLabel(Office.IRibbonControl control)
        {
            if (!EnsureThisAddInReady("GetManageAccountLabel", false)) return "Manage Account (Error)";
            string token = TokenStorage.RetrieveRefreshToken();
            var currentLicense = Globals.ThisAddIn._licensingManager?.CurrentLicense;
            if (!string.IsNullOrEmpty(token) && currentLicense != null && !string.IsNullOrEmpty(currentLicense.Email))
                return $"Account: {currentLicense.Email.Split('@')[0]}";
            return "Login / Manage License";
        }

        public bool GetManageAccountVisible(Office.IRibbonControl control) => true;

        public bool GetLogoutButtonVisible(Office.IRibbonControl control)
        {
            if (!EnsureThisAddInReady("GetLogoutButtonVisible", false)) return false;
            return !string.IsNullOrEmpty(TokenStorage.RetrieveRefreshToken());
        }

                public async void OnLogoutClick(Office.IRibbonControl control)
        {
            try
            {
                ErrorHandlingService.LogException(null, "OnLogoutClick triggered.");
                if (!EnsureThisAddInReady("Logout")) return;

                // 1. Clear the locally stored refresh token. This is the most critical step.
                TokenStorage.ClearRefreshToken();
                ErrorHandlingService.LogException(null, "Local refresh token cleared.");

                // The server-side logout API call is removed. The client does not persist the short-lived
                // access token required by the server, so the call would always fail.
                // The user is effectively logged out on this client by clearing the local token.
                ToastNotifier.ShowToast("Logged out successfully.", 2000, Color.Green);

                var thisAddIn = Globals.ThisAddIn;
                if (thisAddIn._licensingManager != null)
                {
                    // 2. Clear the in-memory license details and refresh the status.
                    thisAddIn._licensingManager.ClearCurrentLicense();
                    await thisAddIn._licensingManager.RefreshLicenseStatusAsync();
                }

                // 3. Update the Ribbon UI to reflect the logged-out state.
                UpdateLicenseUI(null);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.HandleException(ex, "Error during logout");
                ToastNotifier.ShowToast("Error during logout.", 3000, Color.Red);
            }
        }
        #endregion

        #region Ribbon Callbacks - Excel Link Group
        public async void OnInsertExcelContentClick(Office.IRibbonControl control)
        {
            try
            {
                ErrorHandlingService.LogException(null, "OnInsertExcelContentClick triggered.");
                if (!EnsureThisAddInReady("Insert Excel Content") || !IsPremiumFeatureEnabled(control))
                { ToastNotifier.ShowToast("Premium license required for Excel Linking.", 3000, Color.OrangeRed); return; }

                var linkService = ExcelLinkSvc; string excelFilePath = null; string worksheetName = null;
                int targetSlideIndex = -1; ExcelLinkType selectionType = ExcelLinkType.Unknown;
                string sourceIdentifier = null; PowerPoint.Application pptApp = Globals.ThisAddIn.Application;
                PowerPoint.Slide activeSlide = null;
                try
                {
                    var pptWrapper = new PowerPointComWrapper(pptApp); activeSlide = pptWrapper.GetActiveSlide();
                    if (activeSlide == null) { ToastNotifier.ShowToast("Please select a slide.", 2000); return; }
                    targetSlideIndex = activeSlide.SlideIndex;
                }
                finally { PowerPointComWrapper.ReleaseComObject(activeSlide); }
                ErrorHandlingService.LogException(null, $"Target slide index: {targetSlideIndex}");

                ExcelComWrapper excelWrapper = null; object selection = null;
                Excel.Worksheet selectedWorksheet = null; Excel.Workbook selectedWorkbook = null;
                try
                {
                    excelWrapper = ExcelComWrapper.GetExistingInstance(); selection = excelWrapper.Application.Selection;
                    if (selection == null) { ToastNotifier.ShowToast("No selection in Excel.", 2500); return; }
                    if (selection is Excel.ChartObject chartObj) { try { selectionType = ExcelLinkType.Chart; selectedWorksheet = chartObj.Parent as Excel.Worksheet; if (selectedWorksheet == null) throw new InvalidOperationException("No parent worksheet for chart."); sourceIdentifier = chartObj.Name; } finally { ExcelComWrapper.ReleaseComObject(chartObj); } }
                    else if (selection is Excel.Range range) { try { selectionType = ExcelLinkType.Range; selectedWorksheet = range.Worksheet; if (selectedWorksheet == null) throw new InvalidOperationException("No worksheet for range."); sourceIdentifier = range.Address; } finally { ExcelComWrapper.ReleaseComObject(range); } }
                    else { ToastNotifier.ShowToast("Unsupported selection. Select Chart or Range.", 2500); return; }
                    ExcelComWrapper.ReleaseComObject(selection); selection = null;
                    selectedWorkbook = selectedWorksheet?.Parent as Excel.Workbook;
                    if (selectedWorkbook == null) throw new InvalidOperationException("No parent workbook.");
                    if (string.IsNullOrEmpty(sourceIdentifier)) throw new InvalidOperationException("No source identifier.");
                    if (string.IsNullOrEmpty(selectedWorkbook.FullName) || string.IsNullOrEmpty(selectedWorkbook.Path)) { MessageBox.Show("Save Excel workbook first.", "QuantBoost Excel Link", MessageBoxButtons.OK, MessageBoxIcon.Warning); return; }
                    excelFilePath = selectedWorkbook.FullName; worksheetName = selectedWorksheet.Name;
                    ErrorHandlingService.LogException(null, $"Excel source: Type={selectionType}, File='{excelFilePath}', Sheet='{worksheetName}', ID='{sourceIdentifier}'");
                }
                catch (ExcelInstanceNotFoundException ex) { ErrorHandlingService.LogException(ex, "Excel linking: Excel not found."); ToastNotifier.ShowToast(ex.Message, 3000, Color.OrangeRed); return; }
                catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error getting Excel selection"); return; }
                finally { ExcelComWrapper.ReleaseComObject(selectedWorksheet); ExcelComWrapper.ReleaseComObject(selectedWorkbook); ExcelComWrapper.ReleaseComObject(selection); excelWrapper?.Dispose(); }

                ToastNotifier.ShowToast($"Embedding selected {selectionType}...", 1500);
                ExcelLinkMetadata resultLink = null;
                try { resultLink = await linkService.EmbedExcelContentAsync(excelFilePath, worksheetName, selectionType, sourceIdentifier, targetSlideIndex).ConfigureAwait(false); }
                catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error calling EmbedExcelContentAsync"); }
                if (resultLink != null)
                {
                    ToastNotifier.ShowToast($"{selectionType} '{sourceIdentifier}' embedded.", 2000, Color.Green);
                    // Refresh Link Manager if visible - now thread-safe
                    if (_linkManagerTaskPane?.Visible == true)
                    {
                        _linkManagerPaneControl.RefreshLinks();
                    }
                }
                else { ToastNotifier.ShowToast($"Failed to embed {selectionType}. See logs.", 3000, Color.Red); }
            }
            catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error in OnInsertExcelContentClick"); }
        }

        public async void OnRefreshSelectedLinkClick(Office.IRibbonControl control)
        {
            try
            {
                ErrorHandlingService.LogException(null, "OnRefreshSelectedLinkClick triggered.");
                if (!EnsureThisAddInReady("Refresh Selected Link") || !IsPremiumFeatureEnabled(control)) { ToastNotifier.ShowToast("Premium license required.", 3000, Color.OrangeRed); return; }
                var linkService = ExcelLinkSvc; PowerPoint.Application pptApp = Globals.ThisAddIn.Application;
                string shapeId = null; ChartLink linkToRefresh = null; PowerPoint.Shape selectedShape = null;
                try
                {
                    var pptWrapper = new PowerPointComWrapper(pptApp); selectedShape = pptWrapper.GetSelectedShape();
                    if (selectedShape == null) { ToastNotifier.ShowToast("Select a linked shape.", 2000); return; }
                    shapeId = selectedShape.Name;
                }
                finally { PowerPointComWrapper.ReleaseComObject(selectedShape); }
                if (string.IsNullOrEmpty(shapeId)) { ToastNotifier.ShowToast("Could not get selected shape's name.", 2000, Color.Orange); return; }
                ErrorHandlingService.LogException(null, $"Attempting to refresh shape: {shapeId}");
                var allLinks = await linkService.GetAllLinksAsync().ConfigureAwait(false);
                linkToRefresh = allLinks?.FirstOrDefault(l => l.PowerPointShapeId == shapeId);
                if (linkToRefresh == null) { ToastNotifier.ShowToast("Selected shape not a QuantBoost link.", 2500); return; }
                string linkName = linkToRefresh.ChartNameOrId ?? linkToRefresh.SourceRange ?? linkToRefresh.LinkId;
                ToastNotifier.ShowToast($"Refreshing '{linkName}'...", 1500); bool success = false;
                try { await linkService.RefreshChartAsync(linkToRefresh).ConfigureAwait(false); success = true; }
                catch (Exception ex) { ErrorHandlingService.HandleException(ex, $"Error refreshing link '{linkName}'"); }
                if (success)
                {
                    ToastNotifier.ShowToast($"Link '{linkName}' refreshed.", 2000, Color.Green);
                    // Refresh Link Manager if visible - now thread-safe
                    if (_linkManagerTaskPane?.Visible == true)
                    {
                        _linkManagerPaneControl.RefreshLinks();
                    }
                }
            }
            catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error refreshing selected link"); }
        }

        public async void OnRefreshAllLinksClick(Office.IRibbonControl control)
        {
            try
            {
                ErrorHandlingService.LogException(null, "OnRefreshAllLinksClick triggered.");
                if (!EnsureThisAddInReady("Refresh All Links") || !IsPremiumFeatureEnabled(control)) { ToastNotifier.ShowToast("Premium license required.", 3000, Color.OrangeRed); return; }
                var linkService = ExcelLinkSvc; ToastNotifier.ShowToast("Starting bulk link refresh...", 1500);
                var progress = ToastNotifier.CreateProgressReporter("Bulk Link Refresh", showDetails: true);
                try { await linkService.RefreshAllChartsAsync(progress).ConfigureAwait(false); }
                catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error during bulk refresh"); }
                finally
                {
                    // Refresh Link Manager if visible - now thread-safe
                    if (_linkManagerTaskPane?.Visible == true)
                    {
                        _linkManagerPaneControl.RefreshLinks();
                    }
                }
            }
            catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error refreshing all links"); }
        }

        public void OnLinkManagerClick(Office.IRibbonControl control)
        {
            try
            {
                ErrorHandlingService.LogException(null, "OnLinkManagerClick triggered.");
                if (!EnsureThisAddInReady("Link Manager Pane") || !IsPremiumFeatureEnabled(control)) { ToastNotifier.ShowToast("Premium license required.", 3000, Color.OrangeRed); return; }
                if (_linkManagerTaskPane == null)
                {
                    ErrorHandlingService.LogException(null, "Creating Link Manager Task Pane...");
                    _linkManagerPaneControl = new QuantBoost_Powerpoint_Addin.Features.ExcelLink.UI.WpfHostControl();
                    _linkManagerTaskPane = Globals.ThisAddIn.CustomTaskPanes.Add(_linkManagerPaneControl, "QuantBoost Excel Link Manager");
                    _linkManagerTaskPane.DockPosition = Office.MsoCTPDockPosition.msoCTPDockPositionRight;
                    _linkManagerTaskPane.Width = 950; // Increased width to show all buttons properly
                    _linkManagerTaskPane.VisibleChanged += LinkManagerTaskPane_VisibleChanged;
                    ErrorHandlingService.LogException(null, "Link Manager Task Pane created.");
                }
                _linkManagerTaskPane.Visible = !_linkManagerTaskPane.Visible;
                ErrorHandlingService.LogException(null, $"Link Manager Task Pane visibility: {_linkManagerTaskPane.Visible}");
            }
            catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error opening Link Manager"); }
        }
        #endregion

        #region Task Pane Event Handlers
        private async void AnalyzeTaskPane_VisibleChanged(object sender, EventArgs e)
        {
            if (_analyzeTaskPane?.Visible == true && _analyzePaneControl != null)
            {
                try { await RefreshAnalyzePaneAsync(); } // Call the corrected method
                catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error handling Analyze Pane visibility change"); }
            }
        }

        private async void LinkManagerTaskPane_VisibleChanged(object sender, EventArgs e)
        {
            if (_linkManagerTaskPane?.Visible == true && _linkManagerPaneControl != null)
            {
                try { ErrorHandlingService.LogException(null, "LinkManagerTaskPane_VisibleChanged: Loading links."); _linkManagerPaneControl.LoadLinks(); }
                catch (Exception ex) { ErrorHandlingService.HandleException(ex, "Error handling Link Manager visibility change"); }
            }
        }

        // CORRECTED RefreshAnalyzePaneAsync
        private async Task RefreshAnalyzePaneAsync()
        {
            if (_analyzePaneControl == null) { ErrorHandlingService.LogException(null, "RefreshAnalyzePaneAsync: _analyzePaneControl is null."); return; }
            ErrorHandlingService.LogException(null, "Refreshing Analyze Pane content.");
            try
            {
                // Call the public method designed for this purpose.
                // It handles resetting the UI to the initial 'Idle' state.
                _analyzePaneControl.ClearResults();
            }
            catch (Exception ex)
            {
                // The primary error is already being logged, which is the crucial part.
                ErrorHandlingService.LogException(ex, "Error resetting Analyze Pane UI during refresh.");
            }
            await Task.CompletedTask; // Keep async signature if any awaitable operations are added later
        }
        #endregion

        #region Ribbon XML Callbacks - Getters
        public bool IsPremiumFeatureEnabled(Office.IRibbonControl control)
        {
            if (!EnsureThisAddInReady("IsPremiumFeatureEnabled", false)) return false;
            var licenseManager = Globals.ThisAddIn._licensingManager;
            if (licenseManager == null) { ErrorHandlingService.LogException(null, "IsPremiumFeatureEnabled: Licensing Manager null."); return false; }
            LicenseDetails currentLicenseSDK = licenseManager.CurrentLicense;
            LicenseDetailsClient clientDetails = Globals.ThisAddIn.MapToClientDetails(currentLicenseSDK);
            bool isActive = clientDetails != null && clientDetails.IsSubscriptionEffectivelyActive;
            if (!isActive) ErrorHandlingService.LogException(null, $"IsPremiumFeatureEnabled: Feature '{control.Id}' disabled. Status: {clientDetails?.LastKnownStatus}");
            return isActive;
        }
        #endregion

        /// <summary>
        /// Gets the current DPI scaling factor for the system.
        /// </summary>
        /// <returns>DPI scaling factor (1.0 = 100%, 1.25 = 125%, 1.5 = 150%, etc.)</returns>
        private float GetDpiScalingFactor()
        {
            try
            {
                // Use Windows Forms to get DPI information
                using (var graphics = System.Drawing.Graphics.FromHwnd(IntPtr.Zero))
                {
                    float dpiX = graphics.DpiX;
                    // Standard DPI is 96, so scaling factor is current DPI / 96
                    return dpiX / 96.0f;
                }
            }
            catch
            {
                // Fallback to standard DPI if detection fails
                return 1.0f;
            }
        }

        #region Icon Callbacks
        public stdole.IPictureDisp GetAnalyzeIcon(Office.IRibbonControl control)
        {
            return GetIconFromResourceEnhanced(control, "FileSizeAnalyzer", "png");
        }

        public stdole.IPictureDisp GetInsertExcelContentIcon(Office.IRibbonControl control)
        {
            return GetIconFromResourceEnhanced(control, "ExcelLink", "PNG");
        }

        public stdole.IPictureDisp GetRefreshSelectedLinkIcon(Office.IRibbonControl control)
        {
            return GetIconFromResourceEnhanced(control, "Excel_Link_Refresh");
        }

        public stdole.IPictureDisp GetRefreshAllLinksIcon(Office.IRibbonControl control)
        {
            return GetIconFromResourceEnhanced(control, "Excel_Link_RefreshAll");
        }

        public stdole.IPictureDisp GetLinkManagerIcon(Office.IRibbonControl control)
        {
            return GetIconFromResourceEnhanced(control, "link_manager");
        }

        public stdole.IPictureDisp GetManageAccountIcon(Office.IRibbonControl control)
        {
            return GetIconFromResourceEnhanced(control, "login");
        }

        public stdole.IPictureDisp GetLogoutIcon(Office.IRibbonControl control)
        {
            return GetIconFromResourceEnhanced(control, "logout");
        }

        /// <summary>
        /// Enhanced icon loading with QAT detection and DPI-aware scaling.
        /// </summary>
        /// <param name="control">The ribbon control requesting the image.</param>
        /// <param name="iconBaseName">Base name of the icon (e.g., "FileSizeAnalyzer")</param>
        /// <param name="fileExtension">File extension (e.g., "PNG" or "png")</param>
        /// <returns>IPictureDisp containing the optimal sized icon.</returns>
        private stdole.IPictureDisp GetIconFromResourceEnhanced(Office.IRibbonControl control, string iconBaseName, string fileExtension = "PNG")
        {
            try
            {
                float dpiScale = GetDpiScalingFactor();
                string resourceName;

                // Use medium-sized icons that work well in both contexts
                if (dpiScale >= 1.5f)
                    resourceName = $"QuantBoost_Powerpoint_Addin.Resources.{iconBaseName}_48x48.{fileExtension}";
                else if (dpiScale >= 1.25f)
                    resourceName = $"QuantBoost_Powerpoint_Addin.Resources.{iconBaseName}_40x40.{fileExtension}";
                else
                    resourceName = $"QuantBoost_Powerpoint_Addin.Resources.{iconBaseName}_32x32.{fileExtension}";

                // Try to load the selected resource
                var result = GetIconFromResource(resourceName);
                if (result != null)
                {
                    return result;
                }

                // Fallback to 32x32 if resource not found
                var fallbackResourceName = $"QuantBoost_Powerpoint_Addin.Resources.{iconBaseName}_32x32.{fileExtension}";
                return GetIconFromResource(fallbackResourceName);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"Error in enhanced icon loading for {iconBaseName}");
                return null;
            }
        }

        private stdole.IPictureDisp GetIconFromResource(string resourceName)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                using (Stream stream = assembly.GetManifestResourceStream(resourceName))
                {
                    if (stream == null)
                    {
                        return null;
                    }

                    using (System.Drawing.Bitmap bitmap = new System.Drawing.Bitmap(stream))
                    {
                        return ConvertBitmapToIPictureDisp(bitmap);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, $"Error loading icon resource: {resourceName}");
                return null;
            }
        }

        private stdole.IPictureDisp ConvertBitmapToIPictureDisp(System.Drawing.Bitmap bitmap)
        {
            try
            {
                // Use the standard COM interop method for converting Bitmap to IPictureDisp
                IntPtr hBitmap = bitmap.GetHbitmap();
                IntPtr hPalette = IntPtr.Zero;
                
                return CreateIPictureDispFromBitmap(hBitmap, hPalette);
            }
            catch (Exception ex)
            {
                ErrorHandlingService.LogException(ex, "Error converting bitmap to IPictureDisp");
                return null;
            }
        }

        [DllImport("oleaut32.dll")]
        private static extern int OleCreatePictureIndirect(PICTDESC pictdesc, ref Guid riid, bool fOwn, out stdole.IPictureDisp ppvObj);

        [StructLayout(LayoutKind.Sequential)]
        private struct PICTDESC
        {
            public int cbSizeofstruct;
            public int picType;
            public IntPtr hbitmap;
            public IntPtr hpal;
        }

        private stdole.IPictureDisp CreateIPictureDispFromBitmap(IntPtr hBitmap, IntPtr hPalette)
        {
            PICTDESC pictdesc = new PICTDESC();
            pictdesc.cbSizeofstruct = Marshal.SizeOf(pictdesc);
            pictdesc.picType = 1; // PICTYPE_BITMAP
            pictdesc.hbitmap = hBitmap;
            pictdesc.hpal = hPalette;

            Guid riid = typeof(stdole.IPictureDisp).GUID;
            stdole.IPictureDisp pictureDisp;
            OleCreatePictureIndirect(pictdesc, ref riid, true, out pictureDisp);
            return pictureDisp;
        }
        #endregion

        #region UI Update & Invalidation
        public void UpdateLicenseUI(LicenseDetailsClient license)
        {
            try
            {
                InvalidateRibbonControl("btnInsertExcelContent"); InvalidateRibbonControl("btnRefreshSelectedLink");
                InvalidateRibbonControl("btnRefreshAllLinks"); InvalidateRibbonControl("btnLinkManager");
                InvalidateRibbonControl("btnManageAccount"); InvalidateRibbonControl("btnLogout");
            }
            catch (Exception ex) { ErrorHandlingService.LogException(ex, "Error during Ribbon UI invalidation in UpdateLicenseUI."); }
        }

        public void InvalidateRibbonControl(string controlId)
        {
            if (_ribbonUI != null) { try { _ribbonUI.InvalidateControl(controlId); } catch (Exception ex) { ErrorHandlingService.LogException(ex, $"Error invalidating Ribbon control: {controlId}"); } }
        }
        #endregion

        #region Helper Methods
        private bool EnsureThisAddInReady(string operationName, bool showUserMessage = true)
        {
            if (Globals.ThisAddIn == null)
            {
                string errorMsg = $"Cannot perform '{operationName}': Add-in core not available.";
                ErrorHandlingService.LogException(new InvalidOperationException(errorMsg), "Add-in State Error");
                if (showUserMessage) QuantBoost_Shared.Utilities.AsyncHelper.RunOnUIThread(() => ToastNotifier.ShowToast(errorMsg, 3000, Color.Red));
                return false;
            }
            return true;
        }
        // ShowLicenseBanner and HideLicenseBanner can be removed if not used, or kept for future use.
        #endregion

        #region Link Manager Access for Automatic Cleanup

        /// <summary>
        /// Gets the Link Manager task pane for external access (used by automatic cleanup)
        /// </summary>
        /// <returns>The Link Manager CustomTaskPane, or null if not created</returns>
        public CustomTaskPane GetLinkManagerTaskPane()
        {
            return _linkManagerTaskPane;
        }

        /// <summary>
        /// Gets the Link Manager WPF host control for external access (used by automatic cleanup)
        /// </summary>
        /// <returns>The Link Manager WpfHostControl, or null if not created</returns>
        public QuantBoost_Powerpoint_Addin.Features.ExcelLink.UI.WpfHostControl GetLinkManagerControl()
        {
            return _linkManagerPaneControl;
        }

        #endregion
    }
}
// --- END OF REVISED QuantBoostRibbon.cs ---
