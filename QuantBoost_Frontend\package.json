{"name": "quantboostai", "version": "0.1.2", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.12", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/postcss": "^4.1.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jose": "^6.0.10", "lucide-react": "^0.487.0", "next": "15.2.4", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "stripe": "^18.0.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "typescript": "^5"}}