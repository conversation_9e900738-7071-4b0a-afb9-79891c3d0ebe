# `charge_receipts` Table:
- amount column is showing 60000 instead of 600 ($600) - confirm this is correct for int4 
- missing receipt_number (should show Receipt #1945-6546 for user `<EMAIL>`), however we have receipt_url which is working! so we simply and delete the receipt_number column
- missing subscription_id field. This should be fixed and changed to `stripe_subscription_id` instead. 

# `customer_events` Table:
- explain the purpose of this table - its currently blank what is this supposed to capture and do we need it? we don't really have upgrade or downgrade events since we just have one SKU (with different available quantities of licenses)

# `payment_events` Table:
- `stripe_subscription_id` column is missing data, however `subscriptions` table is populated correctly.  

# `Profiles` Table: 
- missing `first_name` and `last_name` column data, because we are not capturing first name and last name in the checkout flow anymore. we are currently using a Link checkout that doesn't need it, however I feel like for fraud prevention as well as customer CRM data we should capture this data.

